import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

class ImagePickerDialog extends StatefulWidget {
  final Function(File?) onImageSelected;

  const ImagePickerDialog({
    Key? key,
    required this.onImageSelected,
  }) : super(key: key);

  @override
  State<ImagePickerDialog> createState() => _ImagePickerDialogState();
}

class _ImagePickerDialogState extends State<ImagePickerDialog>
    with TickerProviderStateMixin {
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    setState(() => _isLoading = true);

    try {
      PermissionStatus permission;
      if (source == ImageSource.camera) {
        permission = await Permission.camera.request();
      } else {
        permission = await Permission.storage.request();
        if (permission.isDenied) {
          permission = await Permission.photos.request();
        }
        if (permission.isDenied) {
          permission = await Permission.mediaLibrary.request();
        }
      }

      if (permission.isDenied) {
        _showPermissionDialog();
        return;
      }

      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      _showErrorDialog('Lỗi khi chọn ảnh: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showPermissionDialog() {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final dialogWidth = min(screenSize.width * 0.85, 400.0);
    final padding = isSmallScreen ? 12.0 : 16.0;
    final titleFontSize = isSmallScreen ? 16.0 : 18.0;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(padding)),
        insetPadding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16.0 : 24.0),
        child: Container(
          width: dialogWidth,
          padding: EdgeInsets.all(padding),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryRed.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.security,
                      color: AppTheme.primaryRed,
                      size: isSmallScreen ? 18 : 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      localizations.translate('permission_required'),
                      style: AppTheme.headingSmall.copyWith(fontSize: titleFontSize),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                localizations.translate('camera_gallery_permission_message'),
                style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 14 : 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        foregroundColor: AppTheme.textSecondary,
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 12 : 16,
                          vertical: isSmallScreen ? 6 : 8,
                        ),
                      ),
                      child: Text(
                        localizations.translate('cancel'),
                        style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 14 : 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        openAppSettings();
                      },
                      style: AppTheme.primaryButtonStyle.copyWith(
                        padding: MaterialStateProperty.all(
                          EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 12 : 16,
                            vertical: isSmallScreen ? 6 : 8,
                          ),
                        ),
                      ),
                      child: Text(
                        localizations.translate('open_settings'),
                        style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 14 : 16),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showErrorDialog(String message) {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final dialogWidth = min(screenSize.width * 0.85, 400.0);
    final padding = isSmallScreen ? 12.0 : 16.0;
    final titleFontSize = isSmallScreen ? 16.0 : 18.0;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(padding)),
        insetPadding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16.0 : 24.0),
        child: Container(
          width: dialogWidth,
          padding: EdgeInsets.all(padding),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: isSmallScreen ? 18 : 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      localizations.translate('error'),
                      style: AppTheme.headingSmall.copyWith(fontSize: titleFontSize),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                message,
                style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 14 : 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: AppTheme.primaryButtonStyle.copyWith(
                  padding: MaterialStateProperty.all(
                    EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16 : 20,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                  ),
                ),
                child: Text(
                  localizations.translate('ok'),
                  style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 14 : 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagePreview() {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;
    final previewHeight = isSmallScreen ? 180.0 : (isLargeScreen ? 300.0 : 240.0);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: previewHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        gradient: AppTheme.primaryGradient.scale(0.1),
        border: Border.all(
          color: AppTheme.primaryRed.withOpacity(0.2),
          width: isSmallScreen ? 1.5 : 2,
        ),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 14),
            child: Image.file(
              _selectedImage!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            ),
          ),
          Positioned(
            top: isSmallScreen ? 6 : 8,
            right: isSmallScreen ? 6 : 8,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
              ),
              child: IconButton(
                onPressed: () => setState(() => _selectedImage = null),
                icon: Icon(
                  Icons.close,
                  color: Colors.white,
                  size: isSmallScreen ? 18 : 20,
                ),
                padding: EdgeInsets.all(isSmallScreen ? 3 : 4),
                constraints: BoxConstraints(
                  minWidth: isSmallScreen ? 28 : 32,
                  minHeight: isSmallScreen ? 28 : 32,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: isSmallScreen ? 6 : 8,
            left: isSmallScreen ? 6 : 8,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 6 : 8,
                vertical: isSmallScreen ? 3 : 4,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: isSmallScreen ? 14 : 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    localizations.translate('selected_image'),
                    style: AppTheme.captionText.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPickerButtons() {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;
    final buttonHeight = isSmallScreen ? 48.0 : (isLargeScreen ? 64.0 : 56.0);
    final iconSize = isSmallScreen ? 18.0 : 20.0;
    final spacing = isSmallScreen ? 8.0 : 12.0;

    return Column(
      children: [
        // Camera Button
        Container(
          width: double.infinity,
          height: buttonHeight,
          margin: EdgeInsets.only(bottom: spacing),
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : () => _pickImage(ImageSource.camera),
            icon: Container(
              padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.camera_alt_rounded,
                size: iconSize,
              ),
            ),
            label: Text(
              localizations.translate('camera'),
              style: AppTheme.buttonText.copyWith(
                fontSize: isSmallScreen ? 14 : (isLargeScreen ? 18 : 16),
              ),
            ),
            style: AppTheme.primaryButtonStyle.copyWith(
              backgroundColor: MaterialStateProperty.all(AppTheme.primaryRed),
              padding: MaterialStateProperty.all(
                EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 16 : 20,
                  vertical: isSmallScreen ? 12 : 16,
                ),
              ),
            ),
          ),
        ),

        // Gallery Button
        Container(
          width: double.infinity,
          height: buttonHeight,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : () => _pickImage(ImageSource.gallery),
            icon: Container(
              padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
              decoration: BoxDecoration(
                color: AppTheme.primaryRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.photo_library_rounded,
                size: iconSize,
                color: AppTheme.primaryRed,
              ),
            ),
            label: Text(
              localizations.translate('gallery'),
              style: AppTheme.buttonText.copyWith(
                color: AppTheme.primaryRed,
                fontSize: isSmallScreen ? 14 : (isLargeScreen ? 18 : 16),
              ),
            ),
            style: AppTheme.secondaryButtonStyle,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;
    final dialogWidth = min(screenSize.width * 0.85, 400.0);
    final padding = isSmallScreen ? 16.0 : (isLargeScreen ? 32.0 : 24.0);
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final bodyFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final buttonHeight = isSmallScreen ? 40.0 : (isLargeScreen ? 56.0 : 48.0);
    final spacing = isSmallScreen ? 12.0 : (isLargeScreen ? 28.0 : 20.0);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 16.0 : 24.0,
                vertical: isSmallScreen ? 16.0 : 24.0,
              ),
              child: Container(
                width: dialogWidth,
                decoration: AppTheme.elevatedCardDecoration,
                child: Padding(
                  padding: EdgeInsets.all(padding),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Container(
                        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient.scale(0.1),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                        ),
                        child: Column(
                          children: [
                            Container(
                              padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                              decoration: BoxDecoration(
                                gradient: AppTheme.primaryGradient,
                                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.primaryRed.withOpacity(0.3),
                                    blurRadius: isSmallScreen ? 8 : 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.camera_alt_rounded,
                                size: isSmallScreen ? 28 : 32,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              localizations.translate('certification_required'),
                              style: AppTheme.headingMedium.copyWith(fontSize: titleFontSize),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              localizations.translate('please_select_certification_image'),
                              style: AppTheme.bodyMedium.copyWith(fontSize: bodyFontSize),
                              textAlign: TextAlign.center,
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: isSmallScreen ? 16 : 24),

                      // Content
                      if (_selectedImage != null) ...[
                        _buildImagePreview(),
                        SizedBox(height: spacing),
                      ] else ...[
                        _buildPickerButtons(),
                        SizedBox(height: spacing),
                      ],

                      // Action buttons
                      LayoutBuilder(
                        builder: (context, constraints) {
                          final buttonWidth = constraints.maxWidth / 2 - (isSmallScreen ? 6 : 8);
                          return Row(
                            children: [
                              Expanded(
                                child: TextButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    widget.onImageSelected(null);
                                  },
                                  style: TextButton.styleFrom(
                                    foregroundColor: AppTheme.textSecondary,
                                    padding: EdgeInsets.symmetric(
                                      vertical: isSmallScreen ? 12 : 16,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                    ),
                                  ),
                                  child: Text(
                                    localizations.translate('cancel'),
                                    style: AppTheme.bodyLarge.copyWith(
                                      color: AppTheme.textSecondary,
                                      fontSize: isSmallScreen ? 14 : 16,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: isSmallScreen ? 12 : 16),
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: _selectedImage != null ? AppTheme.primaryGradient : null,
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                    boxShadow: _selectedImage != null
                                        ? [
                                      BoxShadow(
                                        color: AppTheme.primaryRed.withOpacity(0.3),
                                        blurRadius: isSmallScreen ? 6 : 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ]
                                        : null,
                                  ),
                                  child: ElevatedButton(
                                    onPressed: _selectedImage != null
                                        ? () {
                                      Navigator.pop(context);
                                      widget.onImageSelected(_selectedImage);
                                    }
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: _selectedImage != null
                                          ? Colors.transparent
                                          : AppTheme.borderColor,
                                      foregroundColor: _selectedImage != null
                                          ? Colors.white
                                          : AppTheme.textSecondary,
                                      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 12 : 16),
                                      elevation: 0,
                                      shadowColor: Colors.transparent,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                      ),
                                    ),
                                    child: Text(
                                      localizations.translate('confirm'),
                                      style: AppTheme.buttonText.copyWith(
                                        color: _selectedImage != null
                                            ? Colors.white
                                            : AppTheme.textSecondary,
                                        fontSize: isSmallScreen ? 14 : 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),

                      // Loading indicator
                      if (_isLoading) ...[
                        SizedBox(height: spacing),
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                          decoration: BoxDecoration(
                            color: AppTheme.backgroundColor,
                            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: isSmallScreen ? 18 : 20,
                                height: isSmallScreen ? 18 : 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: isSmallScreen ? 1.5 : 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppTheme.primaryRed,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                localizations.translate('processing'),
                                style: AppTheme.bodyMedium.copyWith(
                                  fontSize: isSmallScreen ? 14 : 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}