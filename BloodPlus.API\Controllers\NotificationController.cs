﻿using BloodPlus.ModelViews.NotificationModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        public NotificationController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<IActionResult> GetAllBlog(int pageNumber = 1, int pageSize = 5)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _notificationService.GetAllNotificationAsync(pageNumber, pageSize, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<NotificationModelView>> GetNotificationById(string id)
        {
            var result = await _notificationService.GetNotificationByIdAsync(id);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPost("send-all")]
        public async Task<ActionResult<NotificationModelView>> SendToAllUser(NotificationForUser model)
        {
            var result = await _notificationService.SendNotificationToAllUsersAsync(model);

            return Ok(new { Message = result });
        }
    }
}
