import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

class MapLocationPickerScreen extends StatefulWidget {
  @override
  State<MapLocationPickerScreen> createState() => _MapLocationPickerScreenState();
}

class _MapLocationPickerScreenState extends State<MapLocationPickerScreen> {
  LatLng? _pickedLocation;
  LatLng? _userLocation;
  String? _address;
  bool _isLoading = true;
  bool _isGettingAddress = false;
  final MapController _mapController = MapController();
  static const String _openCageApiKey = 'f0139ae6881b475d8bfacd801ec46d9b';
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initUserLocation();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initUserLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        await Geolocator.openLocationSettings();
        setState(() => _isLoading = false);
        return;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() => _isLoading = false);
          return;
        }
      }
      if (permission == LocationPermission.deniedForever) {
        setState(() => _isLoading = false);
        return;
      }
      final position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
      setState(() {
        _userLocation = LatLng(position.latitude, position.longitude);
        _pickedLocation = _userLocation;
        _isLoading = false;
      });
      _getAddress(_pickedLocation!);
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getAddress(LatLng latLng) async {
    setState(() => _isGettingAddress = true);
    final url = Uri.parse(
      'https://api.opencagedata.com/geocode/v1/json?q=${latLng.latitude}+${latLng.longitude}&key=$_openCageApiKey&language=vi&pretty=1',
    );
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['results'] != null && data['results'].isNotEmpty) {
          setState(() {
            _address = data['results'][0]['formatted'];
          });
        } else {
          setState(() {
            _address = null;
          });
        }
      } else {
        setState(() {
          _address = null;
        });
      }
    } catch (e) {
      setState(() {
        _address = null;
      });
    } finally {
      setState(() => _isGettingAddress = false);
    }
  }

  Future<void> _searchAddress() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;
    setState(() => _isSearching = true);
    final url = Uri.parse(
      'https://api.opencagedata.com/geocode/v1/json?q=${Uri.encodeComponent(query)}&key=$_openCageApiKey&language=vi&pretty=1',
    );
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['results'] != null && data['results'].isNotEmpty) {
          final lat = data['results'][0]['geometry']['lat'];
          final lng = data['results'][0]['geometry']['lng'];
          final latLng = LatLng(lat, lng);
          setState(() {
            _pickedLocation = latLng;
          });
          _mapController.move(latLng, 16.0);
          _getAddress(latLng);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Không tìm thấy địa chỉ.')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi tìm kiếm: ${response.statusCode}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi tìm kiếm: $e')),
      );
    } finally {
      setState(() => _isSearching = false);
    }
  }

  Future<void> _openInGoogleMaps() async {
    if (_pickedLocation == null) return;
    final url = 'https://www.google.com/maps/search/?api=1&query=${_pickedLocation!.latitude},${_pickedLocation!.longitude}';
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không thể mở Google Maps.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chọn vị trí trên bản đồ'),
        backgroundColor: Colors.red,
      ),
      body: _isLoading || _userLocation == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Tìm địa chỉ...'
                          ),
                          onSubmitted: (_) => _searchAddress(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _isSearching ? null : _searchAddress,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        child: _isSearching
                            ? const SizedBox(width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                            : const Icon(Icons.search, color: Colors.white),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.map, color: Colors.red),
                        tooltip: 'Mở trên Google Maps',
                        onPressed: _pickedLocation != null ? _openInGoogleMaps : null,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Stack(
                    children: [
                      FlutterMap(
                        mapController: _mapController,
                        options: MapOptions(
                          initialCenter: _pickedLocation!,
                          initialZoom: 15.0,
                          onTap: (tapPos, latlng) {
                            setState(() {
                              _pickedLocation = latlng;
                              _address = null;
                            });
                            _getAddress(latlng);
                          },
                        ),
                        children: [
                          TileLayer(
                            urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                            subdomains: ['a', 'b', 'c'],
                            userAgentPackageName: 'bloodplusmobile.production',
                          ),
                          if (_pickedLocation != null)
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: _pickedLocation!,
                                  width: 40,
                                  height: 40,
                                  child: const Icon(Icons.location_on, color: Colors.red, size: 40),
                                ),
                              ],
                            ),
                        ],
                      ),
                      Positioned(
                        left: 16,
                        right: 16,
                        bottom: 32,
                        child: Card(
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                          elevation: 8,
                          color: Colors.white,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _isGettingAddress
                                    ? const CircularProgressIndicator()
                                    : Text(
                                        _address ?? 'Chạm vào bản đồ để chọn vị trí',
                                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                        textAlign: TextAlign.center,
                                      ),
                                const SizedBox(height: 12),
                                ElevatedButton.icon(
                                  onPressed: _address != null
                                      ? () {
                                          Navigator.pop(context, _address);
                                        }
                                      : null,
                                  icon: const Icon(Icons.check),
                                  label: const Text('Chọn vị trí này'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
} 