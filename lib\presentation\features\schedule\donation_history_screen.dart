import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:bloodplusmobile/data/models/Enum/appointment_status.dart';
import 'package:bloodplusmobile/data/models/appointment_model.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/helpers/blood_component_helper.dart';
import 'package:flutter/scheduler.dart';
// RouteObserver để theo dõi navigation
final RouteObserver<ModalRoute> routeObserver = RouteObserver<ModalRoute>();

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> with TickerProviderStateMixin, RouteAware {
  late AnimationController _fadeController;
  late AnimationController _headerController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _headerScaleAnimation;
  late Animation<double> _pulseAnimation;

  String _selectedFilter = 'all';
  final ScrollController _scrollController = ScrollController();
  bool _showFloatingButton = false;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    Provider.of<AppStateNotifier>(context, listen: false).fetchAppointments();
    _scrollController.addListener(_onScroll);
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOutBack));

    _headerScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.elasticOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
    _headerController.forward();
  }

  void _onScroll() {
    if (_scrollController.offset > 100 && !_showFloatingButton) {
      setState(() => _showFloatingButton = true);
    } else if (_scrollController.offset <= 100 && _showFloatingButton) {
      setState(() => _showFloatingButton = false);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Đăng ký RouteAware để biết khi quay lại màn hình
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _headerController.dispose();
    _pulseController.dispose();
    _scrollController.dispose();
    // Hủy đăng ký RouteAware
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    // Khi quay lại màn hình này, tự động reload dữ liệu
    _refreshAppointments();
  }

  Future<void> _refreshAppointments() async {
    setState(() => _isRefreshing = true);
    await Provider.of<AppStateNotifier>(context, listen: false).fetchAppointments();
    setState(() => _isRefreshing = false);
  }

  void _showErrorDialog(String error) {
    DialogHelper.showAnimatedSuccessDialog(
      context: context,
      title: AppLocalizations.of(context).translate('error'),
      message: '${AppLocalizations.of(context).translate('error_loading_appointments')}: $error',
      buttonText: AppLocalizations.of(context).translate('ok'),
      icon: Icons.error_outline_rounded,
      iconColor: AppColors.primaryRed,
    );
  }

  List<Appointment> get _filteredAppointments {
    final appointments = Provider.of<AppStateNotifier>(context).appointments;
    switch (_selectedFilter) {
      case 'upcoming':
        return appointments.where((apt) => apt.status == AppointmentStatus.pending).toList();
      case 'completed':
        return appointments.where((apt) => apt.status == AppointmentStatus.completed).toList();
      case 'cancelled':
        return appointments.where((apt) => apt.status == AppointmentStatus.cancelled).toList();
      default:
        return appointments;
    }
  }

  Widget _buildModernAppBar(bool isSmallScreen) {
    return SliverAppBar(
      expandedHeight: isSmallScreen ? 120 : 160,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primaryRed,
              AppColors.primaryRed.withOpacity(0.8),
              Colors.pink.shade400,
            ],
            stops: const [0.0, 0.7, 1.0],
          ),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(isSmallScreen ? 24 : 32),
            bottomRight: Radius.circular(isSmallScreen ? 24 : 32),
          ),
        ),
        child: FlexibleSpaceBar(
          title: ScaleTransition(
            scale: _headerScaleAnimation,
            child: Text(
              AppLocalizations.of(context).translate('history'),
              style: AppTheme.headingLarge.copyWith(
                fontSize: isSmallScreen ? 18 : 24,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    offset: const Offset(0, 2),
                    blurRadius: isSmallScreen ? 2 : 4,
                    color: Colors.black.withOpacity(0.3),
                  ),
                ],
              ),
            ),
          ),
          centerTitle: true,
          background: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryRed,
                  Colors.pink.shade400,
                ],
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(isSmallScreen ? 24 : 32),
                bottomRight: Radius.circular(isSmallScreen ? 24 : 32),
              ),
            ),
            child: Stack(
              children: [
                ...List.generate(6, (index) => _buildFloatingParticle(index, isSmallScreen)),
                Positioned(
                  bottom: isSmallScreen ? 40 : 60,
                  left: isSmallScreen ? 10 : 20,
                  right: isSmallScreen ? 10 : 20,
                  child: Consumer<AppStateNotifier>(
                    builder: (context, appState, child) => _buildQuickStats(isSmallScreen),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      leading: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 6 : 8.0),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              color: Colors.white.withOpacity(0.2),
              child: IconButton(
                icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
                onPressed: () {
                  NavigationService.navigateToHome();
                },
              ),
            ),
          ),
        ),
      ),
      actions: [
        Container(
          margin: EdgeInsets.all(isSmallScreen ? 6 : 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
          ),
          child: IconButton(
            icon: _isRefreshing
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.refresh_rounded, color: Colors.white),
            onPressed: _isRefreshing ? null : _refreshAppointments,
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingParticle(int index, bool isSmallScreen) {
    final random = (index * 47) % 100;
    return Positioned(
      left: (random * 3.0) % (isSmallScreen ? 200 : 300),
      top: (random * 2.0) % (isSmallScreen ? 80 : 100) + 40,
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.5 + (_pulseAnimation.value * 0.3),
            child: Container(
              width: (isSmallScreen ? 6 : 8) + (random % 4),
              height: (isSmallScreen ? 6 : 8) + (random % 4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.3 + (random % 30) / 100),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.2),
                    blurRadius: isSmallScreen ? 2 : 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickStats(bool isSmallScreen) {
    final appState = Provider.of<AppStateNotifier>(context);
    final totalAppointments = appState.appointments.length;
    final upcomingCount = appState.appointments.where((apt) {
      final isPending = apt.status == AppointmentStatus.pending;
      return isPending;
    }).length;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatCard(
            icon: Icons.event_available_rounded,
            count: upcomingCount.toString(),
            label: AppLocalizations.of(context).translate('upcoming'),
            isSmallScreen: isSmallScreen,
          ),
          _buildStatCard(
            icon: Icons.favorite_rounded,
            count: totalAppointments.toString(),
            label: AppLocalizations.of(context).translate('total'),
            isSmallScreen: isSmallScreen,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String count,
    required String label,
    required bool isSmallScreen,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16, vertical: isSmallScreen ? 8 : 12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: Colors.white, size: isSmallScreen ? 18 : 24),
              const SizedBox(height: 4),
              Text(
                count,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: isSmallScreen ? 14 : 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: isSmallScreen ? 10 : 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChips(bool isSmallScreen) {
    final filters = [
      {'key': 'all', 'label': 'all_appointments', 'icon': Icons.list_rounded},
      {'key': 'upcoming', 'label': 'upcoming', 'icon': Icons.schedule_rounded},
      {'key': 'completed', 'label': 'completed', 'icon': Icons.check_circle_rounded},
      {'key': 'cancelled', 'label': 'cancelled', 'icon': Icons.cancel_rounded},
    ];

    return SliverToBoxAdapter(
      child: Container(
        height: isSmallScreen ? 50 : 60,
        margin: EdgeInsets.symmetric(vertical: isSmallScreen ? 12 : 16),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
          itemCount: filters.length,
          itemBuilder: (context, index) {
            final filter = filters[index];
            final isSelected = _selectedFilter == filter['key'];

            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: EdgeInsets.only(right: isSmallScreen ? 8 : 12),
              child: FilterChip(
                selected: isSelected,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      filter['icon'] as IconData,
                      size: isSmallScreen ? 14 : 18,
                      color: isSelected ? Colors.white : AppColors.primaryRed,
                    ),
                    SizedBox(width: isSmallScreen ? 6 : 8),
                    Text(
                      AppLocalizations.of(context).translate(filter['label'] as String),
                      style: TextStyle(
                        color: isSelected ? Colors.white : AppColors.primaryRed,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ],
                ),
                onSelected: (selected) {
                  setState(() => _selectedFilter = filter['key'] as String);
                },
                backgroundColor: Colors.white,
                selectedColor: AppColors.primaryRed,
                checkmarkColor: Colors.white,
                elevation: isSelected ? 4 : 2,
                shadowColor: AppColors.primaryRed.withOpacity(0.3),
                side: BorderSide(
                  color: isSelected ? AppColors.primaryRed : AppColors.primaryRed.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEnhancedAppointmentCard(Appointment appointment, int index, bool isSmallScreen) {
    final isPast = DateTime.parse(appointment.appointmentDate).isBefore(DateTime.now());
    final localizations = AppLocalizations.of(context);

    final cardSlideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Interval(0.1 * index, 0.1 * index + 0.6, curve: Curves.easeOutBack),
      ),
    );

    return SlideTransition(
      position: cardSlideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 20),
          child: Material(
            elevation: isSmallScreen ? 6 : 8,
            borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
            shadowColor: AppColors.primaryRed.withOpacity(0.2),
            child: InkWell(
              borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
              onTap: () => _navigateToDetail(appointment),
              child: Container(
                padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                  ),
                  border: Border.all(
                    color: isPast ? Colors.grey.withOpacity(0.3) : AppColors.primaryRed.withOpacity(0.2),
                    width: 1.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (appointment.isEmergency == true) EmergencyNotice(isSmallScreen: isSmallScreen),
                    _buildCardHeader(appointment, isPast, isSmallScreen),
                    SizedBox(height: isSmallScreen ? 8 : 16),
                    _buildCardDetails(appointment, localizations, isSmallScreen),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader(Appointment appointment, bool isPast, bool isSmallScreen) {
    return Row(
      children: [
        Hero(
          tag: 'appointment_${appointment.id}',
          child: Container(
            width: isSmallScreen ? 40 : 60,
            height: isSmallScreen ? 40 : 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: isPast
                    ? [Colors.grey.shade400, Colors.grey.shade300]
                    : [AppColors.primaryRed, Colors.pink.shade400],
              ),
              boxShadow: [
                BoxShadow(
                  color: (isPast ? Colors.grey : AppColors.primaryRed).withOpacity(0.3),
                  blurRadius: isSmallScreen ? 4 : 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              Icons.favorite_rounded,
              color: Colors.white,
              size: isSmallScreen ? 18 : 28,
            ),
          ),
        ),
        SizedBox(width: isSmallScreen ? 8 : 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                appointment.eventName,
                style: AppTheme.headingSmall.copyWith(
                  fontSize: isSmallScreen ? 14 : 16,
                  color: isPast ? Colors.grey.shade600 : Colors.black87,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isSmallScreen ? 2 : 4),
              _buildStatusChip(appointment, isSmallScreen),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(Appointment appointment, bool isSmallScreen) {
    Color chipColor;
    IconData chipIcon;

    switch (appointment.status) {
      case AppointmentStatus.completed:
        chipColor = Colors.green;
        chipIcon = Icons.check_circle_rounded;
        break;
      case AppointmentStatus.cancelled:
        chipColor = Colors.red;
        chipIcon = Icons.cancel_rounded;
        break;
      case AppointmentStatus.pending:
        chipColor = Colors.orange;
        chipIcon = Icons.schedule_rounded;
        break;
      case AppointmentStatus.needConfirm:
        chipColor = Colors.blue;
        chipIcon = Icons.help_rounded;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 6 : 8, vertical: isSmallScreen ? 2 : 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(chipIcon, size: isSmallScreen ? 12 : 14, color: chipColor),
          SizedBox(width: isSmallScreen ? 2 : 4),
          Text(
            appointment.getStatusText(context),
            style: TextStyle(
              color: chipColor,
              fontSize: isSmallScreen ? 10 : 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardDetails(Appointment appointment, AppLocalizations localizations, bool isSmallScreen) {
    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.calendar_today_rounded,
          label: localizations.translate('date'),
          value: appointment.getFormattedDate(),
          iconColor: Colors.blue,
          isSmallScreen: isSmallScreen,
        ),
        _buildDetailRow(
          icon: Icons.access_time_rounded,
          label: localizations.translate('time'),
          value: appointment.getFormattedTime(),
          iconColor: Colors.green,
          isSmallScreen: isSmallScreen,
        ),
        _buildDetailRow(
          icon: Icons.location_on_rounded,
          label: localizations.translate('location'),
          value: appointment.location,
          iconColor: Colors.orange,
          isSmallScreen: isSmallScreen,
        ),
        _buildDetailRow(
          icon: Icons.business_rounded,
          label: localizations.translate('organization'),
          value: appointment.organizationName,
          iconColor: Colors.purple,
          isSmallScreen: isSmallScreen,
        ),
        _buildDetailRow(
          icon: Icons.bloodtype_rounded,
          label: localizations.translate('blood_component_history'),
          value: getTranslatedBloodComponent(localizations, appointment.bloodComponent),
          iconColor: AppColors.primaryRed,
          isSmallScreen: isSmallScreen,
        ),
        if (appointment.certification != null && appointment.certification!.isNotEmpty)
          _buildDetailRow(
            icon: Icons.verified_rounded,
            label: localizations.translate('certification'),
            value: localizations.translate('available'),
            iconColor: Colors.green,
            isSmallScreen: isSmallScreen,
          ),
      ],
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
    required Color iconColor,
    required bool isSmallScreen,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 4 : 6),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
            ),
            child: Icon(icon, size: isSmallScreen ? 14 : 16, color: iconColor),
          ),
          SizedBox(width: isSmallScreen ? 8 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 10 : 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToDetail(Appointment appointment) {
    NavigationService.navigateTo(
      AppRoutes.historyDetail,
      arguments: {
        'appointment': appointment,
        'onCancel': () {
          // Refresh appointments sau khi cancel
          Provider.of<AppStateNotifier>(context, listen: false).fetchAppointments();
        },
        'onComplete': () {
          // Refresh appointments sau khi complete
          Provider.of<AppStateNotifier>(context, listen: false).fetchAppointments();
        },
      },
    );
  }

  Widget _buildEmptyState(bool isSmallScreen) {
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    padding: EdgeInsets.all(isSmallScreen ? 24 : 32),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryRed.withOpacity(0.1),
                          AppColors.primaryRed.withOpacity(0.05),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryRed.withOpacity(0.1),
                          blurRadius: isSmallScreen ? 15 : 20,
                          spreadRadius: isSmallScreen ? 3 : 5,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.event_busy_rounded,
                      color: AppColors.primaryRed,
                      size: isSmallScreen ? 60 : 80,
                    ),
                  ),
                );
              },
            ),
            SizedBox(height: isSmallScreen ? 16 : 32),
            Text(
              AppLocalizations.of(context).translate('no_appointments'),
              style: AppTheme.headingMedium.copyWith(fontSize: isSmallScreen ? 16 : 20),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isSmallScreen ? 8 : 16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 32),
              child: Text(
                AppLocalizations.of(context).translate('no_appointments_message'),
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: isSmallScreen ? 12 : 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: isSmallScreen ? 16 : 32),
            CustomButton(
              text: AppLocalizations.of(context).translate('book_now'),
              color: AppColors.primaryRed,
              icon: Icons.add_rounded,
              onPressed: () {
                NavigationService.navigateTo(AppRoutes.donationEvent);
              },
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 24 : 32, vertical: isSmallScreen ? 12 : 16),
              borderRadius: isSmallScreen ? 12 : 16,
              width: isSmallScreen ? 160 : 200,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(bool isSmallScreen) {
    return SliverFillRemaining(
      child: Center(
        child: AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                padding: EdgeInsets.all(isSmallScreen ? 18 : 24),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryRed.withOpacity(0.2),
                      AppColors.primaryRed.withOpacity(0.05),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryRed.withOpacity(0.2),
                      blurRadius: isSmallScreen ? 15 : 20,
                      spreadRadius: isSmallScreen ? 3 : 5,
                    ),
                  ],
                ),
                child: const CircularProgressIndicator(
                  color: AppColors.primaryRed,
                  strokeWidth: 3,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateNotifier>(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          _buildModernAppBar(isSmallScreen),
          _buildFilterChips(isSmallScreen),
          if (appState.isLoading)
            _buildLoadingState(isSmallScreen)
          else if (_filteredAppointments.isEmpty)
            _buildEmptyState(isSmallScreen)
          else
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                      (context, index) => _buildEnhancedAppointmentCard(
                    _filteredAppointments[index],
                    index,
                    isSmallScreen,
                  ),
                  childCount: _filteredAppointments.length,
                ),
              ),
            ),
          SliverPadding(padding: EdgeInsets.only(bottom: isSmallScreen ? 80 : 100)),
        ],
      ),
      floatingActionButton: AnimatedSlide(
        offset: _showFloatingButton ? Offset.zero : const Offset(0, 2),
        duration: const Duration(milliseconds: 300),
        child: AnimatedOpacity(
          opacity: _showFloatingButton ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: FloatingActionButton(
            onPressed: () {
              _scrollController.animateTo(
                0,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOutCubic,
              );
            },
            backgroundColor: AppColors.primaryRed,
            child: Icon(Icons.keyboard_arrow_up_rounded, color: Colors.white, size: isSmallScreen ? 24 : 32),
          ),
        ),
      ),
    );
  }
}

class EmergencyNotice extends StatelessWidget {
  final bool isSmallScreen;
  const EmergencyNotice({Key? key, required this.isSmallScreen}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 10 : 16, vertical: isSmallScreen ? 8 : 12),
      decoration: BoxDecoration(
        color: AppColors.primaryRed.withOpacity(0.08),
        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 14),
        border: Border.all(color: AppColors.primaryRed.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.warning_amber_rounded, color: AppColors.primaryRed, size: isSmallScreen ? 18 : 22),
          SizedBox(width: isSmallScreen ? 8 : 12),
          Expanded(
            child: Text(
              localizations.locale.languageCode == 'vi'
                  ? 'Lưu ý: Đây là lịch hiến máu khẩn cấp. Vui lòng chú ý liên hệ, có thể với đơn vị tổ chức nếu cần thiết!'
                  : 'Note: This is an emergency blood donation schedule. Please pay attention to contact, possibly with the organizing unit if necessary!',
              style: TextStyle(
                color: AppColors.primaryRed,
                fontWeight: FontWeight.w600,
                fontSize: isSmallScreen ? 11 : 13,
              ),
            ),
          ),
        ],
      ),
    );
  }
}