﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.ModelViews.BlogModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class BlogService : IBlogService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly FirebaseService _firebaseService;

        public BlogService(IUnitOfWork unitOfWork, IMapper mapper, FirebaseService firebaseService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _firebaseService = firebaseService;
        }

        public async Task<string> CreateBlogAsync(CreateBlogModelView model, string userId)
        {
            try
            {
                Blog blog = _mapper.Map<Blog>(model);
                blog.UserId = Guid.Parse(userId);
                blog.ViewNumber = 0;

                // Khởi tạo các task upload ảnh song song, dùng FirebaseService UploadImageAsync
                var uploadTasks = new Task<string>[]
                {
                    model.Image1 != null ? _firebaseService.UploadImageAsync(model.Image1) : Task.FromResult<string>(null),
                    model.Image2 != null ? _firebaseService.UploadImageAsync(model.Image2) : Task.FromResult<string>(null),
                    model.Image3 != null ? _firebaseService.UploadImageAsync(model.Image3) : Task.FromResult<string>(null),
                    model.Image4 != null ? _firebaseService.UploadImageAsync(model.Image4) : Task.FromResult<string>(null),
                };

                var photoUrls = await Task.WhenAll(uploadTasks);

                blog.Image1 = photoUrls[0];
                blog.Image2 = photoUrls[1];
                blog.Image3 = photoUrls[2];
                blog.Image4 = photoUrls[3];

                blog.CreatedTime = DateTime.Now;
                blog.CreatedBy = userId;

                await _unitOfWork.GetRepository<Blog>().InsertAsync(blog);
                await _unitOfWork.SaveAsync();

                return "Blog added successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }




        public async Task<string> DeleteBlogAsync(string id, string userId)
        {
            try
            {
                var blog = await _unitOfWork.GetRepository<Blog>().GetByIdAsync(id);
                if (blog == null || blog.DeletedTime.HasValue)
                {
                    throw new Exception("can not find Blog or deleted");
                }

                blog.DeletedTime = DateTimeOffset.UtcNow;
                blog.DeletedBy = userId;

                await _unitOfWork.GetRepository<Blog>().UpdateAsync(blog);
                await _unitOfWork.SaveAsync();

                return "Blog delete successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BlogModelView> GetBlogByIdAsync(string id)
        {
            try
            {
                var blog = await _unitOfWork.GetRepository<Blog>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == id && !u.DeletedTime.HasValue);

                if (blog == null)
                {
                    throw new Exception("can not find or Blog is deleted");
                }

                blog.ViewNumber++;

                await _unitOfWork.GetRepository<Blog>().UpdateAsync(blog);
                await _unitOfWork.SaveAsync();

                var blogModelView = _mapper.Map<BlogModelView>(blog);

                return blogModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BasePaginatedList<ListBlogModelView>> GetAllBlogAsync(int pageNumber, int pageSize, string? title)
        {
            IQueryable<Blog> blogQuery = _unitOfWork.GetRepository<Blog>()
                .Entities
                .Where(p => !p.DeletedTime.HasValue)
                .OrderByDescending(s => s.CreatedTime);

            if (!string.IsNullOrWhiteSpace(title))
            {
                blogQuery = blogQuery.Where(p => p.Title.Contains(title));
            }

            int totalCount = await blogQuery.CountAsync();

            List<Blog> paginatedServices = await blogQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            List<ListBlogModelView> blogModelView = _mapper.Map<List<ListBlogModelView>>(paginatedServices);

            return new BasePaginatedList<ListBlogModelView>(blogModelView, totalCount, pageNumber, pageSize);
        }


        public async Task<string> UpdateBlogAsync(string id, CreateBlogModelView model, string userId)
        {
            try
            {
                var blog = await _unitOfWork.GetRepository<Blog>().GetByIdAsync(id);
                if (blog == null || blog.DeletedTime.HasValue)
                {
                    throw new Exception("can not find or Blog is deleted");
                }

                _mapper.Map(model, blog);
                blog.LastUpdatedBy = userId;
                blog.LastUpdatedTime = DateTimeOffset.UtcNow;

                await _unitOfWork.GetRepository<Blog>().UpdateAsync(blog);
                await _unitOfWork.SaveAsync();

                return "Blog updated successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }
    }
}
