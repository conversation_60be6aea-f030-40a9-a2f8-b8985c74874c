﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.UserModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IPasswordHasher<User> _passwordHasher;
        private readonly SendMailService _sendMailService;
        private readonly FirebaseService _firebaseService;

        public UserService(IUnitOfWork unitOfWork, IMapper mapper, IPasswordHasher<User> passwordHasher, 
            SendMailService sendMailService, FirebaseService firebaseService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _passwordHasher = passwordHasher;   
            _sendMailService = sendMailService;
            _firebaseService = firebaseService;
        }

        public async Task<string> CreateManagerAccountAsync(CreateManagerAccountModel model)
        {
            try
            {
                var existingUser = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .AnyAsync(u => (u.Email == model.Email || u.PhoneNumber == model.PhoneNumber || u.UserName == model.UserName) 
                        && u.EmailConfirmed == true && !u.DeletedTime.HasValue);
                if (existingUser == true)
                {
                    throw new Exception("Email or PhoneNumber or UserName is in used");
                }
                User user = new()
                {
                    Id = Guid.NewGuid(),
                    UserName = model.UserName,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber ?? "N/A",
                    Name = model.Name,
                    DateOfBirth = model.DateOfBirth,
                    Address = model.Address ?? "N/A",
                    Point = 0,
                    DonationCount = 0,
                    Job = model.Job ?? "N/A",
                    Gender = model.Gender,
                    PassportNumber = "N/A",
                    SecurityStamp = Guid.NewGuid().ToString(),
                    CreatedTime = DateTime.UtcNow,
                    EmailConfirmed = true,
                };
                if (model.UserImage != null)
                {
                    user.UserImage = await _firebaseService.UploadImageAsync(model.UserImage);
                }
                else
                {
                    user.UserImage = "N/A";
                }

                user.PasswordHash = _passwordHasher.HashPassword(user, model.Password);
                user.CreatedBy = user.Id.ToString();

                await _unitOfWork.GetRepository<User>().InsertAsync(user);

                //cap role 
                var role = await _unitOfWork.GetRepository<Role>()
                    .Entities
                    .Where(r => r.Name == "Manager" && !r.DeletedTime.HasValue)
                    .FirstOrDefaultAsync();

                await _unitOfWork.GetRepository<UserRole>().InsertAsync(new UserRole
                {
                    UserId = user.Id,
                    RoleId = role.Id,
                    CreatedBy = user.Id.ToString(),
                    CreatedTime = DateTime.UtcNow,
                });

                await _unitOfWork.SaveAsync();

                return "Create manager account successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<UserModelView> GetUserByIdAsync(string id)
        {
            try
            {
                var user = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id.ToString() == id && !u.DeletedTime.HasValue);

                if (user == null)
                {
                    throw new Exception("can not find or User is deleted");
                }

                var userModelView = _mapper.Map<UserModelView>(user);
                if (string.IsNullOrWhiteSpace(user.BloodTypeId))
                {
                    userModelView.BloodType = null;
                }
                else
                {
                    userModelView.BloodType = user.BloodType.BloodName;
                }

                return userModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<BasePaginatedList<ListUserModelView>> GetAllUserAsync(int pageNumber, int pageSize, string? role)
        {
            IQueryable<User> userQuery = _unitOfWork.GetRepository<User>()
                .Entities
                .Where(p => !p.DeletedTime.HasValue)
                .OrderByDescending(s => s.CreatedTime);

            if (!string.IsNullOrWhiteSpace(role))
            {
                userQuery = userQuery.Where(u => u.UserRoles.Any(ur => ur.Role.Name == role));
            }

            int totalCount = await userQuery.CountAsync();

            List<User> paginatedUsers = await userQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            List<ListUserModelView> userModelView = paginatedUsers.Select(u => new ListUserModelView
            {
                Id = u.Id.ToString(),
                UserImage = u.UserImage,
                Name = u.Name,
                PhoneNumber = u.PhoneNumber,
                DonationCount = (ushort)u.DonationCount,
                Point = (int)u.Point,
                DateOfBirth = u.DateOfBirth,
                Role = u.UserRoles.FirstOrDefault()?.Role?.Name ?? ""
            }).ToList();

            return new BasePaginatedList<ListUserModelView>(userModelView, totalCount, pageNumber, pageSize);
        }

        public async Task<string> UpdateUserForAdminAsync(string id, UpdateUserModelView model, string updatedBy)
        {
            try
            {
                var userRepo = _unitOfWork.GetRepository<User>();

                var user = await userRepo.Entities
                    .FirstOrDefaultAsync(u => u.Id.ToString() == id && !u.DeletedTime.HasValue);

                if (user == null)
                {
                    throw new Exception("User is deleted or cannot be found.");
                }

                // Kiểm tra trùng Email
                if (!string.IsNullOrWhiteSpace(model.Email))
                {
                    bool isEmailExist = await userRepo.Entities
                        .AnyAsync(u => u.Email == model.Email && u.Id.ToString() != id && !u.DeletedTime.HasValue);
                    if (isEmailExist)
                    {
                        throw new Exception("Email is already used by another user.");
                    }

                    user.Email = model.Email;
                }

                // Kiểm tra trùng PhoneNumber
                if (!string.IsNullOrWhiteSpace(model.PhoneNumber))
                {
                    bool isPhoneExist = await userRepo.Entities
                        .AnyAsync(u => u.PhoneNumber == model.PhoneNumber && u.Id.ToString() != id && !u.DeletedTime.HasValue);
                    if (isPhoneExist)
                    {
                        throw new Exception("Phone number is already used by another user.");
                    }

                    user.PhoneNumber = model.PhoneNumber;
                }

                if (model.UserImage != null)
                {
                    string imageUrl = await _firebaseService.UploadImageAsync(model.UserImage);
                    user.UserImage = imageUrl;
                }

                // Gán BloodType theo tên
                if (!string.IsNullOrWhiteSpace(model.BloodType))
                {
                    var bloodType = await _unitOfWork.GetRepository<BloodType>().Entities
                        .FirstOrDefaultAsync(bt => bt.BloodName == model.BloodType);
                    if (bloodType == null)
                    {
                        throw new Exception("Invalid blood type name.");
                    }
                    user.BloodTypeId = bloodType.Id;
                }

                // Gán các thuộc tính còn lại nếu có
                user.Name = model.Name ?? user.Name;
                user.DateOfBirth = model.DateOfBirth ?? user.DateOfBirth;
                user.DonationCount = model.DonationCount ?? user.DonationCount;
                user.Address = model.Address ?? user.Address;
                user.Point = model.Point ?? user.Point;
                user.Job = model.Job ?? user.Job;
                user.Gender = model.Gender ?? user.Gender;
                user.PassportNumber = model.PassportNumber ?? user.PassportNumber;
                user.LastUpdatedBy = updatedBy;
                user.LastUpdatedTime = DateTime.Now;

                await _unitOfWork.GetRepository<User>().UpdateAsync(user);
                await _unitOfWork.SaveAsync();

                return "User updated successfully.";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<string> DeleteUserAsync(string id, string deletedBy)
        {
            try
            {
                var user = _unitOfWork.GetRepository<User>()
                    .Entities
                    .FirstOrDefault(u => u.Id.ToString() == id && !u.DeletedTime.HasValue)
                    ?? throw new Exception("User can not find or is deleted");

                user.DeletedTime = DateTime.Now;
                user.DeletedBy = deletedBy;

                await _unitOfWork.GetRepository<User>().UpdateAsync(user);
                await _unitOfWork.SaveAsync();

                return "Deleted user successfull";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BloodDonationWaitTimeModel> ViewDayToDonateBloodAsync(string userId)
        {
            try
            {
                var uId = Guid.Parse(userId);
                var today = DateTime.UtcNow.Date;

                var latestInfo = await _unitOfWork.GetRepository<Appointment>()
                    .Entities
                    .Where(a => a.UserId == uId && a.Status == AppointmentStatus.Completed)
                    .OrderByDescending(a => a.LastUpdatedTime)
                    .Select(a => new { a.LastUpdatedTime, a.BloodComponent })
                    .FirstOrDefaultAsync();

                if (latestInfo != null)
                {
                    int waitingDays = latestInfo.BloodComponent switch
                    {
                        Core.Enum.BloodComponent.WholeBlood => 84,
                        Core.Enum.BloodComponent.RedBloodCells => 84,
                        Core.Enum.BloodComponent.Platelets => 14,
                        Core.Enum.BloodComponent.Plasma => 14,
                        Core.Enum.BloodComponent.WhiteBloodCells => 7,
                        _ => 0
                    };

                    var nextEligibleDate = latestInfo.LastUpdatedTime.Date.AddDays(waitingDays);
                    var daysRemaining = Math.Max((nextEligibleDate - today).Days, 0);

                    return new BloodDonationWaitTimeModel 
                    { 
                        DaysRemaining = daysRemaining,
                        BloodComponent = latestInfo.BloodComponent.ToString(),
                    };
                }
                else
                {
                    return new BloodDonationWaitTimeModel
                    {
                        DaysRemaining = 0,
                        BloodComponent = "N/A",
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<BasePaginatedList<ListRankDonate>> GetRankDonate(int pageNumber, int pageSize)
        {
            var userQuery = _unitOfWork.GetRepository<User>()
                .Entities
                .Where(u => !u.DeletedTime.HasValue);

            int totalCount = await userQuery.CountAsync();

            var rankedUsers = await userQuery
                .OrderByDescending(u => u.DonationCount)
                .Select(u => new ListRankDonate
                {
                    Id = u.Id.ToString(),
                    Name = u.Name,
                    Image = u.UserImage,
                    Count = u.DonationCount
                })
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new BasePaginatedList<ListRankDonate>(rankedUsers, totalCount, pageNumber, pageSize);
        }

        public async Task<DetailRankingModel> GetDetailRankerByIdAsync(string id)
        {
            Guid uId = Guid.Parse(id);
            var user = _unitOfWork.GetRepository<User>()
                .Entities
                .FirstOrDefault(u => u.Id == uId && !u.DeletedTime.HasValue);

            if (user == null)
            {
                throw new Exception("Can not find user or is deleted");
            }

            return new DetailRankingModel
            {
                Name = user.Name,
                Image = user.UserImage ?? "N/A",
                Count = user.DonationCount,
                Job = user.Job ?? "N/A",
                BloodType = user.BloodType != null ? user.BloodType.BloodName : "N/A",
                Point = user.Point,
            };
        }


        public async Task<string> ChangePasswordAsync(ChangePasswordModel model, string userId)
        {
            try
            {
                if (!Guid.TryParse(userId, out var uId))
                    throw new Exception("UserId không hợp lệ");

                var userRepo = _unitOfWork.GetRepository<User>();
                var user = await userRepo.Entities.FirstOrDefaultAsync(u => u.Id == uId && !u.DeletedTime.HasValue);

                if (user == null)
                    throw new Exception("Người dùng không tồn tại");

                // Kiểm tra mật khẩu cũ
                if (_passwordHasher.VerifyHashedPassword(user, user.PasswordHash, model.CurrentPassword)
                    == PasswordVerificationResult.Failed)
                {
                    throw new Exception("Mật khẩu hiện tại không đúng");
                }

                // Hash mật khẩu mới và cập nhật
                user.PasswordHash = _passwordHasher.HashPassword(user, model.NewPassword);
                user.LastUpdatedTime = DateTime.Now;
                user.LastUpdatedBy = userId;

                userRepo.Update(user);
                await _unitOfWork.SaveAsync();

                return "Đổi mật khẩu thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }


        public async Task<UpdateUserResponse> UpdateUserAsync(UpdateInformationModel model, string userId)
        {
            try
            {
                if (!Guid.TryParse(userId, out var uId))
                    throw new Exception("UserId không hợp lệ");

                var userRepo = _unitOfWork.GetRepository<User>();
                var user = await userRepo.Entities
                    .FirstOrDefaultAsync(u => u.Id == uId && !u.DeletedTime.HasValue);

                if (user == null)
                    throw new Exception("Người dùng không tồn tại hoặc đã bị xoá");

                if (model.UserImage != null)
                {
                    string imageUrl = await _firebaseService.UploadImageAsync(model.UserImage);
                    user.UserImage = imageUrl;
                }

                if (!string.IsNullOrWhiteSpace(model.BloodType))
                {
                    var bloodType = await _unitOfWork.GetRepository<BloodType>().Entities
                        .FirstOrDefaultAsync(bt => bt.BloodName == model.BloodType && !bt.DeletedTime.HasValue);
                    if (bloodType == null)
                        throw new Exception("Nhóm máu không hợp lệ");
                    user.BloodTypeId = bloodType.Id;
                }

                user.Name = model.Name ?? user.Name;
                user.DateOfBirth = model.DateOfBirth ?? user.DateOfBirth;
                user.Address = model.Address ?? user.Address;
                user.Job = model.Job ?? user.Job;
                user.Gender = model.Gender ?? user.Gender;
                user.PassportNumber = model.PassportNumber ?? user.PassportNumber;

                user.LastUpdatedTime = DateTime.Now;
                user.LastUpdatedBy = userId;

                await userRepo.UpdateAsync(user);
                await _unitOfWork.SaveAsync();

                return new UpdateUserResponse
                {
                    Address = user.Address,
                    Job = user.Job,
                    BloodType = user.BloodType.BloodName,
                    Name = user.Name,
                    DateOfBirth = user.DateOfBirth,
                    Gender = user.Gender,
                    PassportNumber = user.PassportNumber,
                    UserImage = user.UserImage,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Point = user.Point,
                    DonationCount = user.DonationCount,
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }

        public async Task<string> RequestUpdateEmail(string userId)
        {
            try
            {
                Guid uId= Guid.Parse(userId);
                var user = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == uId && !u.DeletedTime.HasValue);
                if (user == null || user.Email == null)
                    throw new Exception("Người dùng không tồn tại hoặc Email chưa có");

                await _sendMailService.SendOtpAsync(user.Email);

                return "Gửi OTP thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }

        public async Task<string> VerifyOtpEmailAsync(VerifyOtpModel model)
        {
            try
            {
                var otpEntity = await _unitOfWork.GetRepository<Otp>()
                    .Entities
                    .OrderByDescending(u => u.CreatedTime)
                    .FirstOrDefaultAsync(o => o.Email == model.Email && o.OtpNumber == model.Otp);

                if (otpEntity == null)
                {
                    return "OTP không chính xác";
                }

                // Kiểm tra hết hạn dựa trên CreateTime
                var expireTime = otpEntity.CreatedTime.AddMinutes(3);
                if (expireTime < DateTime.UtcNow)
                {
                    // Xoá OTP hết hạn
                    await _unitOfWork.GetRepository<Otp>().DeleteAsync(otpEntity);
                    await _unitOfWork.SaveAsync();

                    return "OTP đã hết hạn";
                }

                // OTP hợp lệ → xoá sau khi xác thực
                otpEntity.DeletedTime = DateTime.Now;
                await _unitOfWork.GetRepository<Otp>().UpdateAsync(otpEntity);
                await _unitOfWork.SaveAsync();

                return "Xác thực OTP thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }


        public async Task<string> UpdateEmailAsync(string currentUserId, UpdateEmailRequest request)
        {
            try
            {
                if (!Guid.TryParse(currentUserId, out var userId))
                    throw new Exception("UserId không hợp lệ");

                var userRepo = _unitOfWork.GetRepository<User>();
                var otpRepo = _unitOfWork.GetRepository<Otp>();

                var user = await userRepo.Entities.FirstOrDefaultAsync(u => u.Id == userId && !u.DeletedTime.HasValue);
                if (user == null)
                    throw new Exception("Người dùng không tồn tại");

                // Kiểm tra email mới đã có ai dùng chưa
                bool isEmailExists = await userRepo.Entities
                    .AnyAsync(u => u.Email == request.NewEmail && u.Id != userId && !u.DeletedTime.HasValue);
                if (isEmailExists)
                    throw new Exception("Email này đã được sử dụng bởi người dùng khác");

                // Kiểm tra OTP đã xác thực chưa (DeletedTime != null)
                var otpEntity = await otpRepo.Entities
                    .Where(o => o.Email == user.Email && o.DeletedTime != null)
                    .FirstOrDefaultAsync();

                if (otpEntity == null || !otpEntity.DeletedTime.HasValue)
                    throw new Exception("OTP chưa được xác thực hoặc không hợp lệ");

                // Xóa OTP đã dùng
                await otpRepo.DeleteAsync(otpEntity);

                // Cập nhật email
                user.Email = request.NewEmail;
                user.LastUpdatedBy = currentUserId;
                user.LastUpdatedTime = DateTime.UtcNow;

                await userRepo.UpdateAsync(user);
                await _unitOfWork.SaveAsync();

                return "Cập nhật email thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }


    }
}
