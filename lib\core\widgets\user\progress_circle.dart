import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'heart_progress_bar.dart';

class ProgressCircle extends StatefulWidget {
  final int daysLeft;
  final String label;
  final String donationType;

  const ProgressCircle({
    Key? key,
    required this.daysLeft,
    required this.label,
    required this.donationType,
  }) : super(key: key);

  @override
  State<ProgressCircle> createState() => _ProgressCircleState();
}

class _ProgressCircleState extends State<ProgressCircle> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _scaleAnimation;

  static const Map<String, int> _totalDaysMap = {
    'whole_blood': 84,
    'red_blood_cells': 84,
    'plasma': 14,
    'platelets': 14,
    'white_blood_cells': 7,
  };

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    final totalDays = _totalDaysMap[widget.donationType.toLowerCase().replaceAll(' ', '_')] ?? 84;
    final progress = widget.daysLeft >= totalDays ? 0.0 : 1.0 - (widget.daysLeft / totalDays);

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    ));

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final size = screenWidth * 0.25; // Circle size is 25% of screen width

    final totalDays = _totalDaysMap[widget.donationType.toLowerCase().replaceAll(' ', '_')] ?? 84;
    final progress = widget.daysLeft >= totalDays ? 0.0 : 1.0 - (widget.daysLeft / totalDays);

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _animationController.reset();
        _animationController.forward();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _animationController.value,
              child: Container(
                padding: EdgeInsets.all(screenWidth * 0.04),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.25),
                      Colors.white.withOpacity(0.15),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(screenWidth * 0.06),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: size,
                      height: size,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: size,
                            height: size,
                            child: CustomPaint(
                              painter: HeartPainter(
                                fillProgress: _progressAnimation.value,
                                isReady: false,
                                colors: [Colors.red, Colors.redAccent],
                              ),
                            ),
                          ),
                          Text(
                            '${(progress * 100).toInt()}%',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: size * 0.2,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.5),
                                  blurRadius: 4,
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: screenWidth * 0.03),
                    Text(
                      '${widget.daysLeft}',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: screenWidth * 0.09,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          )
                        ],
                      ),
                    ),
                    SizedBox(height: screenWidth * 0.01),
                    Text(
                      localizations.translate(widget.label),
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: screenWidth * 0.035,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: screenWidth * 0.02),
                    Text(
                      localizations.translate(widget.donationType),
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: screenWidth * 0.04,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}