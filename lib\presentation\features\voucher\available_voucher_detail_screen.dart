import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/data/models/Enum/voucher_type.dart';
import 'package:bloodplusmobile/data/models/voucher_model.dart';
import 'package:bloodplusmobile/data/services/voucher_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';

class AvailableVoucherDetailScreen extends StatefulWidget {
  final Voucher voucher;

  const AvailableVoucherDetailScreen({
    super.key,
    required this.voucher,
  });

  @override
  State<AvailableVoucherDetailScreen> createState() => _AvailableVoucherDetailScreenState();
}

class _AvailableVoucherDetailScreenState extends State<AvailableVoucherDetailScreen>
    with TickerProviderStateMixin {
  final VoucherService _voucherService = VoucherService();
  Voucher? _voucher;
  bool _isRedeeming = false;
  bool _isLoading = true;
  String? _errorMessage;
  List<Voucher> _latestVouchers = [];

  late AnimationController _animationController;
  late AnimationController _buttonAnimationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _buttonScaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchVoucherDetail();
    _fetchLatestVouchers();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<double>(begin: 30.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _buttonScaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _buttonAnimationController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _buttonAnimationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _fetchVoucherDetail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final voucher = await _voucherService.getVoucherById(widget.voucher.id);
      if (mounted) {
        setState(() {
          _voucher = voucher;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchLatestVouchers() async {
    try {
      final response = await _voucherService.getVouchers(pageNumber: 1, pageSize: 5);
      if (mounted) {
        setState(() {
          _latestVouchers = response.items.where((v) => v.id != widget.voucher.id).toList();
        });
      }
    } catch (e) {
      debugPrint('Error fetching latest vouchers: $e');
    }
  }

  Future<void> _redeemVoucher() async {
    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    setState(() {
      _isRedeeming = true;
      _errorMessage = null;
    });

    try {
      final result = await _voucherService.addVoucherForUser(widget.voucher.id);
      if (mounted) {
        _showSuccessDialog(result);
      }
    } catch (e) {
      String friendlyMessage;
      final errorStr = e.toString();
      if (errorStr.contains("Don't have enough Point to get Voucher")) {
        friendlyMessage = 'Bạn không đủ điểm để đổi voucher này.\nYou do not have enough points to redeem this voucher.';
      } else {
        friendlyMessage = 'Đổi voucher thất bại, vui lòng thử lại.\nFailed to redeem voucher, please try again.';
      }
      if (mounted) {
        setState(() {
          _errorMessage = friendlyMessage;
        });
        _showErrorSnackBar(friendlyMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRedeeming = false;
        });
      }
    }
  }

  void _showSuccessDialog(String message) {
    final localizations = AppLocalizations.of(context);
    DialogHelper.showSuccessWithDualButton(
      context: context,
      title: localizations.translate('redeem_success') ?? 'Đổi voucher thành công!',
      message: localizations.translate('redeem_success_message') ?? 'Bạn đã đổi voucher thành công! Hãy kiểm tra trong mục Voucher của tôi.',
      primaryButtonText: localizations.translate('view') ?? 'Xem Voucher Của Tôi',
      secondaryButtonText: localizations.translate('close') ?? 'Đóng',
      onPrimaryPressed: () {
        Navigator.pushNamedAndRemoveUntil(
          context,
          AppRoutes.myVouchersScreen,
              (route) => route.isFirst,
        );
      },
      onSecondaryPressed: () {
        NavigationService.navigateToHome();
      },
      icon: Icons.check_circle_outline_rounded,
      iconColor: AppTheme.primaryRed,
    );
  }

  void _showErrorSnackBar(String message) {
    final screenWidth = MediaQuery.of(context).size.width;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline_rounded, color: Colors.white, size: 20),
            SizedBox(width: screenWidth * 0.03),
            Expanded(child: Text(message, style: TextStyle(fontSize: screenWidth < 600 ? 12 : 14))),
          ],
        ),
        backgroundColor: AppTheme.primaryRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        margin: EdgeInsets.all(screenWidth * 0.04),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: _buildLoadingState(localizations, screenWidth, isSmallScreen),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: _buildErrorState(localizations, screenWidth, isSmallScreen),
      );
    }

    final voucher = _voucher ?? widget.voucher;
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(voucher, localizations, screenWidth, isSmallScreen),
          SliverToBoxAdapter(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Transform.translate(
                offset: Offset(0, _slideAnimation.value),
                child: Column(
                  children: [
                    SizedBox(height: isSmallScreen ? 6 : 8),
                    _buildVoucherInfoCard(voucher, localizations, screenWidth, isSmallScreen),
                    _buildQuickInfoSection(voucher, localizations, screenWidth, isSmallScreen),
                    _buildDetailsCard(voucher, localizations, screenWidth, isSmallScreen),
                    _buildRedeemSection(localizations, screenWidth, isSmallScreen),
                    if (_latestVouchers.isNotEmpty && _latestVouchers.length > 1) ...[
                      _buildLatestVouchersSection(localizations, screenWidth, isSmallScreen),
                    ],
                    SizedBox(height: isSmallScreen ? 24 : 32),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppTheme.backgroundColor, Color(0xFFF0F2F5)],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            _buildSimpleAppBar(localizations, screenWidth, isSmallScreen),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: isSmallScreen ? 16 : 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: CircularProgressIndicator(
                        valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
                        strokeWidth: isSmallScreen ? 2.5 : 3,
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 24 : 32),
                    Text(
                      localizations.translate('loading_voucher') ?? 'Đang tải thông tin voucher...',
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.textSecondary,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppTheme.backgroundColor, Color(0xFFF0F2F5)],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            _buildSimpleAppBar(localizations, screenWidth, isSmallScreen),
            Expanded(
              child: Center(
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.all(isSmallScreen ? 24 : 32),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.red.withOpacity(0.1),
                              blurRadius: isSmallScreen ? 16 : 20,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.error_outline_rounded,
                          size: isSmallScreen ? 48 : 64,
                          color: Colors.red[400],
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 24 : 32),
                      Text(
                        localizations.translate('error_occurred') ?? 'Có lỗi xảy ra',
                        style: AppTheme.headingMedium.copyWith(
                          fontSize: isSmallScreen ? 16 : 18,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 8 : 12),
                      Text(
                        _errorMessage!,
                        style: AppTheme.bodyMedium.copyWith(
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: isSmallScreen ? 24 : 32),
                      Container(
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryRed.withOpacity(0.3),
                              blurRadius: isSmallScreen ? 8 : 12,
                              offset: const Offset(0, 6),
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: _fetchVoucherDetail,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 24 : 32,
                              vertical: isSmallScreen ? 12 : 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.refresh_rounded, color: Colors.white, size: isSmallScreen ? 18 : 20),
                              SizedBox(width: isSmallScreen ? 6 : 8),
                              Text(
                                localizations.translate('try_again') ?? 'Thử lại',
                                style: AppTheme.buttonText.copyWith(
                                  fontSize: isSmallScreen ? 14 : 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleAppBar(AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16, vertical: isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryRed.withOpacity(0.3),
            blurRadius: isSmallScreen ? 6 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
            ),
            child: IconButton(
              icon: Icon(Icons.arrow_back_ios_rounded, color: Colors.white, size: isSmallScreen ? 18 : 20),
              onPressed: () => NavigationService.navigateToHome(),
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 16),
          Text(
            localizations.translate('voucher_detail') ?? 'Chi tiết Voucher',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontSize: isSmallScreen ? 16 : 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(Voucher voucher, AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return SliverAppBar(
      expandedHeight: isSmallScreen ? 180 : 220,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: Container(
        margin: EdgeInsets.all(isSmallScreen ? 6 : 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.4),
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: IconButton(
          icon: Icon(Icons.arrow_back_ios_rounded, color: Colors.white, size: isSmallScreen ? 18 : 20),
          onPressed: () => NavigationService.navigateToHome(),
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            if (voucher.panel != null && voucher.panel!.isNotEmpty)
              ShaderMask(
                shaderCallback: (rect) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ).createShader(rect);
                },
                blendMode: BlendMode.multiply,
                child: Image.network(
                  voucher.panel!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => _buildDefaultBackground(isSmallScreen),
                ),
              )
            else
              _buildDefaultBackground(isSmallScreen),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.8),
                  ],
                ),
              ),
            ),
            Positioned(
              top: isSmallScreen ? 60 : 80,
              right: isSmallScreen ? 16 : 20,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16, vertical: isSmallScreen ? 6 : 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: isSmallScreen ? 6 : 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: isSmallScreen ? 6 : 8,
                      height: isSmallScreen ? 6 : 8,
                      decoration: const BoxDecoration(
                        color: AppTheme.primaryRed,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 6 : 8),
                    Text(
                      getVoucherTypeName(voucher.type, localizations),
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textPrimary,
                        fontWeight: FontWeight.w600,
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultBackground(bool isSmallScreen) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryRed,
            AppTheme.secondaryRed,
            Color(0xFFB91C1C),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.card_giftcard_rounded,
                size: isSmallScreen ? 48 : 64,
                color: Colors.white,
              ),
            ),
            SizedBox(height: isSmallScreen ? 12 : 16),
            Text(
              'Voucher Premium',
              style: AppTheme.headingMedium.copyWith(
                color: Colors.white,
                fontSize: isSmallScreen ? 16 : 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoucherInfoCard(Voucher voucher, AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: isSmallScreen ? 16 : 24,
            offset: const Offset(0, 12),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: isSmallScreen ? 6 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: isSmallScreen ? 48 : 64,
                  height: isSmallScreen ? 48 : 64,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [Colors.grey.shade100, Colors.white],
                    ),
                    border: Border.all(color: AppTheme.primaryRed.withOpacity(0.3), width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryRed.withOpacity(0.1),
                        blurRadius: isSmallScreen ? 8 : 12,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: Image.network(
                      voucher.sponsorImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [AppTheme.primaryRed.withOpacity(0.1), Colors.white],
                          ),
                        ),
                        child: Icon(
                          Icons.business_rounded,
                          size: isSmallScreen ? 24 : 32,
                          color: AppTheme.primaryRed,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 12 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        voucher.sponsorName,
                        style: AppTheme.headingSmall.copyWith(
                          color: AppTheme.textPrimary,
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 6),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 3 : 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppTheme.primaryRed.withOpacity(0.1),
                              AppTheme.primaryRed.withOpacity(0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                          border: Border.all(color: AppTheme.primaryRed.withOpacity(0.2)),
                        ),
                        child: Text(
                          localizations.translate('sponsor') ?? 'Đối tác',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.primaryRed,
                            fontWeight: FontWeight.w600,
                            fontSize: isSmallScreen ? 10 : 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 24),
            Text(
              voucher.voucherName,
              style: AppTheme.headingLarge.copyWith(
                fontSize: isSmallScreen ? 18 : 22,
                height: 1.3,
                color: AppTheme.textPrimary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickInfoSection(Voucher voucher, AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 16 : 20),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryRed,
                    AppTheme.secondaryRed,
                  ],
                ),
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryRed.withOpacity(0.4),
                    blurRadius: isSmallScreen ? 12 : 16,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                children: [
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.stars_rounded,
                            size: isSmallScreen ? 24 : 28,
                            color: Colors.white,
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: isSmallScreen ? 8 : 12),
                  Text(
                    '${voucher.point}',
                    style: AppTheme.headingLarge.copyWith(
                      color: Colors.white,
                      fontSize: isSmallScreen ? 24 : 28,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                  Text(
                    localizations.translate('points') ?? 'Điểm',
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.w600,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 16),
          Expanded(
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.orange.shade400,
                    Colors.orange.shade600,
                  ],
                ),
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withOpacity(0.4),
                    blurRadius: isSmallScreen ? 12 : 16,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.schedule_rounded,
                      size: isSmallScreen ? 24 : 28,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: isSmallScreen ? 8 : 12),
                  Text(
                    DateFormat('dd/MM').format(voucher.expiryDate),
                    style: AppTheme.headingMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w800,
                      fontSize: isSmallScreen ? 16 : 18,
                    ),
                  ),
                  Text(
                    DateFormat('yyyy').format(voucher.expiryDate),
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.w600,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsCard(Voucher voucher, AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: isSmallScreen ? 16 : 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailSection(
              title: localizations.translate('description') ?? 'Mô tả',
              content: voucher.description,
              icon: Icons.description_rounded,
              iconColor: Colors.blue,
              isSmallScreen: isSmallScreen,
            ),
            if (voucher.userManual != null && voucher.userManual!.isNotEmpty) ...[
              SizedBox(height: isSmallScreen ? 24 : 32),
              _buildDetailSection(
                title: localizations.translate('usage_instructions') ?? 'Hướng dẫn sử dụng',
                content: voucher.userManual!,
                icon: Icons.help_center_rounded,
                iconColor: Colors.green,
                isSmallScreen: isSmallScreen,
              ),
            ],
            SizedBox(height: isSmallScreen ? 24 : 32),
            _buildDetailSection(
              title: localizations.translate('support') ?? 'Hỗ trợ',
              content: (voucher.support != null && voucher.support!.isNotEmpty)
                  ? voucher.support!
                  : (localizations.translate('no_support_info') ?? 'Liên hệ với nhà cung cấp để được hỗ trợ'),
              icon: Icons.support_agent_rounded,
              iconColor: Colors.purple,
              isSmallScreen: isSmallScreen,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailSection({
    required String title,
    required String content,
    required IconData icon,
    required Color iconColor,
    required bool isSmallScreen,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    iconColor.withOpacity(0.1),
                    iconColor.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
              ),
              child: Icon(
                icon,
                size: isSmallScreen ? 20 : 24,
                color: iconColor,
              ),
            ),
            SizedBox(width: isSmallScreen ? 8 : 12),
            Text(
              title,
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w700,
                fontSize: isSmallScreen ? 14 : 16,
              ),
            ),
          ],
        ),
        SizedBox(height: isSmallScreen ? 12 : 16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.grey.shade50,
                Colors.white,
              ],
            ),
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            border: Border.all(color: iconColor.withOpacity(0.2)),
          ),
          child: Text(
            content,
            style: AppTheme.bodyMedium.copyWith(
              height: 1.7,
              color: AppTheme.textPrimary,
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRedeemSection(AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 16 : 20),
      child: ScaleTransition(
        scale: _buttonScaleAnimation,
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: _isRedeeming ? null : AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
            boxShadow: _isRedeeming
                ? null
                : [
              BoxShadow(
                color: AppTheme.primaryRed.withOpacity(0.4),
                blurRadius: isSmallScreen ? 12 : 16,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _isRedeeming ? null : _redeemVoucher,
              borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 16 : 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                  color: _isRedeeming ? AppTheme.textSecondary : Colors.transparent,
                ),
                child: _isRedeeming
                    ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: isSmallScreen ? 20 : 24,
                      height: isSmallScreen ? 20 : 24,
                      child: CircularProgressIndicator(
                        strokeWidth: isSmallScreen ? 2 : 2.5,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 12 : 16),
                    Text(
                      localizations.translate('redeeming') ?? 'Đang đổi voucher...',
                      style: AppTheme.buttonText.copyWith(
                        fontSize: isSmallScreen ? 16 : 18,
                      ),
                    ),
                  ],
                )
                    : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.redeem_rounded,
                        color: Colors.white,
                        size: isSmallScreen ? 20 : 24,
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 12 : 16),
                    Text(
                      localizations.translate('redeem_voucher') ?? 'Đổi Voucher',
                      style: AppTheme.buttonText.copyWith(
                        fontSize: isSmallScreen ? 16 : 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLatestVouchersSection(AppLocalizations localizations, double screenWidth, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.primaryRed.withOpacity(0.1),
                      AppTheme.primaryRed.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                ),
                child: Icon(
                  Icons.local_fire_department_rounded,
                  color: AppTheme.primaryRed,
                  size: isSmallScreen ? 20 : 24,
                ),
              ),
              SizedBox(width: isSmallScreen ? 8 : 12),
              Text(
                localizations.translate('latest_vouchers') ?? 'Voucher Mới Nhất',
                style: AppTheme.headingMedium.copyWith(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: isSmallScreen ? 16 : 18,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 16 : 20),
          SizedBox(
            height: isSmallScreen ? 160 : 200,
            child: CarouselSlider(
              options: CarouselOptions(
                height: isSmallScreen ? 160 : 200,
                enlargeCenterPage: true,
                enableInfiniteScroll: _latestVouchers.length > 1,
                autoPlay: _latestVouchers.length > 1,
                autoPlayInterval: const Duration(seconds: 4),
                autoPlayAnimationDuration: const Duration(milliseconds: 1000),
                autoPlayCurve: Curves.easeInOutCubic,
                viewportFraction: isSmallScreen ? 0.85 : 0.8,
                padEnds: true,
                scrollPhysics: const BouncingScrollPhysics(),
              ),
              items: _latestVouchers.map((voucher) {
                return Builder(
                  builder: (context) => GestureDetector(
                    onTap: () {
                      Navigator.pushReplacement(
                        context,
                        PageRouteBuilder(
                          pageBuilder: (context, animation, secondaryAnimation) =>
                              AvailableVoucherDetailScreen(voucher: voucher),
                          transitionsBuilder: (context, animation, secondaryAnimation, child) {
                            const begin = Offset(1.0, 0.0);
                            const end = Offset.zero;
                            const curve = Curves.easeInOutCubic;

                            var tween = Tween(begin: begin, end: end).chain(
                              CurveTween(curve: curve),
                            );

                            return SlideTransition(
                              position: animation.drive(tween),
                              child: child,
                            );
                          },
                        ),
                      );
                    },
                    child: Container(
                      width: screenWidth * (isSmallScreen ? 0.8 : 0.75),
                      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 6 : 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            Colors.grey.shade50,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: isSmallScreen ? 16 : 20,
                            offset: const Offset(0, 10),
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.04),
                            blurRadius: isSmallScreen ? 4 : 6,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: isSmallScreen ? 80 : 100,
                              width: double.infinity,
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  if (voucher.panel != null && voucher.panel!.isNotEmpty)
                                    ShaderMask(
                                      shaderCallback: (rect) {
                                        return LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          colors: [
                                            Colors.transparent,
                                            Colors.black.withOpacity(0.3),
                                          ],
                                        ).createShader(rect);
                                      },
                                      blendMode: BlendMode.multiply,
                                      child: Image.network(
                                        voucher.panel!,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) => _buildDefaultVoucherImage(isSmallScreen),
                                      ),
                                    )
                                  else
                                    _buildDefaultVoucherImage(isSmallScreen),
                                  Positioned(
                                    top: isSmallScreen ? 8 : 12,
                                    right: isSmallScreen ? 8 : 12,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            AppTheme.primaryRed,
                                            AppTheme.secondaryRed,
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppTheme.primaryRed.withOpacity(0.4),
                                            blurRadius: isSmallScreen ? 6 : 8,
                                            offset: const Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.stars_rounded,
                                            size: isSmallScreen ? 12 : 14,
                                            color: Colors.white,
                                          ),
                                          SizedBox(width: isSmallScreen ? 3 : 4),
                                          Text(
                                            '${voucher.point}',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w700,
                                              fontSize: isSmallScreen ? 10 : 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Flexible(
                                      child: Text(
                                        voucher.voucherName,
                                        style: AppTheme.bodyLarge.copyWith(
                                          fontWeight: FontWeight.w700,
                                          color: AppTheme.textPrimary,
                                          fontSize: isSmallScreen ? 14 : 16,
                                          height: 1.3,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 6 : 8),
                                    Row(
                                      children: [
                                        Container(
                                          width: isSmallScreen ? 20 : 24,
                                          height: isSmallScreen ? 20 : 24,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: AppTheme.primaryRed.withOpacity(0.3),
                                            ),
                                          ),
                                          child: ClipOval(
                                            child: Image.network(
                                              voucher.sponsorImage,
                                              fit: BoxFit.cover,
                                              errorBuilder: (context, error, stackTrace) => Container(
                                                color: AppTheme.primaryRed.withOpacity(0.1),
                                                child: Icon(
                                                  Icons.business_rounded,
                                                  size: isSmallScreen ? 10 : 12,
                                                  color: AppTheme.primaryRed,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: isSmallScreen ? 6 : 8),
                                        Flexible(
                                          child: Text(
                                            voucher.sponsorName,
                                            style: AppTheme.bodySmall.copyWith(
                                              color: AppTheme.textSecondary,
                                              fontWeight: FontWeight.w500,
                                              fontSize: isSmallScreen ? 10 : 12,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultVoucherImage(bool isSmallScreen) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryRed,
            AppTheme.secondaryRed,
          ],
        ),
      ),
      child: Center(
        child: Icon(
          Icons.card_giftcard_rounded,
          color: Colors.white,
          size: isSmallScreen ? 36 : 48,
        ),
      ),
    );
  }
}