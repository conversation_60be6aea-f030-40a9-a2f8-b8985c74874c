import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/models/chat_session.dart';
import '../models/chat_message.dart';

class ChatApiService {

  Future<List<ChatMessage>> sendMessage({
    required String token,
    required String message,
    required String conversationId,
  }) async {
    try {
      final response = await ApiConfig.authenticatedPost(
        '/chat/send',
        token: token,
        body: {
          'Message': message,
          'ConversationId': conversationId,
        },
      );

      print('=== CHAT SEND RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        return jsonList.map((e) => ChatMessage.fromJson(e)).toList();
      } else {
        throw Exception('Lỗi gửi tin nhắn: ${response.statusCode}');
      }
    } catch (e) {
      print('Send message error: $e');
      throw Exception('Lỗi kết nối khi gửi tin nhắn: $e');
    }
  }


  Future<List<ChatSession>> fetchChatSessions({
    required String token,
  }) async {
    try {
      final response = await ApiConfig.authenticatedGet(
        '/chat/conversations',
        token: token,
      );

      print('=== FETCH CHAT SESSIONS RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        return data.entries.map((entry) => ChatSession.fromJson(entry)).toList();
      } else {
        throw Exception('Lỗi tải danh sách phiên chat: ${response.statusCode}');
      }
    } catch (e) {
      print('Fetch sessions error: $e');
      throw Exception('Lỗi kết nối khi tải danh sách phiên: $e');
    }
  }

  Future<List<ChatMessage>> fetchMessagesForSession({
    required String token,
    required String conversationId,
    int limit = 50,
  }) async {
    try {
      final response = await ApiConfig.authenticatedGet(
        '/chat/$conversationId?limit=$limit',
        token: token,
      );

      print('=== FETCH MESSAGES RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        return jsonList.map((e) => ChatMessage.fromJson(e)).toList();
      } else {
        throw Exception('Lỗi tải tin nhắn: ${response.statusCode}');
      }
    } catch (e) {
      print('Fetch messages error: $e');
      throw Exception('Lỗi kết nối khi tải tin nhắn: $e');
    }
  }

  Future<void> deleteSession({
    required String token,
    required String conversationId,
  }) async {
    try {
      final response = await ApiConfig.authenticatedDelete(
        '/chat/conversation/$conversationId',
        token: token,
      );

      print('=== DELETE SESSION RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode != 200) {
        throw Exception('Lỗi xóa phiên chat: ${response.statusCode}');
      }
    } catch (e) {
      print('Delete session error: $e');
      throw Exception('Lỗi kết nối khi xóa phiên chat: $e');
    }
  }
}
