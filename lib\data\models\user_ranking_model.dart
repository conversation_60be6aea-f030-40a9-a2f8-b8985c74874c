class UserRanking {
  final String id;
  final String name;
  final String? image;
  final int count;

  UserRanking({required this.id, required this.name, this.image, required this.count});

  factory UserRanking.fromJson(Map<String, dynamic> json) {
    return UserRanking(
      id: json['Id'],
      name: json['Name'],
      image: json['Image'],
      count: json['Count'] ?? 0,
    );
  }
}

class UserRankingDetail {
  final String name;
  final String? image;
  final int count;
  final String bloodType;
  final String job;
  final int point;

  UserRankingDetail({
    required this.name,
    this.image,
    required this.count,
    required this.bloodType,
    required this.job,
    required this.point,
  });

  factory UserRankingDetail.fromJson(Map<String, dynamic> json) {
    return UserRankingDetail(
      name: json['Name'],
      image: json['Image'],
      count: json['Count'] ?? 0,
      bloodType: json['BloodType'] ?? '',
      job: json['Job'] ?? '',
      point: json['Point'] ?? 0,
    );
  }
}