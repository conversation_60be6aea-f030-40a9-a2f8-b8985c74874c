import 'dart:io';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/presentation/features/auth/verify_otp_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bloodplusmobile/data/models/register_request_model.dart';
import 'package:bloodplusmobile/data/services/auth_service.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _verifyPasswordController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _bloodTypeController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _jobController = TextEditingController();
  final TextEditingController _passportController = TextEditingController();

  // Add FocusNodes for all required fields
  final FocusNode _userNameFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _verifyPasswordFocusNode = FocusNode();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _nameFocusNode = FocusNode();
  final FocusNode _dobFocusNode = FocusNode();
  final FocusNode _addressFocusNode = FocusNode();
  final FocusNode _jobFocusNode = FocusNode();
  final FocusNode _passportFocusNode = FocusNode();

  int _gender = 0;
  File? _userImage;
  bool _isLoading = false;
  bool _passwordVisible = false;
  bool _verifyPasswordVisible = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final AuthService _authService = AuthService();
  final List<String> _bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

  // Password strength indicators
  bool _hasMinLength = false;
  bool _hasUpperCase = false;
  bool _hasLowerCase = false;
  bool _hasNumber = false;
  bool _hasSpecialChar = false;
  late AnimationController _strengthController;
  late Animation<double> _strengthAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));
    _animationController.forward();
    // Password strength
    _strengthController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _strengthAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _strengthController, curve: Curves.easeOut),
    );
    _passwordController.addListener(_checkPasswordStrength);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _strengthController.dispose();
    _userNameController.dispose();
    _passwordController.dispose();
    _verifyPasswordController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _bloodTypeController.dispose();
    _nameController.dispose();
    _dobController.dispose();
    _addressController.dispose();
    _jobController.dispose();
    _passportController.dispose();
    // Dispose FocusNodes
    _userNameFocusNode.dispose();
    _passwordFocusNode.dispose();
    _verifyPasswordFocusNode.dispose();
    _emailFocusNode.dispose();
    _phoneFocusNode.dispose();
    _nameFocusNode.dispose();
    _dobFocusNode.dispose();
    _addressFocusNode.dispose();
    _jobFocusNode.dispose();
    _passportFocusNode.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      maxWidth: 512,
      maxHeight: 512,
      imageQuality: 80,
    );
    if (pickedFile != null) {
      setState(() {
        _userImage = File(pickedFile.path);
      });
    }
  }

  Future<void> _register() async {
    // Validate form first
    if (!_formKey.currentState!.validate()) {
      // Focus the first invalid field
      if (_userNameController.text.isEmpty) {
        FocusScope.of(context).requestFocus(_userNameFocusNode);
      } else if (_passwordController.text.isEmpty || _passwordController.text.length < 6) {
        FocusScope.of(context).requestFocus(_passwordFocusNode);
      } else if (_verifyPasswordController.text != _passwordController.text) {
        FocusScope.of(context).requestFocus(_verifyPasswordFocusNode);
      } else if (_emailController.text.isEmpty || !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(_emailController.text)) {
        FocusScope.of(context).requestFocus(_emailFocusNode);
      } else if (_phoneController.text.isNotEmpty && _phoneController.text.length < 9) {
        FocusScope.of(context).requestFocus(_phoneFocusNode);
      } else if (_nameController.text.isEmpty) {
        FocusScope.of(context).requestFocus(_nameFocusNode);
      } else if (_dobController.text.isEmpty) {
        FocusScope.of(context).requestFocus(_dobFocusNode);
      } else if (_bloodTypeController.text.isEmpty) {
        // Blood type is a dropdown, so we don't focus
      } else if (_addressController.text.isEmpty) {
        FocusScope.of(context).requestFocus(_addressFocusNode);
      } else if (_jobController.text.isEmpty) {
        FocusScope.of(context).requestFocus(_jobFocusNode);
      } else if (_passportController.text.isEmpty) {
        FocusScope.of(context).requestFocus(_passportFocusNode);
      }
      return;
    }

    setState(() => _isLoading = true);
    HapticFeedback.lightImpact();

    final model = RegisterRequestModel(
      userName: _userNameController.text.trim(),
      password: _passwordController.text,
      verifyPassword: _verifyPasswordController.text,
      email: _emailController.text.trim(),
      phoneNumber: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      bloodType: _bloodTypeController.text.trim(),
      name: _nameController.text.trim(),
      dateOfBirth: _dobController.text.trim(),
      address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
      job: _jobController.text.trim().isNotEmpty ? _jobController.text.trim() : null,
      gender: _gender,
      passportNumber: _passportController.text.trim().isNotEmpty ? _passportController.text.trim() : null,
    );

    try {
      final success = await _authService.registerAccount(model, userImage: _userImage);
      setState(() => _isLoading = false);

      if (success) {
        HapticFeedback.heavyImpact();
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => VerifyOtpScreen(email: _emailController.text.trim()),
            settings: RouteSettings(arguments: {'registerInfo': model}),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return SlideTransition(
                position: animation.drive(
                  Tween(begin: const Offset(1.0, 0.0), end: Offset.zero).chain(CurveTween(curve: Curves.easeInOut)),
                ),
                child: child,
              );
            },
          ),
        );
      } else {
        HapticFeedback.heavyImpact();
        _showErrorSnackBar(AppLocalizations.of(context).translate('register_failed'));
      }
    } catch (e) {
      setState(() => _isLoading = false);
      HapticFeedback.heavyImpact();
      String errorMessage = AppLocalizations.of(context).translate('register_error');
      final errorStr = e.toString();
      if (errorStr.contains('Email already exists') || errorStr.contains('Email đã tồn tại') || errorStr.contains('Email already in use')) {
        errorMessage = AppLocalizations.of(context).translate('email_exists');
      } else if (errorStr.contains('Username already exists') || errorStr.contains('Tên đăng nhập đã tồn tại') || errorStr.contains('Username already in use')) {
        errorMessage = AppLocalizations.of(context).translate('username_exists');
      } else if (errorStr.contains('Phone number already exists') || errorStr.contains('Số điện thoại đã tồn tại') || errorStr.contains('PhoneNumber already in use') || errorStr.contains('Phone number already in use')) {
        errorMessage = AppLocalizations.of(context).translate('phone_exists');
      } else if (errorStr.contains('password') && errorStr.contains('not match')) {
        errorMessage = AppLocalizations.of(context).translate('password_not_match');
      } else if (errorStr.contains('Invalid email')) {
        errorMessage = AppLocalizations.of(context).translate('invalid_email');
      } else if (errorStr.contains('required')) {
        errorMessage = AppLocalizations.of(context).translate('please_fill_all_fields');
      } else if (errorStr.contains('network') || errorStr.contains('SocketException')) {
        errorMessage = AppLocalizations.of(context).translate('network_error');
      }
      _showErrorSnackBar(errorMessage);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _checkPasswordStrength() {
    final password = _passwordController.text;
    setState(() {
      _hasMinLength = password.length >= 8;
      _hasUpperCase = password.contains(RegExp(r'[A-Z]'));
      _hasLowerCase = password.contains(RegExp(r'[a-z]'));
      _hasNumber = password.contains(RegExp(r'[0-9]'));
      _hasSpecialChar = password.contains(RegExp(r'[!@#\$%^&*(),.?":{}|<>]'));
    });
    final strength = _getPasswordStrength();
    _strengthController.animateTo(strength / 5);
  }

  int _getPasswordStrength() {
    int strength = 0;
    if (_hasMinLength) strength++;
    if (_hasUpperCase) strength++;
    if (_hasLowerCase) strength++;
    if (_hasNumber) strength++;
    if (_hasSpecialChar) strength++;
    return strength;
  }

  Color _getStrengthColor() {
    final strength = _getPasswordStrength();
    if (strength <= 2) return Colors.red;
    if (strength <= 3) return Colors.orange;
    if (strength <= 4) return Colors.yellow[700]!;
    return Colors.green;
  }

  String _getStrengthText() {
    final localizations = AppLocalizations.of(context);
    final strength = _getPasswordStrength();
    if (strength <= 2) return localizations.translate('password_weak');
    if (strength <= 3) return localizations.translate('password_medium');
    if (strength <= 4) return localizations.translate('password_strong');
    return localizations.translate('password_very_strong');
  }

  Widget _buildPasswordStrengthIndicator() {
    return AnimatedBuilder(
      animation: _strengthAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  AppLocalizations.of(context).translate('password_strength') + ': ',
                  style: AppTheme.bodySmall,
                ),
                Text(
                  _getStrengthText(),
                  style: AppTheme.bodySmall.copyWith(
                    color: _getStrengthColor(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _strengthAnimation.value,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(_getStrengthColor()),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildRequirementChip(AppLocalizations.of(context).translate('min_8_characters'), _hasMinLength),
                _buildRequirementChip(AppLocalizations.of(context).translate('uppercase_letter'), _hasUpperCase),
                _buildRequirementChip(AppLocalizations.of(context).translate('lowercase_letter'), _hasLowerCase),
                _buildRequirementChip(AppLocalizations.of(context).translate('number'), _hasNumber),
                _buildRequirementChip(AppLocalizations.of(context).translate('special_character'), _hasSpecialChar),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildRequirementChip(String text, bool isMet) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isMet ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMet ? Colors.green : Colors.grey,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isMet ? Icons.check : Icons.close,
            size: 12,
            color: isMet ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: AppTheme.captionText.copyWith(
              color: isMet ? Colors.green : Colors.grey,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String label,
    bool required = false,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    VoidCallback? onTap,
    bool readOnly = false,
    Widget? suffixIcon,
    String? hintText,
    required double fontScale,
    required double fieldHeight,
    FocusNode? focusNode, // Add focusNode param
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 16 * fontScale),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12 * fontScale),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10 * fontScale,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        validator: validator,
        onTap: onTap,
        readOnly: readOnly,
        style: AppTheme.bodyLarge.copyWith(color: AppTheme.textPrimary, fontSize: 16 * fontScale),
        focusNode: focusNode, // Pass focusNode to TextFormField
        decoration: InputDecoration(
          labelText: '$label${required ? ' *' : ''}',
          hintText: hintText,
          labelStyle: AppTheme.bodyMedium.copyWith(fontSize: 14 * fontScale),
          hintStyle: AppTheme.bodyMedium.copyWith(color: Colors.grey.shade400, fontSize: 14 * fontScale),
          suffixIcon: suffixIcon,
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.borderColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.primaryRed, width: 2 * fontScale),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: Colors.red.shade400),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 16 * fontScale, vertical: fieldHeight * 0.5),
        ),
      ),
    );
  }

  Widget _buildBloodTypeDropdown({required double fontScale, required double fieldHeight}) {
    return Container(
      margin: EdgeInsets.only(bottom: 16 * fontScale),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12 * fontScale),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10 * fontScale,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value: _bloodTypeController.text.isEmpty ? null : _bloodTypeController.text,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).translate('blood_type') + ' *',
          labelStyle: AppTheme.bodyMedium.copyWith(fontSize: 14 * fontScale),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.borderColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.primaryRed, width: 2 * fontScale),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: Colors.red.shade400),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 16 * fontScale, vertical: fieldHeight * 0.5),
        ),
        items: _bloodTypes.map((String bloodType) {
          return DropdownMenuItem<String>(
            value: bloodType,
            child: Text(AppLocalizations.of(context).translate(bloodType), style: AppTheme.bodyLarge.copyWith(fontSize: 16 * fontScale)),
          );
        }).toList(),
        onChanged: (String? newValue) {
          setState(() {
            _bloodTypeController.text = newValue ?? '';
          });
        },
        validator: (v) => v == null || v.isEmpty ? AppLocalizations.of(context).translate('choose_blood_type_required') : null,
      ),
    );
  }

  Widget _buildGenderDropdown({required double fontScale, required double fieldHeight}) {
    return Container(
      margin: EdgeInsets.only(bottom: 16 * fontScale),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12 * fontScale),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10 * fontScale,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<int>(
        value: _gender,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).translate('gender') + ' *',
          labelStyle: AppTheme.bodyMedium.copyWith(fontSize: 14 * fontScale),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.borderColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12 * fontScale),
            borderSide: BorderSide(color: AppTheme.primaryRed, width: 2 * fontScale),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 16 * fontScale, vertical: fieldHeight * 0.5),
        ),
        items: [
          DropdownMenuItem(value: 0, child: Text(AppLocalizations.of(context).translate('male'), style: AppTheme.bodyLarge.copyWith(fontSize: 16 * fontScale))),
          DropdownMenuItem(value: 1, child: Text(AppLocalizations.of(context).translate('female'), style: AppTheme.bodyLarge.copyWith(fontSize: 16 * fontScale))),
          DropdownMenuItem(value: 2, child: Text(AppLocalizations.of(context).translate('other_gender'), style: AppTheme.bodyLarge.copyWith(fontSize: 16 * fontScale))),
        ],
        onChanged: (v) => setState(() => _gender = v ?? 0),
      ),
    );
  }

  Widget _buildImagePicker({required double fontScale, required double imageSize}) {
    return Container(
      margin: EdgeInsets.only(bottom: 20 * fontScale),
      padding: EdgeInsets.all(20 * fontScale),
      decoration: AppTheme.cardDecoration,
      child: Column(
        children: [
          Text(AppLocalizations.of(context).translate('avatar'), style: AppTheme.headingSmall.copyWith(fontSize: 18 * fontScale)),
          SizedBox(height: 16 * fontScale),
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: imageSize,
              height: imageSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppTheme.backgroundColor,
                border: Border.all(color: AppTheme.primaryRed, width: 3 * fontScale),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryRed.withOpacity(0.2),
                    blurRadius: 10 * fontScale,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: _userImage != null
                  ? ClipOval(
                child: Image.file(
                  _userImage!,
                  width: imageSize,
                  height: imageSize,
                  fit: BoxFit.cover,
                ),
              )
                  : Icon(
                Icons.add_a_photo,
                size: 40 * fontScale,
                color: AppTheme.primaryRed,
              ),
            ),
          ),
          SizedBox(height: 12 * fontScale),
          Text(
            _userImage != null ? AppLocalizations.of(context).translate('tap_to_change') : AppLocalizations.of(context).translate('tap_to_choose_image'),
            style: AppTheme.bodyMedium.copyWith(fontSize: 14 * fontScale),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon, double fontScale) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16 * fontScale),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8 * fontScale),
            decoration: BoxDecoration(
              color: AppTheme.primaryRed.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8 * fontScale),
            ),
            child: Icon(icon, color: AppTheme.primaryRed, size: 20 * fontScale),
          ),
          SizedBox(width: 12 * fontScale),
          Text(title, style: AppTheme.headingSmall.copyWith(fontSize: 18 * fontScale)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final screenHeight = constraints.maxHeight;
        final fontScale = screenWidth > 600 ? 1.2 : 1.0;
        final padding = screenWidth * 0.05;
        final fieldHeight = screenHeight * 0.07;
        final imageSize = screenWidth > 600 ? 150.0 : screenWidth * 0.3;

        return Scaffold(
          backgroundColor: AppTheme.backgroundColor,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios, color: AppTheme.textPrimary, size: 24 * fontScale),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(AppLocalizations.of(context).translate('register_account_title'), style: AppTheme.headingMedium.copyWith(fontSize: 20 * fontScale)),
            centerTitle: true,
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(padding),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(padding * 1.2),
                        decoration: AppTheme.elevatedCardDecoration.copyWith(
                          gradient: AppTheme.primaryGradient,
                        ),
                        child: Column(
                          children: [
                            Icon(Icons.person_add, size: 40 * fontScale, color: Colors.white),
                            SizedBox(height: 8 * fontScale),
                            Text(
                              AppLocalizations.of(context).translate('register_new_account'),
                              style: AppTheme.headingMedium.copyWith(color: Colors.white, fontSize: 22 * fontScale),
                            ),
                            Text(
                              AppLocalizations.of(context).translate('please_fill_all_fields_full'),
                              style: AppTheme.bodyMedium.copyWith(color: Colors.white70, fontSize: 16 * fontScale),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: padding * 1.2),

                      _buildSectionTitle(AppLocalizations.of(context).translate('account_info_section'), Icons.account_circle, fontScale),
                      _buildStyledTextField(
                        controller: _userNameController,
                        label: AppLocalizations.of(context).translate('username'),
                        required: true,
                        hintText: AppLocalizations.of(context).translate('enter_username'),
                        validator: (v) => v == null || v.isEmpty ? AppLocalizations.of(context).translate('field_required') : null,
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _userNameFocusNode, // Add focusNode
                      ),
                      _buildStyledTextField(
                        controller: _passwordController,
                        label: AppLocalizations.of(context).translate('password'),
                        required: true,
                        obscureText: !_passwordVisible,
                        hintText: AppLocalizations.of(context).translate('min_6_characters'),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _passwordVisible ? Icons.visibility : Icons.visibility_off,
                            color: AppTheme.textSecondary,
                            size: 20 * fontScale,
                          ),
                          onPressed: () => setState(() => _passwordVisible = !_passwordVisible),
                        ),
                        validator: (v) => v == null || v.length < 6 ? AppLocalizations.of(context).translate('min_6_characters') : null,
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _passwordFocusNode, // Add focusNode
                      ),
                      if (_passwordController.text.isNotEmpty)
                        _buildPasswordStrengthIndicator(),
                      _buildStyledTextField(
                        controller: _verifyPasswordController,
                        label: AppLocalizations.of(context).translate('retype_password'),
                        required: true,
                        obscureText: !_verifyPasswordVisible,
                        hintText: AppLocalizations.of(context).translate('retype_password_hint'),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _verifyPasswordVisible ? Icons.visibility : Icons.visibility_off,
                            color: AppTheme.textSecondary,
                            size: 20 * fontScale,
                          ),
                          onPressed: () => setState(() => _verifyPasswordVisible = !_verifyPasswordVisible),
                        ),
                        validator: (v) => v != _passwordController.text ? AppLocalizations.of(context).translate('password_not_match') : null,
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _verifyPasswordFocusNode, // Add focusNode
                      ),

                      _buildSectionTitle(AppLocalizations.of(context).translate('contact_info_section'), Icons.contact_phone, fontScale),
                      _buildStyledTextField(
                        controller: _emailController,
                        label: AppLocalizations.of(context).translate('email'),
                        required: true,
                        keyboardType: TextInputType.emailAddress,
                        hintText: AppLocalizations.of(context).translate('email_hint'),
                        validator: (v) {
                          if (v == null || v.isEmpty) return AppLocalizations.of(context).translate('field_required');
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(v)) {
                            return AppLocalizations.of(context).translate('invalid_email');
                          }
                          return null;
                        },
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _emailFocusNode, // Add focusNode
                      ),
                      _buildStyledTextField(
                        controller: _phoneController,
                        label: AppLocalizations.of(context).translate('phone_number'),
                        required: false,
                        keyboardType: TextInputType.phone,
                        hintText: AppLocalizations.of(context).translate('phone_hint'),
                        validator: (v) => v != null && v.isNotEmpty && v.length < 9 ? AppLocalizations.of(context).translate('invalid_phone') : null,
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _phoneFocusNode, // Add focusNode
                      ),

                      _buildSectionTitle(AppLocalizations.of(context).translate('personal_info_section'), Icons.person, fontScale),
                      _buildStyledTextField(
                        controller: _nameController,
                        label: AppLocalizations.of(context).translate('full_name'),
                        required: true,
                        hintText: AppLocalizations.of(context).translate('full_name_hint'),
                        validator: (v) => v == null || v.isEmpty ? AppLocalizations.of(context).translate('field_required') : null,
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _nameFocusNode, // Add focusNode
                      ),
                      _buildStyledTextField(
                        controller: _dobController,
                        label: AppLocalizations.of(context).translate('date_of_birth'),
                        required: true,
                        readOnly: true,
                        hintText: AppLocalizations.of(context).translate('choose_dob'),
                        suffixIcon: Icon(Icons.calendar_today, color: AppTheme.primaryRed, size: 20 * fontScale),
                        validator: (v) => v == null || v.isEmpty ? AppLocalizations.of(context).translate('choose_dob_required') : null,
                        onTap: () async {
                          FocusScope.of(context).requestFocus(FocusNode());
                          DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: DateTime(2000),
                            firstDate: DateTime(1900),
                            lastDate: DateTime.now(),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: ColorScheme.light(
                                    primary: AppTheme.primaryRed,
                                    onPrimary: Colors.white,
                                    surface: Colors.white,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (picked != null) {
                            _dobController.text = "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
                          }
                        },
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _dobFocusNode, // Add focusNode
                      ),
                      _buildGenderDropdown(fontScale: fontScale, fieldHeight: fieldHeight),
                      _buildBloodTypeDropdown(fontScale: fontScale, fieldHeight: fieldHeight),

                      _buildSectionTitle(AppLocalizations.of(context).translate('additional_info_section'), Icons.info_outline, fontScale),
                      _buildStyledTextField(
                        controller: _addressController,
                        label: AppLocalizations.of(context).translate('address'),
                        hintText: AppLocalizations.of(context).translate('address_hint'),
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _addressFocusNode, // Add focusNode
                      ),
                      _buildStyledTextField(
                        controller: _jobController,
                        label: AppLocalizations.of(context).translate('job'),
                        hintText: AppLocalizations.of(context).translate('job_hint'),
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _jobFocusNode, // Add focusNode
                      ),
                      _buildStyledTextField(
                        controller: _passportController,
                        label: AppLocalizations.of(context).translate('passport_number'),
                        hintText: AppLocalizations.of(context).translate('passport_hint'),
                        fontScale: fontScale,
                        fieldHeight: fieldHeight,
                        focusNode: _passportFocusNode, // Add focusNode
                      ),

                      _buildImagePicker(fontScale: fontScale, imageSize: imageSize),

                      Container(
                        width: double.infinity,
                        height: fieldHeight,
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12 * fontScale),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryRed.withOpacity(0.3),
                              blurRadius: 8 * fontScale,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12 * fontScale),
                            onTap: _isLoading ? null : _register,
                            child: Center(
                              child: _isLoading
                                  ? SizedBox(
                                width: 24 * fontScale,
                                height: 24 * fontScale,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                                  : Text(AppLocalizations.of(context).translate('register'), style: AppTheme.buttonText.copyWith(fontSize: 16 * fontScale)),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: padding * 1.2),

                      Container(
                        padding: EdgeInsets.all(padding),
                        decoration: AppTheme.cardDecoration,
                        child: Text(
                          AppLocalizations.of(context).translate('register_agreement'),
                          style: AppTheme.captionText.copyWith(fontSize: 12 * fontScale),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      SizedBox(height: padding * 2),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}