﻿using BloodPlus.Core.Enum;

namespace BloodPlus.ModelViews.DonationEventModelView
{
    public class DonationEventModelView
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string OrganizationName { get; set; }
        public string Description { get; set; }
        public string Location { get; set; }
        public DateTime EventDate { get; set; }
        public DateTime EndTime { get; set; }
        public int RequiredDonors { get; set; }
        public int CurrentDonors { get; set; }
        public string Status { get; set; }
        public string Image { get; set; }
        public bool IsEmergency { get; set; }
    }
}
