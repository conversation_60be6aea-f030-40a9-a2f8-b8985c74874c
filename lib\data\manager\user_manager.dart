import 'dart:convert';
import 'package:bloodplusmobile/data/manager/days_waiting_manager.dart';
import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserManager {
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _userRoleKey = 'user_role';
  static const String _userInfoKey = 'user_info';
  static const String _verificationIdKey = 'verification_id';

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    serverClientId: '200053914917-0v8qphjmrav3mlh3j9pi4u2459ffcva2.apps.googleusercontent.com',
  );

  Future<void> saveUserToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
    print('Saved token: $token');
  }

  Future<String?> getUserToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);
    print('Retrieved token: $token');
    return token;
  }

  Future<void> saveUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
    print('Saved userId: $userId');
  }

  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString(_userIdKey);
    print('Retrieved userId: $userId');
    return userId;
  }

  Future<void> saveUserEmail(String email) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userEmailKey, email);
    print('Saved email: $email');
  }

  Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString(_userEmailKey);
    print('Retrieved email: $email');
    return email;
  }

  Future<void> saveUserRole(String role) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userRoleKey, role);
    print('Saved role: $role');
  }

  Future<String?> getUserRole() async {
    final prefs = await SharedPreferences.getInstance();
    final role = prefs.getString(_userRoleKey);
    print('Retrieved role: $role');
    return role;
  }


  Future<void> saveUserInfo(String userId, dynamic user) async {
    final prefs = await SharedPreferences.getInstance();
    String userJson;

    if (user is UserModel) {
      userJson = jsonEncode(user.toJson());
    } else if (user is Map<String, dynamic>) {
      userJson = jsonEncode(user);
    } else {
      throw Exception('Invalid user data type');
    }

    await prefs.setString('$_userInfoKey$userId', userJson);
    print('Saved userInfo for userId: $userId');
  }

  Future<UserModel?> getUserInfo(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString('$_userInfoKey$userId');
    if (userJson != null) {
      try {
        return UserModel.fromJson(jsonDecode(userJson));
      } catch (e) {
        print('Error parsing user info: $e');
        return null;
      }
    }
    print('No user info found for userId: $userId');
    return null;
  }

  Future<void> saveVerificationId(String verificationId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_verificationIdKey, verificationId);
    print('Saved verificationId: $verificationId');
  }

  Future<String?> getVerificationId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_verificationIdKey);
  }

  Future<void> clearVerificationId() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_verificationIdKey);
    print('Cleared verificationId');
  }

  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = await getUserId();

    if (userId != null) {
      await prefs.remove('$_userInfoKey$userId');
    }

    await prefs.remove(_tokenKey);
    await prefs.remove(_userIdKey);
    await prefs.remove(_verificationIdKey);
    await prefs.remove('user_name');
    await prefs.remove('user_email');
    await prefs.remove(_userRoleKey);
    await prefs.remove('donation_count');
    await prefs.remove('user_points');
    await prefs.remove('session_expired');
    await prefs.remove('isLoggedIn');

    await DaysWaitingManager().clearDaysWaiting();
    await _googleSignIn.signOut();
    print('Cleared all user data and signed out from Google');
  }

  Future<void> updateUserProfileFetch(UserModel userProfile) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_name', userProfile.name ?? '');
    await prefs.setString('user_email', userProfile.email ?? '');
    await prefs.setInt('donation_count', userProfile.donationCount ?? 0);
    await prefs.setInt('user_points', userProfile.point);
  }

  Future<void> syncUserData(UserModel userProfile, String token) async {
    final userId = userProfile.id;
    if (userId != null) {
      await saveUserId(userId);
      await saveUserToken(token);
      await saveUserInfo(userId, userProfile);
      await updateUserProfileFetch(userProfile);
    }
  }
}