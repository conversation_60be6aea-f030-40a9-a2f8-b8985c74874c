import 'dart:io';
import 'package:bloodplusmobile/data/manager/days_waiting_manager.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/appointment_model.dart';
import 'package:bloodplusmobile/data/models/blog_model.dart';
import 'package:bloodplusmobile/data/models/donation_event_model.dart';
import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:bloodplusmobile/data/services/appointment_service.dart';
import 'package:bloodplusmobile/data/services/blog_service.dart';
import 'package:bloodplusmobile/data/services/donation_event_service.dart';
import 'package:bloodplusmobile/data/services/user_service.dart';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppStateNotifier with ChangeNotifier {
  final DaysWaitingManager _daysWaitingManager = DaysWaitingManager();
  final AppointmentService _appointmentService = AppointmentService();
  final BlogService _blogService = BlogService();
  final DonationEventService _donationEventService = DonationEventService();
  final UserService _userService = UserService();
  final UserManager _userManager = UserManager();

  bool _isLoggedIn = false;
  bool get isLoggedIn => _isLoggedIn;

  UserModel? _user;
  UserModel? get user => _user;
  int _donationCount = 0;
  int get donationCount => _donationCount;
  int get points => _user?.point ?? 0;
  Map<String, dynamic>? _daysWaiting;
  List<Appointment> _appointments = [];
  List<BlogModel> _blogs = [];
  List<DonationEvent> _donationEvents = [];
  bool _isLoading = false;
  bool _isDataReady = false; // Thêm cờ để kiểm tra dữ liệu đã sẵn sàng

  Map<String, dynamic>? get daysWaiting => _daysWaiting;
  List<Appointment> get appointments => _appointments;
  List<BlogModel> get blogs => _blogs;
  List<DonationEvent> get donationEvents => _donationEvents;
  bool get isLoading => _isLoading;
  bool get isDataReady => _isDataReady;

  void resetState() {
    _user = null;
    _donationCount = 0;
    _daysWaiting = null;
    _appointments = [];
    _blogs = [];
    _donationEvents = [];
    _isLoading = false;
    _isDataReady = false;
    _isLoggedIn = false;
    notifyListeners();
  }

  void _loadLoginState() async {
    final prefs = await SharedPreferences.getInstance();
    _isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
    notifyListeners();
  }

  void setLoggedIn(bool value) async {
    _isLoggedIn = value;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLoggedIn', value);
    notifyListeners();
  }

  AppStateNotifier() {
    _loadLoginState();
  }

  Future<bool> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> fetchDaysWaiting(String userId, String token) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchDaysWaiting');
      return;
    }

    print('AppStateNotifier: Starting fetchDaysWaiting for userId: $userId');
    _isLoading = true;
    notifyListeners();

    try {
      _daysWaiting = await _daysWaitingManager.forceRefreshDaysWaiting(userId, token);
      print('AppStateNotifier: Successfully loaded days waiting: \u001b[32m' + _daysWaiting.toString());
    } catch (e) {
      print('AppStateNotifier: Error in fetchDaysWaiting: $e');
    }

    _isLoading = false;
    notifyListeners();
    print('AppStateNotifier: fetchDaysWaiting completed');
  }

  Future<void> fetchBlogs({bool forceRefresh = false}) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchBlogs');
      return;
    }

    print('AppStateNotifier: Starting fetchBlogs');

    try {
      final response = await _blogService.getBlogs(pageNumber: 1, pageSize: 10);
      _blogs = response.items;
      print('AppStateNotifier: Successfully loaded ${_blogs.length} blogs');
    } catch (e) {
      print('AppStateNotifier: Error in fetchBlogs: $e');
    }

    print('AppStateNotifier: fetchBlogs completed');
  }

  Future<void> fetchDonationEvents({bool forceRefresh = false}) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchDonationEvents');
      return;
    }

    print('AppStateNotifier: Starting fetchDonationEvents');

    try {
      final response = await _donationEventService.getDonationEvents(pageNumber: 1, pageSize: 10);
      _donationEvents = response.items;
      print('AppStateNotifier: Successfully loaded ${_donationEvents.length} donation events');
    } catch (e) {
      print('AppStateNotifier: Error in fetchDonationEvents: $e');
    }

    print('AppStateNotifier: fetchDonationEvents completed');
  }

  Future<void> fetchAllData({bool forceRefresh = false}) async {
    print('AppStateNotifier: Starting fetchAllData');

    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchAllData');
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      final userId = await _userManager.getUserId();
      final token = await _userManager.getUserToken();

      await fetchUserProfile(forceRefresh: forceRefresh);
      if (userId != null && token != null) {
        await fetchAppointments();
        await fetchDaysWaiting(userId, token);
      }

      await Future.wait([
        fetchBlogs(forceRefresh: forceRefresh),
        fetchDonationEvents(forceRefresh: forceRefresh),
      ]);

      _isDataReady = true; // Đánh dấu dữ liệu đã sẵn sàng
    } catch (e) {
      print('AppStateNotifier: Error in fetchAllData: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
      print('AppStateNotifier: fetchAllData completed');
    }
  }

  Future<void> fetchAppointments() async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchAppointments');
      return;
    }

    print('AppStateNotifier: Starting fetchAppointments');

    try {
      _appointments = await _appointmentService.getAppointments() ?? [];
      print('AppStateNotifier: Successfully loaded ${_appointments.length} appointments');
    } catch (e) {
      print('AppStateNotifier: Error in fetchAppointments: $e');
      _appointments = [];
    }

    print('AppStateNotifier: fetchAppointments completed');
  }

  Future<void> fetchUserProfile({bool forceRefresh = true}) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchUserProfile');
      return;
    }

    print('AppStateNotifier: Starting fetchUserProfile (forceRefresh: $forceRefresh)');
    _isLoading = true;
    notifyListeners();

    try {
      final userId = await _userManager.getUserId();
      final token = await _userManager.getUserToken();

      if (userId != null && token != null) {
        _user = await _userService.getUserInfo(userId, token, forceRefresh: forceRefresh);
        await _userManager.syncUserData(_user!, token);
        _donationCount = _user!.donationCount ?? 0;

        print('AppStateNotifier: Successfully loaded user profile:');
        print('  - ID: ${_user!.id}');
        print('  - Name: "${_user!.name}"');
        print('  - Email: ${_user!.email}');
        print('  - UserImage: ${_user!.userImage}');
        print('  - Donation count: $_donationCount');
        print('  - Points: ${_user!.point}');
        print('  - Address: ${_user!.address}');
        print('  - BloodType: ${_user!.bloodType}');
      } else {
        print('AppStateNotifier: Missing userId or token');
      }
    } catch (e) {
      print('AppStateNotifier: Error in fetchUserProfile: $e');
    }

    _isLoading = false;
    notifyListeners();
    print('AppStateNotifier: fetchUserProfile completed');
  }

  Future<void> updateUserProfile({
    String? bloodType,
    String? name,
    String? dateOfBirth,
    String? address,
    String? job,
    int? gender,
    String? passportNumber,
    String? userImagePath,
  }) async {
    if (!await _checkConnectivity()) return;
    _isLoading = true;
    notifyListeners();
    try {
      final token = await _userManager.getUserToken();
      if (token != null) {
        final updatedUser = await _userService.updateUserProfileApi(
          token: token,
          bloodType: bloodType,
          name: name,
          dateOfBirth: dateOfBirth,
          address: address,
          job: job,
          gender: gender,
          passportNumber: passportNumber,
          userImagePath: userImagePath,
        );
        _user = updatedUser;
        await _userManager.syncUserData(_user!, token);
        _donationCount = updatedUser.donationCount ?? 0;
      }
    } catch (e) {
      throw e;
    }
    _isLoading = false;
    notifyListeners();
  }

  Future<void> refreshAfterAppointmentCompletion(String appointmentId, {File? certificationImage}) async {
    if (!await _checkConnectivity()) {
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      await _appointmentService.MarkcompleteAppointment(appointmentId, certificationImage: certificationImage);

      await Future.delayed(const Duration(seconds: 2));

      final userId = await _userManager.getUserId();
      final token = await _userManager.getUserToken();

      if (userId != null && token != null) {
        _user = await _userService.getUserInfo(userId, token, forceRefresh: true);
        await _userManager.syncUserData(_user!, token);

        final originalDonationCount = _donationCount;
        _donationCount = _user!.donationCount ?? 0;

        if (_donationCount == originalDonationCount && _user!.donationCount == null) {
          _donationCount = originalDonationCount + 1;

          _user = UserModel(
            id: _user!.id,
            name: _user!.name,
            email: _user!.email,
            userImage: _user!.userImage,
            bloodType: _user!.bloodType,
            job: _user!.job,
            dateOfBirth: _user!.dateOfBirth,
            donationCount: _donationCount,
            address: _user!.address,
            passportNumber: _user!.passportNumber,
            latitude: _user!.latitude,
            longitude: _user!.longitude,
            gender: _user!.gender,
            phoneNumber: _user!.phoneNumber,
            point: _user!.point,
          );

          await _userManager.syncUserData(_user!, token);
        }

        _daysWaiting = await _daysWaitingManager.forceRefreshDaysWaiting(userId, token);
        _appointments = await _appointmentService.getAppointments(forceRefresh: true) ?? [];
      }
    } catch (e) {
      throw e;
    }

    _isLoading = false;
    notifyListeners();
  }

}