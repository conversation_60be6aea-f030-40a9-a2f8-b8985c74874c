import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:flutter/material.dart';

class CustomTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final IconData? icon;
  final Widget? suffixIcon;
  final bool enabled;
  final int maxLines;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;

  const CustomTextField({
    Key? key,
    required this.controller,
    required this.label,
    this.icon,
    this.suffixIcon,
    this.enabled = true,
    this.maxLines = 1,
    this.validator,
    this.keyboardType,
  }) : super(key: key);

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              color: widget.enabled ? Colors.white : AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _isFocused
                    ? AppTheme.primaryRed
                    : (widget.enabled ? AppTheme.borderColor : AppTheme.borderColor.withOpacity(0.5)),
                width: _isFocused ? 2 : 1,
              ),
              boxShadow: _isFocused
                  ? [
                BoxShadow(
                  color: AppTheme.primaryRed.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
                  : [],
            ),
            child: TextFormField(
              controller: widget.controller,
              enabled: widget.enabled,
              maxLines: widget.maxLines,
              keyboardType: widget.keyboardType,
              style: widget.enabled
                  ? AppTheme.bodyLarge
                  : AppTheme.bodyMedium,
              validator: widget.validator,
              onTap: () {
                if (widget.enabled) {
                  setState(() => _isFocused = true);
                  _animationController.forward();
                }
              },
              onTapOutside: (_) {
                setState(() => _isFocused = false);
                _animationController.reverse();
              },
              onEditingComplete: () {
                setState(() => _isFocused = false);
                _animationController.reverse();
              },
              decoration: InputDecoration(
                labelText: widget.label,
                labelStyle: widget.enabled
                    ? (_isFocused
                    ? AppTheme.bodyMedium.copyWith(color: AppTheme.primaryRed)
                    : AppTheme.bodyMedium)
                    : AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary.withOpacity(0.7)),
                prefixIcon: widget.icon != null
                    ? Padding(
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    widget.icon,
                    color: _isFocused
                        ? AppTheme.primaryRed
                        : (widget.enabled ? AppTheme.textSecondary : AppTheme.textSecondary.withOpacity(0.5)),
                    size: 20,
                  ),
                )
                    : null,
                suffixIcon: widget.suffixIcon,
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: widget.icon != null ? 0 : 16,
                  vertical: widget.maxLines == 1 ? 16 : 12,
                ),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
              ),
            ),
          ),
        );
      },
    );
  }
}