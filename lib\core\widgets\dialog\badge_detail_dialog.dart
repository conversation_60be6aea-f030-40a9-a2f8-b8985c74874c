import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math';

class BadgeDetailDialog extends StatelessWidget {
  final String badgeName;
  final String badgeDescription;
  final IconData badgeIcon;
  final List<Color> badgeGradient;
  final Color badgeColor;
  final int donationCount;
  final int? nextBadgeDonations;
  final String? nextBadgeName;
  final IconData? nextBadgeIcon;
  final Color? nextBadgeColor;

  const BadgeDetailDialog({
    Key? key,
    required this.badgeName,
    required this.badgeDescription,
    required this.badgeIcon,
    required this.badgeGradient,
    required this.badgeColor,
    required this.donationCount,
    this.nextBadgeDonations,
    this.nextBadgeName,
    this.nextBadgeIcon,
    this.nextBadgeColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing
    final dialogWidth = min(screenSize.width * 0.9, 400.0);
    final dialogMaxHeight = screenSize.height * 0.8;
    final padding = isSmallScreen ? 16.0 : (isLargeScreen ? 28.0 : 24.0);
    final iconSize = isSmallScreen ? 40.0 : (isLargeScreen ? 60.0 : 50.0);
    final badgeContainerSize = isSmallScreen ? 70.0 : (isLargeScreen ? 120.0 : 100.0);
    final titleFontSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final donationFontSize = isSmallScreen ? 16.0 : (isLargeScreen ? 20.0 : 18.0);
    final descriptionFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final buttonHeight = isSmallScreen ? 48.0 : (isLargeScreen ? 60.0 : 54.0);
    final spacing = isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 12.0 : 16.0,
        vertical: isSmallScreen ? 12.0 : 24.0,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: dialogWidth,
          maxHeight: dialogMaxHeight,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey[900]!.withOpacity(0.95),
              Colors.grey[800]!.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: isSmallScreen ? 0.5 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.lightBlue.withOpacity(0.3),
              blurRadius: isSmallScreen ? 20 : 30,
              offset: const Offset(0, 15),
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Badge Icon
                Container(
                  width: badgeContainerSize,
                  height: badgeContainerSize,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: badgeGradient,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: badgeColor.withOpacity(0.3),
                        blurRadius: isSmallScreen ? 12 : 20,
                        spreadRadius: isSmallScreen ? 3 : 5,
                      ),
                    ],
                  ),
                  child: Icon(
                    badgeIcon,
                    color: Colors.white,
                    size: iconSize,
                  ),
                ),
                SizedBox(height: spacing),
                // Badge Title
                Text(
                  localizations.translate(badgeName),
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: spacing),
                // Donation Count
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 20,
                    vertical: isSmallScreen ? 6 : 10,
                  ),
                  decoration: BoxDecoration(
                    color: badgeColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                    border: Border.all(
                      color: badgeColor.withOpacity(0.3),
                      width: isSmallScreen ? 0.5 : 1,
                    ),
                  ),
                  child: Text(
                    localizations.translate('donation_count_with_times').replaceAll('{count}', donationCount.toString()),
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: donationFontSize,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: spacing),
                // Description
                Text(
                  badgeDescription,
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: descriptionFontSize,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: spacing * 2),
                // Progress to next badge
                if (nextBadgeDonations != null && nextBadgeName != null)
                  _buildProgressToNextBadge(context),
                SizedBox(height: spacing * 2),
                // Close button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: badgeColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 12 : 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                      ),
                      elevation: isSmallScreen ? 2 : 4,
                    ),
                    child: Text(
                      localizations.translate('ok'),
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontSize: isSmallScreen ? 14 : (isLargeScreen ? 18 : 16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressToNextBadge(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing
    final padding = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final iconSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final titleFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final spacing = isSmallScreen ? 4.0 : (isLargeScreen ? 10.0 : 8.0);

    if (nextBadgeDonations == null || nextBadgeName == null) {
      return Container(
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.2),
          borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
          border: Border.all(
            color: Colors.green.withOpacity(0.3),
            width: isSmallScreen ? 0.5 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.emoji_events,
              color: Colors.green,
              size: iconSize,
            ),
            SizedBox(width: spacing),
            Expanded(
              child: Text(
                localizations.translate('all_badges_achieved') ?? 'You have achieved all badges!',
                style: GoogleFonts.poppins(
                  color: Colors.green,
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    final remaining = nextBadgeDonations! - donationCount;
    final progress = donationCount / nextBadgeDonations!;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: isSmallScreen ? 0.5 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.translate('progress_to_next_badge') ?? 'Progress to next badge:',
            style: GoogleFonts.poppins(
              color: Colors.white.withOpacity(0.8),
              fontSize: titleFontSize,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: spacing),
          Row(
            children: [
              Icon(
                nextBadgeIcon ?? Icons.star,
                color: nextBadgeColor ?? Colors.amber,
                size: iconSize,
              ),
              SizedBox(width: spacing),
              Expanded(
                child: Text(
                  localizations.translate(nextBadgeName!),
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: spacing),
          LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: Colors.white.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(nextBadgeColor ?? Colors.amber),
            minHeight: isSmallScreen ? 4 : 6,
          ),
          SizedBox(height: spacing),
          Text(
            localizations.translate('remaining_donations').replaceAll('{count}', remaining.toString()),
            style: GoogleFonts.poppins(
              color: Colors.white.withOpacity(0.7),
              fontSize: isSmallScreen ? 10 : (isLargeScreen ? 14 : 12),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}