import 'package:flutter/material.dart';

class CustomButton extends StatefulWidget {
  final String text;
  final Color color;
  final Color textColor;
  final VoidCallback? onPressed;
  final EdgeInsets padding;
  final double borderRadius;
  final bool isLoading;
  final bool isDisabled;
  final IconData? icon;
  final double? width;
  final double height;
  final TextStyle? textStyle;
  final int? maxLines;
  final double? minWidth;

  const CustomButton({
    Key? key,
    required this.text,
    required this.color,
    this.textColor = Colors.white,
    required this.onPressed,
    this.padding = const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
    this.borderRadius = 16,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.height = 56,
    this.textStyle,
    this.maxLines,
    this.minWidth,
  }) : super(key: key);

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _controller.reverse();
      }
    });
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = widget.isDisabled || widget.isLoading;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    final fontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final iconSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final shadowBlurRadius = isSmallScreen ? 8.0 : (isLargeScreen ? 20.0 : 16.0);
    final shadowOffset = isSmallScreen ? 4.0 : (isLargeScreen ? 10.0 : 8.0);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTapDown: isDisabled ? null : (_) {
            setState(() => _isPressed = true);
            _controller.forward();
          },
          onTapUp: isDisabled ? null : (_) {
            setState(() => _isPressed = false);
            if (widget.onPressed != null) widget.onPressed!();
          },
          onTapCancel: isDisabled ? null : () {
            setState(() => _isPressed = false);
            _controller.reverse();
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: widget.width,
            height: widget.height,
            padding: widget.padding,
            constraints: BoxConstraints(minWidth: widget.minWidth ?? 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              gradient: LinearGradient(
                colors: isDisabled
                    ? [Colors.grey.shade300, Colors.grey.shade300]
                    : [
                  widget.color,
                  widget.color.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: isDisabled
                  ? null
                  : [
                BoxShadow(
                  color: widget.color.withOpacity(0.3),
                  blurRadius: _isPressed ? shadowBlurRadius / 2 : shadowBlurRadius,
                  offset: Offset(0, _isPressed ? shadowOffset / 2 : shadowOffset),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Center(
              child: widget.isLoading
                  ? SizedBox(
                width: iconSize,
                height: iconSize,
                child: CircularProgressIndicator(
                  color: widget.textColor,
                  strokeWidth: isSmallScreen ? 2 : 2.5,
                ),
              )
                  : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.icon != null)
                    Icon(
                      widget.icon,
                      color: isDisabled ? Colors.grey.shade600 : widget.textColor,
                      size: iconSize,
                    ),
                  if (widget.icon != null) const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      widget.text,
                      style: widget.textStyle ??
                          TextStyle(
                            color: isDisabled ? Colors.grey.shade600 : widget.textColor,
                            fontSize: fontSize,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                      maxLines: widget.maxLines ?? 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}