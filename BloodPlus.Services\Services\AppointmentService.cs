﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.AppointmentModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class AppointmentService : IAppointmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly FirebaseService _firebaseService;
        private readonly OneSignalService _oneSignalService;

        public AppointmentService(IUnitOfWork unitOfWork, IMapper mapper, FirebaseService firebaseService, OneSignalService oneSignalService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _firebaseService = firebaseService;
            _oneSignalService = oneSignalService;
        }


        public async Task<string> CreateAppointmentAsync(CreateAppointmentModelView model, string userId)
        {
            try
            {
                Guid uId = Guid.Parse(userId);

                var existingPendingAppointment = await _unitOfWork.GetRepository<Appointment>()
                    .Entities
                    .Where(a => a.UserId == uId 
                           && (a.Status == Core.Enum.AppointmentStatus.Pending || a.Status == Core.Enum.AppointmentStatus.NeedConfirm) 
                           && !a.DeletedTime.HasValue)
                    .AnyAsync();

                if (existingPendingAppointment)
                {
                    throw new Exception("You already have a pending appointment. Please complete or cancel or wating confirm it before creating a new one.");
                }

                var lastDonation = await _unitOfWork.GetRepository<Appointment>()
                    .Entities
                    .Where(a => a.UserId == uId && a.Status == Core.Enum.AppointmentStatus.Completed)
                    .OrderByDescending(a => a.LastUpdatedTime)
                    .FirstOrDefaultAsync();

                if (lastDonation != null)
                {
                    var nextEligibleDate = lastDonation.LastUpdatedTime;

                    switch (model.BloodComponent)
                    {
                        case Core.Enum.BloodComponent.WholeBlood:
                            nextEligibleDate = nextEligibleDate.AddDays(84); 
                            break;
                        case Core.Enum.BloodComponent.RedBloodCells:
                            nextEligibleDate = nextEligibleDate.AddDays(84);
                            break;
                        case Core.Enum.BloodComponent.Platelets:
                            nextEligibleDate = nextEligibleDate.AddDays(14);
                            break;
                        case Core.Enum.BloodComponent.Plasma:
                            nextEligibleDate = nextEligibleDate.AddDays(14);
                            break;
                        case Core.Enum.BloodComponent.WhiteBloodCells:
                            nextEligibleDate = nextEligibleDate.AddDays(7);
                            break;
                        default:
                            throw new Exception("Invalid blood component type");
                    }

                    if (DateTime.UtcNow < nextEligibleDate)
                    {
                        throw new Exception ($"You can donate this blood component again after {nextEligibleDate:dd/MM/yyyy}");
                    }
                }

                Appointment appointment = new Appointment
                {
                    UserId = uId,
                    DonationEventId = model.EventId,
                    Status = Core.Enum.AppointmentStatus.Pending,
                    BloodComponent = model.BloodComponent,
                    CreatedBy = userId,
                    CreatedTime = DateTime.UtcNow,
                    IsEmergency = false,

                };

                RegistrationForm regisForm = new RegistrationForm
                {
                    AppointmentId = appointment.Id,
                    UserId = appointment.UserId,
                    HasDonatedBefore = model.HasDonatedBefore,
                    HasDiseases = model.HasDiseases,
                    DiseaseDetails = model.DiseaseDetails,
                    IsTakingMedicine = model.IsTakingMedicine,
                    MedicineDetails = model.MedicineDetails,
                    Symptoms = model.Symptoms,
                    RiskBehavior = model.RiskBehavior,
                    TravelHistory = model.TravelHistory,    
                    TattooOrSurgery = model.TattooOrSurgery,
                    WeightOver45kg = model.WeightOver45kg,
                    Notes = model.Notes,
                    HasPreviousInfections = model.HasPreviousInfections,
                    PreviousInfectionsDetails = model.PreviousInfectionsDetails,
                    HadRecentIllness12Months = model.HadRecentIllness12Months,
                    RecentIllness12MonthsDetails = model.RecentIllness12MonthsDetails,
                    HadRecentIllness6Months = model.HadRecentIllness6Months,
                    HadRecentIllness1Month = model.HadRecentIllness1Month,
                    HadRecentIllness14Days = model.HadRecentIllness14Days,
                    RecentIllness14DaysDetails = model.RecentIllness14DaysDetails,
                    UsedAntibiotics7Days = model.UsedAntibiotics7Days,
                    AntibioticsDetails = model.AntibioticsDetails,
                    IsPregnantOrRecentMother = model.IsPregnantOrRecentMother,
                    PregnancyDetails = model.PregnancyDetails,
                    CreatedBy = userId,
                    CreatedTime = DateTime.Now,
                };

                var donationEvent = await _unitOfWork.GetRepository<DonationEvent>()
                    .Entities
                    .FirstOrDefaultAsync(o => o.Id == model.EventId && !o.DeletedTime.HasValue);
                donationEvent.CurrentDonors++;

                await _unitOfWork.GetRepository<Appointment>().InsertAsync(appointment);
                await _unitOfWork.GetRepository<DonationEvent>().UpdateAsync(donationEvent);
                await _unitOfWork.GetRepository<RegistrationForm>().InsertAsync(regisForm);
                await _unitOfWork.SaveAsync();

                return "Appointment added successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<string> MarkCancelAppointmentAsync(string id, string userId)
        {
            try
            {
                var appointment = await _unitOfWork.GetRepository<Appointment>().GetByIdAsync(id);
                if (appointment == null || appointment.DeletedTime.HasValue || appointment.Status == Core.Enum.AppointmentStatus.Canceled
                    || appointment.Status == Core.Enum.AppointmentStatus.Completed)
                {
                    throw new Exception("can not find Appointment or deleted or canceled");
                }

                var donationEvent = await _unitOfWork.GetRepository<DonationEvent>()
                    .Entities
                    .FirstOrDefaultAsync(o => o.Id == appointment.DonationEventId && !o.DeletedTime.HasValue);
                donationEvent.CurrentDonors--;

                var regisForm = await _unitOfWork.GetRepository<RegistrationForm>()
                    .Entities
                    .FirstOrDefaultAsync(r => r.AppointmentId == appointment.Id);
                regisForm.DeletedTime = DateTime.Now;
                regisForm.DeletedBy = userId;

                appointment.Status = Core.Enum.AppointmentStatus.Canceled;
                appointment.LastUpdatedTime = DateTime.Now;
                appointment.LastUpdatedBy = userId;

                await _unitOfWork.GetRepository<Appointment>().UpdateAsync(appointment);
                await _unitOfWork.GetRepository<DonationEvent>().UpdateAsync(donationEvent);
                await _unitOfWork.GetRepository<RegistrationForm>().UpdateAsync(regisForm);
                await _unitOfWork.SaveAsync();

                return "Appointment canceled";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<string> MarkCompletedAppointmentAsync(string id, string userId)
        {
            try
            {
                var appointment = await _unitOfWork.GetRepository<Appointment>().GetByIdAsync(id);
                if (appointment == null || appointment.DeletedTime.HasValue || appointment.Status == Core.Enum.AppointmentStatus.Canceled)
                {
                    throw new Exception("can not find Appointment or deleted or canceled");
                }

                var user = await _unitOfWork.GetRepository<User>().GetByIdAsync(appointment.UserId);
                user.DonationCount++;

                // Tính điểm theo thành phần máu
                int rewardPoint = appointment.BloodComponent switch
                {
                    Core.Enum.BloodComponent.WholeBlood => 200,
                    Core.Enum.BloodComponent.RedBloodCells => 200,
                    Core.Enum.BloodComponent.Platelets => 80,
                    Core.Enum.BloodComponent.Plasma => 80,
                    Core.Enum.BloodComponent.WhiteBloodCells => 30,
                    _ => throw new Exception("Invalid blood component type"),
                };
                user.Point += rewardPoint;

                appointment.Status = Core.Enum.AppointmentStatus.Completed;
                appointment.LastUpdatedTime = DateTime.Now;
                appointment.LastUpdatedBy = userId;

                var noti = await _oneSignalService.SendNotificationAsync("Máu+ thông báo",
                                        $"Chứng nhận hiến máu của bạn đã được duyệt, bạn nhận được {rewardPoint} điểm thưởng!",
                                        userId,
                                        user.DeviceToken);
                if (noti)
                {
                    Notification notification = new()
                    {
                        UserId = user.Id,
                        Title = "Máu+ thông báo",
                        Content = $"Chứng nhận hiến máu của bạn đã được duyệt, bạn nhận được {rewardPoint} điểm thưởng!",
                        IsRead = false,
                        SentDate = DateTime.Now,
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now,
                    };
                    await _unitOfWork.GetRepository<Notification>().InsertAsync(notification);
                }

                await _unitOfWork.GetRepository<Appointment>().UpdateAsync(appointment);
                await _unitOfWork.GetRepository<User>().UpdateAsync(user);
                await _unitOfWork.SaveAsync();

                return "Appointment completed";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<string> MarkNeedConfirmAppointmentAsync(MarkCompletedModelView model, string userId)
        {
            try
            {
                var appointment = await _unitOfWork.GetRepository<Appointment>().GetByIdAsync(model.Id);
                if (appointment == null || appointment.DeletedTime.HasValue)
                {
                    throw new Exception("can not find Appointment or deleted");
                }

                appointment.Status = Core.Enum.AppointmentStatus.NeedConfirm;
                appointment.Certification = await _firebaseService.UploadImageAsync(model.Certification);
                appointment.LastUpdatedTime = DateTime.Now;
                appointment.LastUpdatedBy = userId;

                await _unitOfWork.GetRepository<Appointment>().UpdateAsync(appointment);
                await _unitOfWork.SaveAsync();

                return "Appointment need confirm !";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<string> DeleteAppointmentAsync(string id, string userId)
        {
            try
            {
                var appointment = await _unitOfWork.GetRepository<Appointment>().GetByIdAsync(id);
                if (appointment == null || appointment.DeletedTime.HasValue)
                {
                    throw new Exception("can not find Appointment or deleted");
                }

                appointment.DeletedTime = DateTimeOffset.UtcNow;
                appointment.DeletedBy = userId;

                var regisForm = await _unitOfWork.GetRepository<RegistrationForm>()
                    .Entities
                    .FirstOrDefaultAsync(r => r.AppointmentId == appointment.Id);
                regisForm.DeletedTime = DateTime.Now;
                regisForm.DeletedBy = userId;

                await _unitOfWork.GetRepository<Appointment>().UpdateAsync(appointment);
                await _unitOfWork.GetRepository<RegistrationForm>().UpdateAsync(regisForm);
                await _unitOfWork.SaveAsync();

                return "Appointment delete successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<AppointmentModelView> GetAppointmentByIdAsync(string id)
        {
            try
            {
                var appointment = await _unitOfWork.GetRepository<Appointment>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == id && !u.DeletedTime.HasValue);

                if (appointment == null)
                {
                    throw new Exception("can not find or Appointment is deleted");
                }

                AppointmentModelView appointmentModelView = new AppointmentModelView
                {
                    EventName = appointment.DonationEvent.Title,
                    OrganizationName = appointment.DonationEvent.Organization.Name,
                    Location = appointment.DonationEvent.Location,
                    AppointmentDate = appointment.DonationEvent.EventDate,
                    Status = appointment.Status,
                    BloodComponent = appointment.BloodComponent,
                    Certification = appointment.Certification,
                };

                return appointmentModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BasePaginatedList<ListAppointmentForUser>> GetAllAppointmentAsync(int pageNumber, int pageSize, string apmUserId)
        {
            var userId = Guid.Parse(apmUserId);

            IQueryable<Appointment> appointmentQuery = _unitOfWork.GetRepository<Appointment>()
                .Entities
                .Where(p => !p.DeletedTime.HasValue && p.UserId == userId)
                .OrderByDescending(s => s.CreatedTime);

            int totalCount = await appointmentQuery.CountAsync();

            var result = await appointmentQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(p => new ListAppointmentForUser
                {
                    Id = p.Id,
                    EventName = p.DonationEvent.Title,
                    OrganizationName = p.DonationEvent.Organization.Name,
                    Location = p.DonationEvent.Location,
                    AppointmentDate = p.DonationEvent.EventDate,
                    Status = p.Status,
                    BloodComponent = p.BloodComponent,
                    IsEmergency = p.IsEmergency
                })
                .ToListAsync();

            return new BasePaginatedList<ListAppointmentForUser>(result, totalCount, pageNumber, pageSize);
        }


        public async Task<BasePaginatedList<ListAppointmentForAdmin>> GetAllAppointmentForAdminAsync(int pageNumber, int pageSize)
        {
            IQueryable<Appointment> appointmentQuery = _unitOfWork.GetRepository<Appointment>()
                .Entities
                .Where(p => !p.DeletedTime.HasValue)
                .OrderByDescending(s => s.CreatedTime);

            int totalCount = await appointmentQuery.CountAsync();

            var result = await appointmentQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(p => new ListAppointmentForAdmin
                {
                    Id = p.Id,
                    EventName = p.DonationEvent.Title,
                    OrganizationName = p.DonationEvent.Organization.Name,
                    Location = p.DonationEvent.Location,
                    AppointmentDate = p.DonationEvent.EventDate,
                    Status = p.Status,
                    BloodComponent = p.BloodComponent,
                    IsEmergency = p.IsEmergency,
                    Email = p.User.Email
                })
                .ToListAsync();

            return new BasePaginatedList<ListAppointmentForAdmin>(result, totalCount, pageNumber, pageSize);
        }

        public async Task<string> CreateEmergencyAppointmentAsync(EmergencyAppointmentCreateModelView model, string userId)
        {
            try
            {
                Guid uId = Guid.Parse(userId);
                // Kiểm tra xem người dùng đã có lịch hẹn chưa
                var existingPendingAppointment = await _unitOfWork.GetRepository<Appointment>()
                    .Entities
                    .AnyAsync(a => a.UserId == uId &&
                                   (a.Status == AppointmentStatus.Pending || a.Status == AppointmentStatus.NeedConfirm) &&
                                   !a.DeletedTime.HasValue);

                if (existingPendingAppointment)
                {
                    throw new Exception("Người dùng đã có lịch hẹn chờ xác nhận hoặc chưa hoàn thành.");
                }

                // Tìm sự kiện hiến máu
                var donationEvent = await _unitOfWork.GetRepository<DonationEvent>()
                    .Entities
                    .FirstOrDefaultAsync(e => e.Id == model.DonationEventId && !e.DeletedTime.HasValue && e.RequiredBloodType == model.BloodTypeId);

                if (donationEvent == null)
                {
                    throw new Exception("Không tìm thấy sự kiện hiến máu khẩn cấp.");
                }

                // Tạo appointment mới
                var appointment = new Appointment
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = uId,
                    DonationEventId = model.DonationEventId,
                    Status = AppointmentStatus.Pending,
                    BloodComponent = model.BloodComponent,
                    CreatedBy = userId,
                    CreatedTime = DateTime.UtcNow,
                    BloodType = model.BloodTypeId,
                    Location = model.Location,
                    Email = model.Email,
                    IsEmergency = true,
                    PhoneNumber = model.PhoneNumber

                };

                // Tăng số người đăng ký sự kiện
                donationEvent.CurrentDonors++;

                // Lưu vào database
                await _unitOfWork.GetRepository<Appointment>().InsertAsync(appointment);
                await _unitOfWork.GetRepository<DonationEvent>().UpdateAsync(donationEvent);
                await _unitOfWork.SaveAsync();

                return "Đăng ký hiến máu khẩn cấp thành công!";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi tạo lịch hẹn hiến máu khẩn cấp: {ex.Message}");
            }
        }

    }
}
