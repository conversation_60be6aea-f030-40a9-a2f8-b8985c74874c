import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:bloodplusmobile/data/models/blog_model.dart';
import 'package:bloodplusmobile/data/services/blog_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';

class BlogDetailScreen extends StatefulWidget {
  final String blogId;

  const BlogDetailScreen({super.key, required this.blogId});

  @override
  _BlogDetailScreenState createState() => _BlogDetailScreenState();
}

class _BlogDetailScreenState extends State<BlogDetailScreen> with TickerProviderStateMixin {
  final BlogService _blogService = BlogService();
  BlogModel? blog;
  bool isLoading = true;
  String? errorMessage;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  bool isLiked = false;
  bool isBookmarked = false;
  final ScrollController _scrollController = ScrollController();
  double _appBarOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScrollListener();
    fetchBlog();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      final offset = _scrollController.offset;
      final maxOffset = MediaQuery.of(context).size.height * 0.25; // Responsive max offset
      setState(() {
        _appBarOpacity = (offset / maxOffset).clamp(0.0, 1.0);
      });
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> fetchBlog() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final fetchedBlog = await _blogService.getBlogById(widget.blogId);
      setState(() {
        blog = fetchedBlog;
        isLoading = false;
      });

      _fadeController.forward();
      _slideController.forward();
      _scaleController.forward();
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  String _getTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    final localizations = AppLocalizations.of(context);

    if (difference.inMinutes < 1) {
      return localizations.translate('just_now')!;
    } else if (difference.inMinutes < 60) {
      return localizations.translate('minutes_ago')!.replaceAll('{minutes}', difference.inMinutes.toString());
    } else if (difference.inHours < 24) {
      return localizations.translate('hours_ago')!.replaceAll('{hours}', difference.inHours.toString());
    } else if (difference.inDays < 7) {
      return localizations.translate('days_ago')!.replaceAll('{days}', difference.inDays.toString());
    }
    return DateFormat('dd MMM yyyy').format(date);
  }

  Widget _buildLoadingScreen(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(screenWidth * 0.075),
              decoration: BoxDecoration( // Changed from Decoration to BoxDecoration
                color: Colors.white,
                borderRadius: BorderRadius.circular(screenWidth * 0.05),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: screenWidth * 0.05,
                    offset: Offset(0, screenWidth * 0.025),
                  ),
                ],
              ),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryRed),
                strokeWidth: 3,
              ),
            ),
            SizedBox(height: screenHeight * 0.03),
            Text(
              localizations.translate('loading_article')!,
              style: AppTheme.bodyLarge.copyWith(
                color: Colors.grey[600],
                fontSize: screenWidth * 0.04,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildCustomAppBar(title: localizations.translate('article_detail'), context: context),
      body: Center(
        child: Container(
          margin: EdgeInsets.all(screenWidth * 0.05),
          padding: EdgeInsets.all(screenWidth * 0.075),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.05),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: screenWidth * 0.05,
                offset: Offset(0, screenWidth * 0.025),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(screenWidth * 0.05),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(screenWidth * 0.125),
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  color: Colors.red,
                  size: screenWidth * 0.125,
                ),
              ),
              SizedBox(height: screenHeight * 0.03),
              Text(
                localizations.translate('cannot_load_article')!,
                style: AppTheme.headingMedium.copyWith(
                  color: Colors.red,
                  fontSize: screenWidth * 0.045,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: screenHeight * 0.015),
              Text(
                localizations.translate('check_connection_try_again')!,
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.grey[600],
                  fontSize: screenWidth * 0.035,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: screenHeight * 0.03),
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: localizations.translate('try_again'),
                      color: AppColors.primaryRed,
                      textColor: Colors.white,
                      onPressed: fetchBlog,
                      padding: EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                      borderRadius: screenWidth * 0.03,
                    ),
                  ),
                  SizedBox(width: screenWidth * 0.04),
                  Expanded(
                    child: CustomButton(
                      text: localizations.translate('go_back'),
                      color: Colors.grey[300]!,
                      textColor: Colors.grey[700]!,
                      onPressed: () => NavigationService.goBack(),
                      padding: EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                      borderRadius: screenWidth * 0.03,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildCustomAppBar({String? title, required BuildContext context}) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;

    return AppBar(
      backgroundColor: Colors.white.withOpacity(_appBarOpacity),
      elevation: _appBarOpacity > 0.5 ? 4 : 0,
      leading: Container(
        margin: EdgeInsets.all(screenWidth * 0.02),
        decoration: BoxDecoration(
          color: _appBarOpacity > 0.5 ? Colors.transparent : Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(screenWidth * 0.03),
        ),
        child: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: _appBarOpacity > 0.5 ? Colors.black : Colors.white,
            size: screenWidth * 0.06,
          ),
          onPressed: () => NavigationService.goBack(),
        ),
      ),
      title: AnimatedOpacity(
        opacity: _appBarOpacity,
        duration: const Duration(milliseconds: 200),
        child: Text(
          title ?? (blog?.title ?? ''),
          style: AppTheme.headingMedium.copyWith(
            color: Colors.black,
            fontSize: screenWidth * 0.045,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      actions: [
        Container(
          margin: EdgeInsets.all(screenWidth * 0.02),
          decoration: BoxDecoration(
            color: _appBarOpacity > 0.5 ? Colors.transparent : Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
          ),
          child: IconButton(
            icon: Icon(
              isBookmarked ? Icons.bookmark_rounded : Icons.bookmark_border_rounded,
              color: _appBarOpacity > 0.5
                  ? (isBookmarked ? AppColors.primaryRed : Colors.black)
                  : Colors.white,
              size: screenWidth * 0.06,
            ),
            onPressed: () {
              setState(() {
                isBookmarked = !isBookmarked;
              });
            },
          ),
        ),
        Container(
          margin: EdgeInsets.all(screenWidth * 0.02),
          decoration: BoxDecoration(
            color: _appBarOpacity > 0.5 ? Colors.transparent : Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
          ),
          child: IconButton(
            icon: Icon(
              Icons.share_rounded,
              color: _appBarOpacity > 0.5 ? Colors.black : Colors.white,
              size: screenWidth * 0.06,
            ),
            onPressed: () {
              // Share functionality
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHeroImage(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    if (blog?.image1 == null) {
      return Container(
        height: screenHeight * 0.35,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.grey[200]!, Colors.grey[100]!],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported_rounded,
              size: screenWidth * 0.15,
              color: Colors.grey[400],
            ),
            SizedBox(height: screenHeight * 0.015),
            Text(
              localizations.translate('no_image')!,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: screenWidth * 0.04,
              ),
            ),
          ],
        ),
      );
    }

    return Hero(
      tag: 'blog_image_${blog!.id}',
      child: Container(
        height: screenHeight * 0.35,
        width: double.infinity,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.network(
              blog!.image1!,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.grey[200]!, Colors.grey[100]!],
                    ),
                  ),
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                          (loadingProgress.expectedTotalBytes ?? 1)
                          : null,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryRed),
                      strokeWidth: 2,
                    ),
                  ),
                );
              },
              errorBuilder: (_, __, ___) => Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.grey[200]!, Colors.grey[100]!],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image_rounded,
                      size: screenWidth * 0.15,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: screenHeight * 0.015),
                    Text(
                      localizations.translate('cannot_load_image')!,
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: screenWidth * 0.04,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.black.withOpacity(0.6),
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withOpacity(0.3),
                  ],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetaInfo(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.05,
        vertical: screenHeight * 0.02,
      ),
      child: Row(
        children: [
          if (blog!.createdTime != null) ...[
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.03,
                vertical: screenHeight * 0.01,
              ),
              decoration: BoxDecoration(
                color: AppColors.primaryRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(screenWidth * 0.05),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.schedule_rounded,
                    size: screenWidth * 0.04,
                    color: AppColors.primaryRed,
                  ),
                  SizedBox(width: screenWidth * 0.015),
                  Text(
                    _getTimeAgo(blog!.createdTime!),
                    style: TextStyle(
                      color: AppColors.primaryRed,
                      fontSize: screenWidth * 0.0325,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: screenWidth * 0.025),
          ],
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.03,
              vertical: screenHeight * 0.01,
            ),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(screenWidth * 0.05),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.visibility_rounded,
                  size: screenWidth * 0.04,
                  color: Colors.blue[300],
                ),
                SizedBox(width: screenWidth * 0.015),
                Text(
                  '${blog!.viewNumber} Views',
                  style: TextStyle(
                    color: Colors.blue[600],
                    fontSize: screenWidth * 0.0325,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentSection(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    final imageList = [blog!.image1, blog!.image2, blog!.image3, blog!.image4]
        .whereType<String>()
        .toList();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.05),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.05),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: screenWidth * 0.05,
            offset: Offset(0, screenWidth * 0.02),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AutoSizeText(
                  blog!.title,
                  style: AppTheme.headingLarge.copyWith(
                    fontSize: screenWidth * 0.06,
                    fontWeight: FontWeight.bold,
                    height: 1.3,
                    color: Colors.black87,
                  ),
                  maxLines: 3,
                ),
                SizedBox(height: screenHeight * 0.02),
                Container(
                  width: screenWidth * 0.125,
                  height: 4,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primaryRed, AppColors.primaryRed.withOpacity(0.6)],
                    ),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          ),

          // Description
          if (blog!.description.isNotEmpty)
            Container(
              margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.05),
              padding: EdgeInsets.all(screenWidth * 0.05),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(screenWidth * 0.04),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: AutoSizeText(
                blog!.description,
                style: AppTheme.bodyLarge.copyWith(
                  height: 1.6,
                  fontSize: screenWidth * 0.04,
                  color: Colors.grey[700],
                ),
              ),
            ),

          SizedBox(height: screenHeight * 0.03),

          // Content with images
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.05),
            child: Column(
              children: [
                if (blog!.content.isNotEmpty) ...[
                  _buildContentWithImages(imageList, context),
                ] else if (imageList.length > 1) ...[
                  for (int i = 1; i < imageList.length; i++) ...[
                    _buildImageSection(imageList[i], context),
                    if (i < imageList.length - 1) SizedBox(height: screenHeight * 0.03),
                  ],
                ],
              ],
            ),
          ),

          SizedBox(height: screenHeight * 0.04),
        ],
      ),
    );
  }

  Widget _buildContentWithImages(List<String> imageList, BuildContext context) {
    if (blog!.content.isEmpty) return Container();
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    final contentParts = _splitContent(blog!.content, imageList.length - 1);

    return Column(
      children: [
        for (int i = 0; i < contentParts.length; i++) ...[
          if (contentParts[i].isNotEmpty)
            Container(
              padding: EdgeInsets.all(screenWidth * 0.05),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(screenWidth * 0.04),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: AutoSizeText(
                contentParts[i],
                style: AppTheme.bodyMedium.copyWith(
                  height: 1.7,
                  fontSize: screenWidth * 0.0375,
                  color: Colors.black87,
                ),
              ),
            ),
          if (i < imageList.length - 1 && i + 1 < imageList.length) ...[
            SizedBox(height: screenHeight * 0.03),
            _buildImageSection(imageList[i + 1], context),
            SizedBox(height: screenHeight * 0.03),
          ],
        ],
      ],
    );
  }

  List<String> _splitContent(String content, int parts) {
    if (parts <= 0) return [content];

    final words = content.split(' ');
    final wordsPerPart = (words.length / (parts + 1)).ceil();

    List<String> result = [];
    for (int i = 0; i <= parts; i++) {
      final start = i * wordsPerPart;
      final end = ((i + 1) * wordsPerPart).clamp(0, words.length);
      if (start < words.length) {
        result.add(words.sublist(start, end).join(' '));
      }
    }
    return result;
  }

  Widget _buildImageSection(String imageUrl, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return ClipRRect(
      borderRadius: BorderRadius.circular(screenWidth * 0.04),
      child: Image.network(
        imageUrl,
        width: double.infinity,
        height: screenHeight * 0.3,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            height: screenHeight * 0.3,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.grey[200]!, Colors.grey[100]!],
              ),
            ),
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                    (loadingProgress.expectedTotalBytes ?? 1)
                    : null,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryRed),
                strokeWidth: 2,
              ),
            ),
          );
        },
        errorBuilder: (_, __, ___) => Container(
          height: screenHeight * 0.3,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey[200]!, Colors.grey[100]!],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.broken_image_rounded,
                size: screenWidth * 0.1,
                color: Colors.grey[400],
              ),
              SizedBox(height: screenHeight * 0.01),
              Text(
                localizations.translate('cannot_load_image')!,
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: screenWidth * 0.035,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      margin: EdgeInsets.all(screenWidth * 0.05),
      padding: EdgeInsets.all(screenWidth * 0.05),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.05),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: screenWidth * 0.05,
            offset: Offset(0, screenWidth * 0.02),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    isLiked = !isLiked;
                  });
                },
                icon: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    isLiked ? Icons.favorite_rounded : Icons.favorite_border_rounded,
                    key: ValueKey(isLiked),
                    color: Colors.white,
                    size: screenWidth * 0.05,
                  ),
                ),
                label: Text(
                  isLiked ? 'Like' : '...',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: screenWidth * 0.035,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isLiked ? AppColors.primaryRed : Colors.grey[400],
                  padding: EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                  elevation: isLiked ? 8 : 2,
                  shadowColor: isLiked ? AppColors.primaryRed.withOpacity(0.3) : Colors.grey.withOpacity(0.3),
                ),
              ),
            ),
          ),
          SizedBox(width: screenWidth * 0.04),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
            child: IconButton(
              onPressed: () {
                // Comment functionality
              },
              icon: Icon(
                Icons.comment_outlined,
                color: Colors.grey[600],
                size: screenWidth * 0.05,
              ),
              padding: EdgeInsets.all(screenWidth * 0.04),
            ),
          ),
          SizedBox(width: screenWidth * 0.025),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
            child: IconButton(
              onPressed: () {
                // Share functionality
              },
              icon: Icon(
                Icons.share_rounded,
                color: Colors.grey[600],
                size: screenWidth * 0.05,
              ),
              padding: EdgeInsets.all(screenWidth * 0.04),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingScreen(context);
    }

    if (errorMessage != null) {
      return _buildErrorScreen(context);
    }

    if (blog == null) {
      return _buildErrorScreen(context);
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      extendBodyBehindAppBar: true,
      appBar: _buildCustomAppBar(context: context),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    SliverToBoxAdapter(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeroImage(context),
                          _buildMetaInfo(context),
                          SizedBox(height: MediaQuery.of(context).size.height * 0.015),
                          _buildContentSection(context),
                          _buildActionButtons(context),
                          SizedBox(height: MediaQuery.of(context).size.height * 0.03),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}