class EmergencyAppointmentRequest {
  final String donationEventId;
  final String userName;
  final String phoneNumber;
  final String location;
  final String email;
  final String bloodTypeId;
  final int bloodComponent; // Enum dạng int

  EmergencyAppointmentRequest({
    required this.donationEventId,
    required this.userName,
    required this.phoneNumber,
    required this.location,
    required this.email,
    required this.bloodTypeId,
    required this.bloodComponent,
  });

  Map<String, dynamic> toJson() => {
    'DonationEventId': donationEventId,
    'UserName': userName,
    'PhoneNumber': phoneNumber,
    'Location': location,
    'Email': email,
    'BloodTypeId': bloodTypeId,
    'BloodComponent': bloodComponent,
  };
}
