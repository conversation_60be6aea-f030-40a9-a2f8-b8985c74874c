﻿using BloodPlus.Core.Enum;

namespace BloodPlus.ModelViews.DonationEventModelView
{
    public class CreateDEModelView
    {
        public string OrganizationId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Location { get; set; }
        public DateTime EventDate { get; set; }
        public DateTime EndTime { get; set; }
        public int RequiredDonors { get; set; }
        public EventStatus Status { get; set; }
        public bool IsEmergency { get; set; }
    }
}
