<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Google.Apis.Auth" Version="1.69.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="8.8.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.8.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BloodPlus.ModelViews\BloodPlus.ModelViews.csproj">
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\BloodPlus.Repositories\BloodPlus.Repositories.csproj">
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\BloodPlus.Services\BloodPlus.Services.csproj">
      <Private>True</Private>
    </ProjectReference>
  </ItemGroup>

</Project>
