﻿using BloodPlus.Core.Enum;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace BloodPlus.ModelViews.UserModelViews
{
    public class CreateManagerAccountModel
    {
        [Required]
        [StringLength(50)]
        public string UserName { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; }


        [Required]
        public string Email { get; set; }

        [Required]
        [StringLength(10)]
        [RegularExpression(@"^\d+$", ErrorMessage = "Phone number must contain only digits.")]
        public string? PhoneNumber { get; set; }

        [Required]
        public string Name { get; set; }

        [Required]
        public DateTime? DateOfBirth { get; set; }

        [Required]
        public string? Address { get; set; }
        public string? Job { get; set; }

        [Required]
        public Gender? Gender { get; set; }

        public IFormFile? UserImage { get; set; }
    }
}
