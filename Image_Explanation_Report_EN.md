# DETAILED EXPLANATION OF IMAGES IN BLOODPLUS REPORT

---

## FIGURE 1: DATABASE SCHEMA (ENTITY-RELATIONSHIP DIAGRAM - ERD)

### **Overview:**
The Entity-Relationship Diagram (ERD) illustrates the database structure of the BloodPlus system with 12 main tables and their relationships.

### **Main Entities and Functions:**

#### **1. User (User Management)**
- **Purpose**: Store system user information
- **Key Fields**:
  - Id: Primary key
  - UserName, Email: Login credentials
  - FullName, PhoneNumber: Personal information
  - BloodTypeId: Link to BloodType table
  - Address, DateOfBirth: Additional information
  - IsActive, IsDeleted: Account status

#### **2. Role (Role Management)**
- **Purpose**: Manage system permissions
- **Roles**: Ad<PERSON>, Manager, User
- **Relationship**: N:N with User through UserRole

#### **3. DonationEvent (Blood Donation Events)**
- **Purpose**: Manage blood donation events
- **Key Fields**:
  - EventName, Description: Event information
  - EventDate, StartTime, EndTime: Timing
  - Location, Address: Venue details
  - RequiredBloodTypes: Required blood types
  - MaxParticipants: Maximum capacity
  - IsEmergency: Emergency event flag

#### **4. Appointment (Appointments)**
- **Purpose**: Manage user blood donation appointments
- **Relationships**: 
  - UserId → User (N:1)
  - DonationEventId → DonationEvent (N:1)
- **Status**: Pending, Confirmed, Completed, Cancelled

#### **5. BloodType (Blood Types)**
- **Purpose**: Define blood type categories
- **Types**: A+, A-, B+, B-, AB+, AB-, O+, O-
- **Relationships**: 1:N with User and DonationEvent

#### **6. Organization (Organizations)**
- **Purpose**: Manage healthcare organizations
- **Information**: Name, address, contact, organization type
- **Relationship**: 1:N with DonationEvent

#### **7. Blog (Articles)**
- **Purpose**: Manage news and articles
- **Content**: Title, Content, Images, Tags
- **Metadata**: Author, PublishDate, ViewCount

#### **8. Notification (Notifications)**
- **Purpose**: User notification system
- **Types**: 
  - New events
  - Appointment reminders
  - Emergency blood requests
  - News updates

#### **9. Voucher (Rewards)**
- **Purpose**: Manage vouchers and rewards
- **Information**: Name, description, value, conditions
- **Status**: Available, Used, Expired

#### **10. ChatMessage (Chat Messages)**
- **Purpose**: Store AI chat history
- **Content**: Message, Response, Timestamp
- **Relationship**: UserId → User (N:1)

### **Key Relationships:**
- **User ↔ Appointment**: 1:N (One user has multiple appointments)
- **DonationEvent ↔ Appointment**: 1:N (One event has multiple appointments)
- **User ↔ Notification**: 1:N (One user receives multiple notifications)
- **BloodType ↔ User**: 1:N (One blood type has multiple users)
- **Organization ↔ DonationEvent**: 1:N (One organization hosts multiple events)

### **Usage in Report:**
```markdown
### 5.3 Database Schema (ERD)

![Database Schema](images/database-schema.png)

*Figure 5.1: Entity-Relationship Diagram of BloodPlus System*

The database is designed with 12 main tables, ensuring data integrity and high query performance. Relationships are designed following normalization principles to avoid redundancy and ensure consistency.
```

---

## FIGURE 2: SYSTEM ARCHITECTURE

### **Overview:**
The system architecture diagram shows how BloodPlus system components interact following Clean Architecture principles. The complete system consists of Backend API (ASP.NET Core) and Mobile Application (Flutter) working together.

### **BACKEND ARCHITECTURE (ASP.NET Core):**

#### **1. Presentation Layer (API Layer)**
- **Components**: Controllers, Middleware, Authentication
- **Functions**: 
  - Handle HTTP requests/responses
  - Input data validation
  - Authentication & Authorization
  - Error handling
- **Technology**: ASP.NET Core Controllers, JWT Authentication

#### **2. Business Logic Layer (Service Layer)**
- **Components**: Services, Business Rules, DTOs
- **Functions**:
  - Process business logic
  - Business rules validation
  - Data transformation
  - External service integration
- **Main Services**:
  - AuthService: User authentication
  - DonationEventService: Event management
  - NotificationService: Send notifications
  - ChatGPTService: AI integration

#### **3. Data Access Layer (Repository Layer)**
- **Components**: Repositories, Unit of Work, Data Context
- **Functions**:
  - Database data access
  - Implement Repository pattern
  - Transaction management
  - Data mapping
- **Pattern**: Repository + Unit of Work

#### **4. Domain Layer (Core Layer)**
- **Components**: Entities, Enums, Interfaces
- **Functions**:
  - Define business entities
  - Business rules and constraints
  - Domain interfaces
- **Characteristics**: Independent, no dependencies on other layers

#### **5. Infrastructure Layer**
- **Components**: External Services, File Storage, Email
- **Services**:
  - Firebase: Push notifications, File storage
  - OpenAI: AI Chatbot
  - Google Maps: Geolocation
  - Email Service: Send emails

### **MOBILE ARCHITECTURE (Flutter):**

#### **1. Presentation Layer (UI Layer)**
- **Components**: Screens, Widgets, UI Components
- **Functions**:
  - Display user interface
  - Handle user interactions
  - Navigate between screens
  - Show loading states and errors
- **Technology**: Flutter Widgets, Material Design
- **Structure**:
  - **Features**: Auth, Home, Schedule, Blog, User, Voucher
  - **Shared Widgets**: Custom components, Common UI elements
  - **Themes**: App colors, typography, styling

#### **2. Business Logic Layer (BLoC/Provider Layer)**
- **Components**: Providers, ViewModels, State Management
- **Functions**:
  - Manage application state
  - Handle business logic
  - Coordinate between UI and data
  - Validate user input
- **Technology**: Provider Pattern, State Management
- **Managers**:
  - **AuthManager**: Authentication state
  - **EventManager**: Event data management
  - **NotificationManager**: Push notification handling
  - **LocationManager**: GPS and location services

#### **3. Data Layer (Repository Layer)**
- **Components**: Repositories, Data Sources, Models
- **Functions**:
  - Abstract data access
  - Handle API calls
  - Manage local storage
  - Data caching and synchronization
- **Technology**: HTTP Client, SharedPreferences, SQLite
- **Repositories**:
  - **AuthRepository**: User authentication
  - **EventRepository**: Event data
  - **UserRepository**: User profile management
  - **ChatRepository**: AI chat history

#### **4. Services Layer (External Integration)**
- **Components**: API Services, Platform Services
- **Functions**:
  - HTTP API communication
  - Platform-specific features
  - Third-party integrations
  - Background services
- **Services**:
  - **ApiService**: REST API communication
  - **FirebaseService**: Push notifications, Analytics
  - **LocationService**: GPS, Geolocation
  - **BiometricService**: Fingerprint, Face ID
  - **CameraService**: Image capture and upload

#### **5. Core Layer (Foundation)**
- **Components**: Constants, Utils, Configurations
- **Functions**:
  - App configuration
  - Utility functions
  - Constants and enums
  - Route management
- **Technology**: Dart core libraries
- **Structure**:
  - **Config**: API endpoints, App settings
  - **Constants**: Colors, Strings, Dimensions
  - **Utils**: Helper functions, Validators
  - **Routes**: Navigation configuration

### **MOBILE APP ARCHITECTURE FLOW:**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  UI Components  │    │   State Mgmt    │    │   Repositories  │
│   (Widgets)     │◄──►│  (Providers)    │◄──►│  (Data Access)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│   Services      │◄─────────────┘
                        │ (API, Platform) │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │  External APIs  │
                        │ Backend, Firebase│
                        └─────────────────┘
```

### **COMPLETE SYSTEM DATA FLOW:**
1. **Mobile Request**: User Action → UI Widget → Provider → Repository → API Service
2. **API Processing**: HTTP Request → Backend Controller → Service → Repository → Database
3. **Database Response**: SQL Server → Entity Framework → Repository → Service → Controller
4. **Mobile Response**: HTTP Response → API Service → Repository → Provider → UI Widget → User Interface

### **CROSS-PLATFORM COMMUNICATION:**

#### **API Communication**
- **Protocol**: HTTPS REST API
- **Authentication**: JWT Bearer Token
- **Data Format**: JSON
- **Error Handling**: Standardized error responses
- **Timeout**: Configurable request timeout

#### **Real-time Features**
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Real-time Updates**: SignalR for live notifications
- **Background Sync**: Periodic data synchronization
- **Offline Support**: Local caching and queue management

#### **Security Integration**
- **Token Management**: Automatic token refresh
- **Biometric Auth**: Local device authentication
- **Data Encryption**: Sensitive data encryption
- **Certificate Pinning**: API security validation

### **Usage in Report:**
```markdown
### 2.3 Overall System Architecture

![System Architecture](images/system-architecture.png)

*Figure 2.1: Complete Architecture of BloodPlus System (Backend + Mobile)*

The system consists of two main components working together:

**Backend Architecture (ASP.NET Core)**:
- Clean Architecture with 4 layers (Presentation, Business, Data, Domain)
- RESTful API design with JWT authentication
- Entity Framework Core for data access
- External service integrations (Firebase, OpenAI, Google Maps)

**Mobile Architecture (Flutter)**:
- 5-layer architecture (UI, Business Logic, Data, Services, Core)
- Provider pattern for state management
- Repository pattern for data abstraction
- Cross-platform compatibility (Android/iOS)

**Key Benefits**:
- **Separation of Concerns**: Clear responsibility separation
- **Scalability**: Easy to scale both backend and mobile
- **Maintainability**: Independent development and testing
- **Cross-platform**: Single codebase for multiple platforms
- **Real-time**: Live notifications and updates
```

---

## FIGURE 3: USER FLOW DIAGRAM

### **Overview:**
The user flow diagram describes the steps users take in the BloodPlus application from opening the app to completing main tasks.

### **Main Activity Flows:**

#### **1. Authentication Flow**
**Step 1**: Open application
- Check login status
- If not logged in → Login screen
- If logged in → Home screen

**Step 2**: Register/Login
- **New Registration**:
  - Enter personal information
  - OTP verification via SMS
  - Account creation success
- **Login**:
  - Email/Password or Google Sign-in
  - Successful authentication → Home screen

#### **2. Event Discovery & Registration Flow**
**Step 1**: Search events
- View nearby events list
- Filter by: Time, Location, Blood type
- View event details

**Step 2**: Register participation
- Select desired event
- Confirm personal information
- Choose suitable time
- Confirm registration

**Step 3**: Manage appointments
- View registered appointments
- Receive reminder notifications
- Check-in at event

#### **3. Emergency Request Flow**
**Step 1**: Create emergency request
- Enter patient information
- Select required blood type
- Identify hospital location
- Send request

**Step 2**: Emergency notification
- System finds suitable donors
- Send location-based push notifications
- Donors respond

**Step 3**: Connect and support
- Connect donors with hospital
- Provide contact information
- Track status

#### **4. AI Chatbot Flow**
**Step 1**: Open chat
- Access chatbot from menu
- View previous chat history

**Step 2**: Ask questions
- Enter blood donation questions
- AI analyzes context (location, blood type)
- Provide professional consultation

**Step 3**: Receive support
- Get detailed answers
- Suggest nearest donation locations
- Save chat history

#### **5. Reward System Flow**
**Step 1**: Earn points
- Complete blood donation → Earn points
- View leaderboard
- Track accumulated points

**Step 2**: Redeem vouchers
- View available vouchers
- Select desired voucher
- Confirm point redemption

**Step 3**: Use vouchers
- View owned vouchers
- Use at partner locations
- Track voucher status

### **Decision Points:**
- **Already logged in?** → Yes: Home / No: Login
- **Suitable events available?** → Yes: Register / No: Create alert
- **Emergency?** → Yes: Emergency flow / No: Normal flow
- **Enough points for voucher?** → Yes: Redeem / No: Continue earning

### **Usage in Report:**
```markdown
### 4.8 User Activity Flow

![User Flow](images/user-flow.png)

*Figure 4.1: Main User Activity Flow Diagram*

The diagram describes 5 main activity flows:
1. **Authentication Flow**: Registration/login with OTP verification
2. **Event Flow**: Search and register for blood donation events
3. **Emergency Flow**: Handle urgent blood requests
4. **Chatbot Flow**: Interact with AI consultation
5. **Reward Flow**: Earn points and redeem vouchers

Each flow is optimized for users to complete tasks with minimal steps.
```

---

## INTEGRATION GUIDE FOR REPORT

### **1. Figure Placement:**
- **Figure 1 (Database Schema)**: Section 5 - Database
- **Figure 2 (System Architecture)**: Section 2 - System Architecture
- **Figure 3 (User Flow)**: Section 4 - Main Features

### **2. Standard Format:**
```markdown
![Figure Name](image_path)

*Figure X.Y: Brief description*

Detailed explanation...
```

### **3. Technical Requirements:**
- **Resolution**: Minimum 300 DPI
- **Format**: PNG or JPG
- **Size**: Suitable for A4 paper
- **Font**: Arial or Times New Roman, size 10-12

### **4. Design Notes:**
- Use consistent colors
- Ensure clarity when printed in black and white
- Proper alignment and layout
- Complete annotations for symbols

---

## ADDITIONAL DIAGRAMS TO CREATE

### **1. Mobile App Architecture Diagram**
**Description**: Flutter app architecture with MVVM pattern
**Content**:
- **Presentation Layer**: Widgets, Screens, UI Components
- **Business Logic Layer**: Providers, ViewModels, State Management
- **Data Layer**: Repositories, Services, Local Storage
- **Platform Layer**: Native Android/iOS integrations

### **2. API Sequence Diagram**
**Description**: API processing flow from Mobile to Database
**Content**:
- Request flow: Mobile → Controller → Service → Repository → Database
- Response flow: Database → Repository → Service → Controller → Mobile
- Error handling and exception management

### **3. Security Architecture Diagram**
**Description**: Security layers in the system
**Content**:
- Authentication layer (JWT, OAuth)
- Authorization layer (Role-based access)
- Data encryption layer
- Network security (HTTPS, API Gateway)

### **4. Deployment Architecture**
**Description**: System deployment architecture
**Content**:
- Development environment
- Staging environment
- Production environment
- CI/CD pipeline

### **5. Integration Architecture**
**Description**: Integration with external services
**Content**:
- Firebase integration (Auth, Storage, FCM)
- OpenAI API integration
- Google Maps API
- Payment gateway integration

---

## SAMPLE TEMPLATES FOR EACH FIGURE TYPE

### **Template 1: Database Schema**
```markdown
### X.X Database Schema

![Database Schema](images/database-schema.png)

*Figure X.X: Entity Relationship Diagram of BloodPlus System*

**Description**: The database consists of [number] main tables with relationships designed following normalization principles:

**Main Tables**:
- **User**: Manages user information and authentication
- **DonationEvent**: Stores blood donation event information
- **Appointment**: Manages appointments between users and events
- **[List other tables]**

**Key Relationships**:
- User (1) ↔ (N) Appointment: One user can have multiple appointments
- DonationEvent (1) ↔ (N) Appointment: One event has multiple registrations
- [Describe other relationships]

**Design Advantages**:
- Ensures data integrity
- Optimizes query performance
- Easy to extend and maintain
```

### **Template 2: Complete System Architecture (Backend + Mobile)**
```markdown
### X.X Complete System Architecture

![System Architecture](images/system-architecture.png)

*Figure X.X: Overall Architecture of BloodPlus System (Backend + Mobile)*

**Description**: The system consists of Backend API and Mobile Application working together:

**BACKEND ARCHITECTURE (ASP.NET Core)**:

**1. Presentation Layer**:
- Function: Handle HTTP requests, authentication, validation
- Technology: ASP.NET Core Controllers, JWT Authentication
- Components: Controllers, Middleware, Authentication filters

**2. Business Logic Layer**:
- Function: Process business logic, business rules
- Technology: C# Services, AutoMapper, FluentValidation
- Services: AuthService, EventService, NotificationService, ChatGPTService

**3. Data Access Layer**:
- Function: Data access, transaction management
- Technology: Entity Framework Core, Repository Pattern
- Patterns: Repository + Unit of Work

**4. Infrastructure Layer**:
- Function: External service integration
- Services: Firebase, OpenAI, Google Maps, Email Service

**MOBILE ARCHITECTURE (Flutter)**:

**1. Presentation Layer (UI)**:
- Function: User interface and interactions
- Technology: Flutter Widgets, Material Design
- Components: Screens, Custom Widgets, Navigation

**2. Business Logic Layer**:
- Function: State management and business logic
- Technology: Provider Pattern, State Management
- Managers: AuthManager, EventManager, NotificationManager

**3. Data Layer**:
- Function: Data access and caching
- Technology: HTTP Client, SharedPreferences, SQLite
- Repositories: AuthRepository, EventRepository, UserRepository

**4. Services Layer**:
- Function: External integrations and platform services
- Technology: Platform Channels, Third-party SDKs
- Services: ApiService, FirebaseService, LocationService, BiometricService

**5. Core Layer**:
- Function: App configuration and utilities
- Technology: Dart core libraries
- Components: Config, Constants, Utils, Routes

**COMMUNICATION**:
- **API Protocol**: HTTPS REST with JWT authentication
- **Real-time**: Firebase Cloud Messaging, SignalR
- **Data Format**: JSON with standardized error handling
- **Security**: Token management, biometric auth, data encryption

**ADVANTAGES**:
- **Full-stack Solution**: Complete backend and mobile integration
- **Cross-platform**: Single mobile codebase for Android/iOS
- **Scalable Architecture**: Independent scaling of components
- **Real-time Features**: Live notifications and updates
- **Security**: Multi-layer security implementation
- **Maintainability**: Clear separation and modular design
```

### **Template 3: User Flow**
```markdown
### X.X User Activity Flow

![User Flow](images/user-flow.png)

*Figure X.X: Main User Activity Flow Diagram*

**Description**: The diagram shows [number] main user activity flows in the application:

**1. Authentication Flow**:
- Step 1: Check login status
- Step 2: Register/login (Email/Google)
- Step 3: OTP verification (if needed)
- Result: Access to application

**2. [Other Flow Name]**:
- [Describe steps]

**Decision Points**:
- [List important decision points]

**UX Optimization**:
- Minimize number of steps
- Provide clear feedback
- User-friendly error handling
```

---

## FIGURE COMPLETION CHECKLIST

### **Before adding to report:**
- [ ] Image has sufficient resolution (≥300 DPI)
- [ ] Text in image is clear and readable
- [ ] Colors are suitable for black and white printing
- [ ] Size is appropriate for A4 paper
- [ ] Has caption and figure number
- [ ] Detailed explanation in content
- [ ] Correct reference in text
- [ ] Proper layout, no distortion
- [ ] Complete annotations for symbols
- [ ] Spell-check text in images

---

## PROFESSIONAL WRITING GUIDELINES

### **Figure Captions:**
- Use consistent numbering (Figure X.Y)
- Brief but descriptive
- Italicized format
- Placed below the figure

### **In-text References:**
- "As shown in Figure 2.1..."
- "Figure 3.2 illustrates..."
- "The database schema (Figure 5.1) demonstrates..."

### **Technical Descriptions:**
- Use present tense
- Be specific and accurate
- Include technical details
- Explain benefits and advantages

### **Common Phrases:**
- "The diagram illustrates..."
- "As depicted in the figure..."
- "The architecture demonstrates..."
- "The flow shows..."
- "This design ensures..."
