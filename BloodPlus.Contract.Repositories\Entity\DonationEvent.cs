﻿using BloodPlus.Core.Base;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using BloodPlus.Core.Enum;

namespace BloodPlus.Contract.Repositories.Entity
{
    public class DonationEvent : BaseEntity
    {
        [Required]
        public string OrganizationId { get; set; }

        [ForeignKey("OrganizationId")]
        public virtual Organization Organization { get; set; }
        public string Title { get; set; }
        public string? Description { get; set; }
        public string Location { get; set; }
        public DateTime EventDate { get; set; }
        public DateTime EndTime { get; set; }
        public int RequiredDonors { get; set; }
        public int CurrentDonors { get; set; }
        public EventStatus Status { get; set; }
        public string? Image { get; set; }
        public bool IsEmergency { get; set; }
        public string? RequiredBloodType { get; set; }
        public virtual ICollection<Appointment> Appointments { get; set; }
    }
}
