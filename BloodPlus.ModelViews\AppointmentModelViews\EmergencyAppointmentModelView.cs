﻿
using BloodPlus.Core.Enum;

namespace BloodPlus.ModelViews.AppointmentModelViews
{
    public class EmergencyAppointmentCreateModelView
    {
        public string DonationEventId { get; set; }
        public string? UserName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Location { get; set; }
        public string? Email { get; set; }
        public string? BloodTypeId { get; set; }
        public BloodComponent BloodComponent { get; set; }
    }
}
