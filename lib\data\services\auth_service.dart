import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:bloodplusmobile/data/services/user_service.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:bloodplusmobile/data/models/register_request_model.dart';
import 'package:bloodplusmobile/data/models/verify_otp_request_model.dart';
import 'dart:io';

class AuthService {
  static final IOClient _client = IOClient();

  final UserService _userService;
  final UserManager _userManager;
  final AppStateNotifier? _appStateNotifier;
  final GoogleSignIn _googleSignIn;

  AuthService({
    UserService? userService,
    UserManager? userManager,
    AppStateNotifier? appStateNotifier,
    GoogleSignIn? googleSignIn,
  })  : _userService = userService ?? UserService(),
        _userManager = userManager ?? UserManager(),
        _appStateNotifier = appStateNotifier,
        _googleSignIn = googleSignIn ??
            GoogleSignIn(
              scopes: ['email', 'profile'],
              serverClientId: '200053914917-0v8qphjmrav3mlh3j9pi4u2459ffcva2.apps.googleusercontent.com',
            );

  Future<String?> _getDeviceToken() async {
    try {
      return OneSignal.User.pushSubscription.id;
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>> login(
      String username, String password, Map<String, String> localizedStrings) async {
    final deviceToken = await _getDeviceToken();
    final url = Uri.parse(ApiConfig.authLogin.fullApiUrl);
    final body = jsonEncode({
      'username': username,
      'password': password,
      'deviceToken': deviceToken ?? '',
    });

    try {
      final response = await _client.post(
        url,
        headers: {'Content-Type': 'application/json', 'accept': '*/*'},
        body: body,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final token = _extractToken(data) ?? '';
        final userId = _extractUserId(data, token) ?? '';
        final email = _extractUserEmail(data, token) ?? '';
        final role = _extractUserRole(data, token) ?? '';
        print("torof $role");
        print("dsddd $data");
        await _saveUserSession(token, userId, email, role);
        return {'accessToken': token, 'userId': userId};
      } else {
        throw Exception(
          localizedStrings['login_failed']?.replaceAll('{statusCode}', response.statusCode.toString()) ??
              'Login failed: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception(
        localizedStrings['connection_error']?.replaceAll('{error}', e.toString()) ?? 'Login error: $e',
      );
    }
  }

  Future<Map<String, dynamic>> loginWithGoogle(Map<String, String> localizedStrings) async {
    final deviceToken = await _getDeviceToken();
    try {
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return {'error': localizedStrings['google_login_cancelled'] ?? 'Google login cancelled'};
      }

      final googleAuth = await googleUser.authentication;
      final idToken = googleAuth.idToken;
      if (idToken == null) {
        throw Exception(localizedStrings['google_token_error'] ?? 'Unable to get idToken from Google');
      }

      final loginResult = await _attemptGoogleLogin(idToken, deviceToken, localizedStrings);
      if (loginResult['success'] == true) return loginResult['data'];

      if (loginResult['statusCode'] == 401) {
        final userInfo = {
          'name': googleUser.displayName ?? 'Google User',
          'email': googleUser.email ?? '',
          'userImage': googleUser.photoUrl ?? '',
        };

        if (!_isValidGoogleUserInfo(userInfo)) {
          throw Exception(
            localizedStrings['google_user_info_invalid'] ?? 'Invalid Google account information',
          );
        }

        await _attemptGoogleRegistration(userInfo, deviceToken, localizedStrings);
        final retryResult = await _attemptGoogleLogin(idToken, deviceToken, localizedStrings);
        if (retryResult['success'] == true) return retryResult['data'];

        return await _createFallbackGoogleLogin(googleUser, deviceToken, localizedStrings);
      }

      throw Exception(
        localizedStrings['google_login_failed']?.replaceAll('{error}', loginResult['error'] ?? '') ??
            'Google login failed: ${loginResult['error'] ?? ''}',
      );
    } catch (e) {
      throw Exception(
        localizedStrings['connection_error']?.replaceAll('{error}', e.toString()) ?? 'Google login error: $e',
      );
    }
  }

  Future<Map<String, dynamic>> _attemptGoogleLogin(
      String idToken, String? deviceToken, Map<String, String> localizedStrings) async {
    final url = Uri.parse('${ApiConfig.authGoogleLogin.fullApiUrl}?idToken=$idToken&deviceToken=${deviceToken ?? ''}');

    try {
      final response = await _client.post(
        url,
        headers: {'Content-Type': 'application/json', 'accept': '*/*'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final token = _extractToken(data) ?? '';
        final userId = _extractUserId(data, token) ?? '';
        final email = _extractUserEmail(data, token) ?? '';
        final role = _extractUserRole(data, token) ?? '';

        await _saveUserSession(token, userId, email, role);
        return {'success': true, 'data': {'accessToken': token, 'userId': userId}};
      } else {
        return {
          'success': false,
          'statusCode': response.statusCode,
          'error': localizedStrings['login_failed']
              ?.replaceAll('{statusCode}', response.statusCode.toString()) ??
              'Google login failed: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _attemptGoogleRegistration(
      Map<String, dynamic> userInfo, String? deviceToken, Map<String, String> localizedStrings) async {
    final url = Uri.parse(ApiConfig.authRegister.fullApiUrl);
    final email = userInfo['email']?.toString() ?? '';
    final name = userInfo['name']?.toString() ?? 'Google User';
    final userName = email.split('@')[0];
    const defaultPassword = 'GoogleUser123!';

    final body = jsonEncode({
      'name': name,
      'email': email,
      'userName': userName,
      'password': defaultPassword,
      'verifyPassword': defaultPassword,
      'phoneNumber': '0000000000',
      'dateOfBirth': '1990-01-01T00:00:00.000Z',
      'gender': 1,
      'userImage': userInfo['userImage']?.toString() ?? '',
      'isGoogleUser': true,
      'deviceToken': deviceToken ?? '',
    });

    try {
      final response = await _client.post(
        url,
        headers: {'Content-Type': 'application/json', 'accept': '*/*'},
        body: body,
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': localizedStrings['google_register_success'] ?? 'Google account created successfully',
        };
      } else {
        throw Exception(
          localizedStrings['google_register_failed']?.replaceAll('{statusCode}', response.statusCode.toString()) ??
              'Google registration failed: ${response.statusCode}',
        );
      }
    } catch (e) {
      return {'success': false, 'message': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _createFallbackGoogleLogin(
      GoogleSignInAccount googleUser, String? deviceToken, Map<String, String> localizedStrings) async {
    try {
      final tempUserId = _generateTempUserId(googleUser.email);
      final mockToken = 'google_temp_${DateTime.now().millisecondsSinceEpoch}';

      _appStateNotifier?.resetState();
      await _userManager.saveUserToken(mockToken);
      await _userManager.saveUserId(tempUserId);

      final tempUserInfo = UserModel(
        id: tempUserId,
        name: googleUser.displayName ?? 'Google User',
        email: googleUser.email ?? '',
        userImage: googleUser.photoUrl ?? 'assets/images/profile.jpg',
        donationCount: 0,
        point: 0,
      );

      await _userManager.saveUserInfo(tempUserId, tempUserInfo);
      await _appStateNotifier?.fetchUserProfile();

      return {'accessToken': mockToken, 'userId': tempUserId};
    } catch (e) {
      throw Exception(
        localizedStrings['fallback_login_failed'] ?? 'Failed to create temporary login session: $e',
      );
    }
  }

  String? _extractToken(Map<String, dynamic> data) {
    return data['AccessToken']?.toString() ?? data['accessToken']?.toString() ?? data['token']?.toString();
  }

  String? _extractUserId(Map<String, dynamic> data, String token) {
    final userId = _extractUserIdFromToken(token) ?? data['userId']?.toString();
    if (userId == null) {
      throw Exception('Unable to extract userId from token or API data');
    }
    return userId;
  }

  String? _extractUserRole(Map<String, dynamic> data, String token) {
    // Lấy từ response body nếu có
    final role = data['http://schemas.microsoft.com/ws/2008/06/identity/claims/role']?.toString();
    if (role != null) return role;

    // Nếu không có, lấy từ JWT
    return _extractClaimFromToken(token, 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role');
  }

  String? _extractClaimFromToken(String token, String claimKey) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final Map<String, dynamic> payloadMap = jsonDecode(decoded);

      return payloadMap[claimKey]?.toString();
    } catch (_) {
      return null;
    }
  }


  String? _extractUserEmail(Map<String, dynamic> data, String token) {
    final email = _extractUserIdFromToken(token) ?? data['email']?.toString();
    if (email == null) {
      throw Exception('Unable to extract email from token or API data');
    }
    return email;
  }

  String? _extractUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      final payload = parts[1];
      final normalizedPayload = payload.padRight(payload.length + (4 - payload.length % 4) % 4, '=');
      final decodedPayload = utf8.decode(base64Url.decode(normalizedPayload));
      final payloadMap = jsonDecode(decodedPayload) as Map<String, dynamic>;

      final userId = payloadMap['userId']?.toString();
      return userId;
    } catch (e) {
      return null;
    }
  }

  bool _isValidGoogleUserInfo(Map<String, dynamic> userInfo) {
    return userInfo['email'] != null &&
        userInfo['email'].toString().isNotEmpty &&
        userInfo['name'] != null &&
        userInfo['name'].toString().isNotEmpty;
  }

  String _generateTempUserId(String email) {
    final emailHash = email.hashCode.abs().toString();
    return 'google_temp_$emailHash';
  }

  Future<void> _saveUserSession(String token, String userId, String email, String role) async {
    _appStateNotifier?.resetState();
    await _userManager.saveUserToken(token);
    await _userManager.saveUserEmail(email);
    await _userManager.saveUserRole(role);
    await _userManager.saveUserId(userId);
    final user = await _userService.getUserInfo(userId, token);
    await _userManager.saveUserInfo(userId, user);
    await _appStateNotifier?.fetchUserProfile();
  }

  Future<bool> registerAccount(RegisterRequestModel model, {File? userImage}) async {
    try {
      final uri = Uri.parse(ApiConfig.authRegisterAccount.fullApiUrl);
      final request = http.MultipartRequest('POST', uri);
      request.fields.addAll(model.toMap());
      if (userImage != null) {
        request.files.add(await http.MultipartFile.fromPath('UserImage', userImage.path));
      }
      final response = await request.send();
      final respStr = await response.stream.bytesToString();
      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(respStr.contains('Email already in use')
            ? 'Email đã tồn tại! Vui lòng sử dụng email khác.'
            : respStr.contains('UserName already in use')
            ? 'Tên đăng nhập đã tồn tại! Vui lòng chọn tên khác.'
            : respStr.contains('PhoneNumber already in use')
            ? 'Số điện thoại đã tồn tại! Vui lòng sử dụng số khác.'
            : 'Đăng ký thất bại: $respStr');
      }
    } catch (e) {
      print('Lỗi exception khi đăng ký: $e');
      throw Exception(e.toString());
    }
  }

  Future<bool> verifyOtp(VerifyOtpRequestModel model) async {
    try {
      final response = await ApiConfig.post(
        ApiConfig.authVerifyOtp,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: model.toMap(),
      );
      if (response.statusCode == 200) {
        // Kiểm tra nội dung body để xác định OTP đúng hay sai
        if (response.body.contains('OTP không chính xác')) {
          return false;
        }
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('Lỗi exception khi xác thực OTP: $e');
      return false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    try {
      final response = await ApiConfig.post(
        ApiConfig.authForgotPassword,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: {'Email': email},
      );
      return response.statusCode == 200;
    } catch (e) {
      print('Lỗi khi gửi yêu cầu quên mật khẩu: $e');
      return false;
    }
  }

  Future<bool> verifyOtpPassword(String email, String otp) async {
    try {
      final response = await ApiConfig.patch(
        ApiConfig.authVerifyOtpPassword,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: {'Email': email, 'Otp': otp},
      );
      if (response.statusCode == 200) {
        // Kiểm tra nội dung body để xác định OTP đúng hay sai
        if (response.body.contains('OTP không chính xác') || response.body.contains('OTP đã hết hạn')) {
          return false;
        }
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('Lỗi khi xác thực OTP quên mật khẩu: $e');
      return false;
    }
  }

  Future<bool> resetPassword(String email, String newPassword, String verifyPassword) async {
    try {
      final response = await ApiConfig.patch(
        ApiConfig.authResetPassword,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: {
          'Email': email,
          'NewPassword': newPassword,
          'VerifyPassword': verifyPassword,
        },
      );
      return response.statusCode == 200;
    } catch (e) {
      print('Lỗi khi đặt lại mật khẩu: $e');
      return false;
    }
  }
}