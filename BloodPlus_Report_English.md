# BLOODPLUS PROJECT REPORT
## Blood Donation Community Support Application

---

**Student:** [Student Name]  
**Student ID:** [Student ID]  
**Class:** [Class Name]  
**Subject:** PRM392 - Mobile Programming  
**Supervisor:** [Supervisor Name]  
**Submission Date:** July 19, 2025  

---

## 1. PROJECT OVERVIEW

### 1.1 Introduction
BloodPlus is a comprehensive mobile application system developed to support the blood donation community. The project includes:
- **Backend API**: Built with ASP.NET Core using Clean Architecture
- **Mobile App**: Cross-platform Flutter application (Android/iOS)

### 1.2 Project Objectives
- Create a bridge between blood donors and medical organizations
- Efficiently manage blood donation events
- Provide information and consultation about blood donation
- Build an active blood donation community

### 1.3 Target Users
- **Citizens**: Those who need to donate blood or seek blood
- **Medical Organizations**: Hospitals, medical centers
- **Administrators**: System and event management

---

## 2. SYSTEM ARCHITECTURE

### 2.1 Overall Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │    Database     │
│   (Flutter)     │◄──►│  (ASP.NET Core) │◄──►│  (SQL Server)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│  External APIs  │◄─────────────┘
                        │ Firebase, OpenAI│
                        └─────────────────┘
```

### 2.2 Backend Architecture (Clean Architecture)
```
BloodPlus.API/                 # Presentation Layer
├── Controllers/               # API Controllers
├── Middleware/               # Custom Middleware
└── Program.cs                # Application Entry Point

BloodPlus.Services/           # Business Logic Layer
├── Services/                 # Business Services
├── Interfaces/              # Service Contracts
└── Hubs/                    # SignalR Hubs

BloodPlus.Repositories/       # Data Access Layer
├── Context/                 # Database Context
├── UOW/                     # Unit of Work Pattern
└── Migrations/              # Database Migrations

BloodPlus.Core/              # Domain Layer
├── Base/                    # Base Entities
├── Enum/                    # Enumerations
└── Utils/                   # Utilities

BloodPlus.ModelViews/        # DTOs & ViewModels
BloodPlus.Contract.Repositories/ # Repository Contracts
```

### 2.3 Mobile App Architecture
```
lib/
├── core/                    # Core configurations
│   ├── config/             # API & App configs
│   ├── constants/          # Constants & themes
│   └── routes/             # Navigation routes
├── data/                   # Data layer
│   ├── models/             # Data models
│   ├── services/           # API services
│   ├── repositories/       # Data repositories
│   └── manager/            # State management
└── presentation/           # UI layer
    └── features/           # Feature-based UI
        ├── auth/           # Authentication
        ├── home/           # Home screen
        ├── schedule/       # Event scheduling
        ├── blog/           # News & blogs
        ├── user/           # User profile
        └── voucher/        # Rewards system
```

---

## 3. TECHNOLOGIES USED

### 3.1 Backend Technologies
- **Framework**: ASP.NET Core 8.0
- **Database**: SQL Server with Entity Framework Core
- **Authentication**: JWT Token + Identity Framework
- **Architecture Pattern**: Clean Architecture, Repository Pattern, Unit of Work
- **Real-time Communication**: SignalR
- **External Integrations**:
  - Firebase (Push notifications, File storage)
  - OpenAI GPT-4 (Consultation chatbot)
  - Google Gemini AI
  - Geolocation Services

### 3.2 Mobile Technologies
- **Framework**: Flutter (Dart)
- **State Management**: Provider Pattern
- **Local Storage**: SharedPreferences
- **Maps**: Google Maps Flutter
- **Authentication**: Google Sign-In
- **Notifications**: OneSignal
- **Biometric**: Local Authentication
- **Internationalization**: Flutter Localizations (7 languages)

### 3.3 DevOps & Deployment
- **Version Control**: Git
- **Database**: SQL Server
- **Cloud Services**: Firebase
- **API Documentation**: Swagger/OpenAPI

---

## 4. KEY FEATURES

### 4.1 Authentication & Authorization System
- Register/Login with email/password
- Google Sign-In integration
- OTP verification via phone number
- Password reset functionality
- Role-based access: Admin, Manager, User
- Biometric security (fingerprint, Face ID)

### 4.2 Blood Donation Event Management
- **Regular Events**: Pre-scheduled, registration-based
- **Emergency Events**: Quick creation, location-based notifications
- Event search by location, time, blood type
- Participation history and tracking
- Appointment management and check-in

### 4.3 Smart Notification System
- Push notifications via Firebase
- Location-based notifications (Geofencing)
- Emergency notifications for compatible blood types
- Blood donation appointment reminders
- News and blog notifications

### 4.4 AI Consultation Chatbot
- OpenAI GPT-4 integration
- Health and blood donation consultation
- Multi-language support
- Nearest donation location information
- Chat history storage

### 4.5 Blog & News System
- Blood donation article management
- Topic-based categorization
- Content search and filtering
- Article sharing
- Rating and commenting

### 4.6 Voucher & Reward System
- Points accumulation based on donation frequency
- Voucher and gift exchange
- Blood donor leaderboard
- Incentive programs
- Used voucher management

### 4.7 User Profile Management
- Detailed personal information
- Blood donation history
- Blood type and medical information
- Notification settings
- Privacy management

---

## 5. DATABASE DESIGN

### 5.1 Main Entities
- **User**: User information, authentication
- **DonationEvent**: Blood donation events
- **Appointment**: Blood donation appointments
- **BloodType**: Blood groups (A, B, AB, O with Rh+/-)
- **Notification**: Notification system
- **Blog**: Articles and news
- **Voucher**: Rewards and offers
- **ChatMessage**: AI chat history
- **Organization**: Medical organizations

### 5.2 Relationships
- User 1:N Appointment
- User 1:N Notification
- DonationEvent 1:N Appointment
- BloodType 1:N User
- User 1:N ChatMessage
- Organization 1:N DonationEvent

---

## 6. API ENDPOINTS

### 6.1 Authentication APIs
```
POST /api/auth/auth-account          # Login
POST /api/auth/create-account        # Register
POST /api/auth/login-google          # Google Login
POST /api/auth/verify-phonenumber    # OTP Verification
```

### 6.2 User Management APIs
```
GET  /api/user/profile               # Get profile
PUT  /api/user/update-profile        # Update profile
GET  /api/user/donation-history      # Donation history
```

### 6.3 Event Management APIs
```
GET  /api/donationevent              # Event list
POST /api/donationevent              # Create event
GET  /api/donationevent/{id}         # Event details
PUT  /api/donationevent/{id}         # Update event
```

### 6.4 Emergency APIs
```
POST /api/emergencydonation          # Create emergency event
GET  /api/emergencydonation/nearby   # Nearby emergency events
```

### 6.5 Other APIs
```
GET  /api/blog                       # Blog list
GET  /api/voucher                    # Voucher list
POST /api/chatgpt/completion         # AI Chat
GET  /api/notification               # Notifications
GET  /api/bloodtype                  # Blood types
```

---

## 7. OUTSTANDING FEATURES

### 7.1 Geolocation Intelligence
- Automatic user location detection
- Find nearest blood donation events
- Emergency notifications by radius
- Distance and travel time calculation

### 7.2 AI-Powered Chatbot
- GPT-4 for in-depth consultation
- Context understanding (location, blood type)
- Multi-language support (7 languages)
- Learning from chat history

### 7.3 Real-time Notifications
- SignalR for real-time notifications
- Firebase Cloud Messaging
- Blood type-based notifications
- High-priority emergency notifications

### 7.4 Gamification
- Point reward system
- Leaderboards
- Badges and achievements
- Vouchers and gifts

---

## 8. SECURITY & PRIVACY

### 8.1 Authentication & Authorization
- JWT Token with refresh token
- Role-based access control
- Password encryption with BCrypt
- Session management

### 8.2 Data Protection
- HTTPS for all API calls
- Sensitive data encryption
- Validation and sanitization
- SQL Injection prevention

### 8.3 Privacy Compliance
- Data protection regulation compliance
- User data deletion capability
- Transparent data usage
- User consent management

---

## 9. TESTING & QUALITY ASSURANCE

### 9.1 Backend Testing
- Unit Tests for Business Logic
- Integration Tests for APIs
- Database Testing with In-Memory DB
- Performance Testing

### 9.2 Mobile Testing
- Widget Testing
- Integration Testing
- Platform-specific Testing (Android/iOS)
- User Acceptance Testing

### 9.3 Quality Metrics
- Code Coverage > 80%
- API Response Time < 200ms
- Mobile App Size < 50MB
- Crash Rate < 0.1%

---

## 10. DEPLOYMENT & DEVOPS

### 10.1 Backend Deployment
- Docker containerization
- CI/CD pipeline
- Database migration scripts
- Environment configuration

### 10.2 Mobile Deployment
- Google Play Store (Android)
- Apple App Store (iOS)
- Code signing and certificates
- Beta testing with TestFlight/Play Console

---

## 11. CONCLUSION & FUTURE DEVELOPMENT

### 11.1 Achievements
- Successfully built complete BloodPlus system
- Successfully integrated modern technologies
- Met all functional requirements
- User-friendly and intuitive interface

### 11.2 Future Development Directions
- **Machine Learning**: Blood demand prediction
- **IoT Integration**: Medical device connectivity
- **Blockchain**: Blood traceability
- **AR/VR**: Blood donation education
- **Wearable Integration**: Health monitoring

### 11.3 Lessons Learned
- Importance of good architectural design
- Necessity of testing and quality assurance
- Value of AI and new technology integration
- Importance of UX/UI in medical applications

---

## 12. REFERENCES

1. Microsoft Documentation - ASP.NET Core
2. Flutter Documentation - flutter.dev
3. Firebase Documentation
4. OpenAI API Documentation
5. Clean Architecture - Robert C. Martin
6. Entity Framework Core Documentation
7. SignalR Documentation

---

**Student Signature**

[Student Name]  
[Date]
