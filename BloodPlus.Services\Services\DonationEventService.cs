﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.ModelViews.DonationEventModelView;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class DonationEventService : IDonationEventService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly OneSignalService _oneSignalService;

        public DonationEventService(IUnitOfWork unitOfWork, IMapper mapper, OneSignalService oneSignalService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _oneSignalService = oneSignalService;
        }

        public async Task<string> CreateDonationEventAsync(CreateDEModelView model, string userId)
        {
            try
            {
                var organization = await _unitOfWork.GetRepository<Organization>()
                    .Entities
                    .Where(o => o.Id == model.OrganizationId && !o.DeletedTime.HasValue)
                    .FirstOrDefaultAsync();

                if (organization == null)
                {
                    throw new Exception("Tổ chức không tồn tại.");
                }

                // Tạo mới event
                var donationEvent = _mapper.Map<DonationEvent>(model);
                donationEvent.CurrentDonors = 0;
                donationEvent.Image = organization.Image;
                donationEvent.CreatedTime = DateTime.Now;
                donationEvent.CreatedBy = userId;

                await _unitOfWork.GetRepository<DonationEvent>().InsertAsync(donationEvent);

                // Tìm user gần event
                var nearUsers = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .Where(u => !u.DeletedTime.HasValue &&
                                u.DeviceToken != null)
                    .ToListAsync();

                var sendNotificationTasks = new List<Task>();

                foreach (var user in nearUsers)
                {
                    // Gửi thông báo push song song
                    sendNotificationTasks.Add(_oneSignalService.SendNotificationAsync(
                        "Sự kiện hiến máu!",
                        $"Sự kiện {donationEvent.Title} sẽ diễn ra vào ngày {model.EventDate:dd/MM/yyyy}. Hãy tham gia nhé!",
                        user.Id.ToString(),
                        user.DeviceToken
                    ));

                    // Thêm thông báo vào DB
                    Notification notification = new()
                    {
                        UserId = user.Id,
                        Title = "Sự kiện hiến máu!",
                        Content = $"Sự kiện {donationEvent.Title} sẽ diễn ra vào ngày {model.EventDate:dd/MM/yyyy}. Hãy tham gia nhé!",
                        IsRead = false,
                        SentDate = DateTime.Now,
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now,
                    };

                    await _unitOfWork.GetRepository<Notification>().InsertAsync(notification);
                }

                // Lưu tất cả thay đổi (event + notifications)
                await _unitOfWork.SaveAsync();

                // Gửi tất cả notification đẩy song song
                await Task.WhenAll(sendNotificationTasks);

                return "DonationEvent added and notifications sent successfully.";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}", ex);
            }
        }




        public async Task<string> DeleteDonationEventAsync(string id, string userId)
        {
            try
            {
                var donationEvent = await _unitOfWork.GetRepository<DonationEvent>().GetByIdAsync(id);
                if (donationEvent == null || donationEvent.DeletedTime.HasValue)
                {
                    throw new Exception("can not find DonationEvent or deleted");
                }

                donationEvent.DeletedTime = DateTimeOffset.UtcNow;
                donationEvent.DeletedBy = userId;

                await _unitOfWork.GetRepository<DonationEvent>().UpdateAsync(donationEvent);
                await _unitOfWork.SaveAsync();

                return "DonationEvent delete successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<DonationEventModelView> GetDonationEventByIdAsync(string id)
        {
            try
            {
                var donationEvent = await _unitOfWork.GetRepository<DonationEvent>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == id && !u.DeletedTime.HasValue);

                if (donationEvent == null)
                {
                    throw new Exception("can not find or DonationEvent is deleted");
                }

                var DEModelView = _mapper.Map<DonationEventModelView>(donationEvent);
                DEModelView.OrganizationName = donationEvent.Organization.Name;
                DEModelView.Image = donationEvent.Image;

                return DEModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BasePaginatedList<ListDEModelView>> GetAllDonationEventAsync(int pageNumber, int pageSize, string? location, 
                                                                                       DateOnly? startDate, DateOnly? endDate, string? organization)
        {
            IQueryable<DonationEvent> doantionEventQuery = _unitOfWork.GetRepository<DonationEvent>()
                .Entities
                .Where(p => !p.DeletedTime.HasValue && p.IsEmergency == false)
                .OrderByDescending(s => s.CreatedTime);

            if (!string.IsNullOrWhiteSpace(location))
            {
                doantionEventQuery = doantionEventQuery.Where(p => p.Location.Contains(location));
            }

            if (!string.IsNullOrWhiteSpace(organization))
            {
                doantionEventQuery = doantionEventQuery.Where(p => p.Organization.Name.Contains(organization));
            }

            if (startDate.HasValue && endDate.HasValue)
            {
                DateTime startDateTime = startDate.Value.ToDateTime(TimeOnly.MinValue);
                DateTime endDateTime = endDate.Value.ToDateTime(TimeOnly.MaxValue);

                doantionEventQuery = doantionEventQuery.Where(p => p.EventDate >= startDateTime && p.EventDate <= endDateTime);
            }
            else if(startDate.HasValue)
            {
                DateTime startDateTime = startDate.Value.ToDateTime(TimeOnly.MinValue);
                DateTime endDateTime = startDate.Value.ToDateTime(TimeOnly.MaxValue);

                doantionEventQuery = doantionEventQuery.Where(p => p.EventDate >= startDateTime && p.EventDate <= endDateTime);
            }


            int totalCount = await doantionEventQuery.CountAsync();

            var result = await doantionEventQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(p => new ListDEModelView
                {
                    Id = p.Id,
                    Title = p.Title,
                    OrganizationName = p.Organization.Name,
                    Location = p.Location,
                    EventDate = p.EventDate,
                    EndTime = p.EndTime,
                    RequiredDonors = p.RequiredDonors,
                    CurrentDonors = p.CurrentDonors,
                    Image = p.Image
                })
                .ToListAsync();

            return new BasePaginatedList<ListDEModelView>(result, totalCount, pageNumber, pageSize);
        }


        public async Task<string> UpdateDonationEventAsync(string id, UpdateDEModelView model, string userId)
        {
            try
            {
                var donationEvent = await _unitOfWork.GetRepository<DonationEvent>().GetByIdAsync(id);
                if (donationEvent == null || donationEvent.DeletedTime.HasValue)
                {
                    throw new Exception("can not find or donationEvent is deleted");
                }

                _mapper.Map(model, donationEvent);
                donationEvent.LastUpdatedBy = userId;
                donationEvent.LastUpdatedTime = DateTimeOffset.UtcNow;

                await _unitOfWork.GetRepository<DonationEvent>().UpdateAsync(donationEvent);
                await _unitOfWork.SaveAsync();

                return "DonationEvent updated successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }
    }
}
