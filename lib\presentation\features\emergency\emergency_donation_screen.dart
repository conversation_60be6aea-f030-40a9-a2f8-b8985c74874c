import 'package:flutter/material.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';

import 'create_emergency_donation_form_screen.dart';
import 'package:bloodplusmobile/data/services/emergency_donation_service.dart';
import 'package:bloodplusmobile/data/models/emergency_donation_model.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'emergency_event_detail_screen.dart';
import 'emergency_appointment_form_screen.dart';

class EmergencyDonationScreen extends StatefulWidget {
  const EmergencyDonationScreen({super.key});

  @override
  State<EmergencyDonationScreen> createState() => _EmergencyDonationScreenState();
}

class _EmergencyDonationScreenState extends State<EmergencyDonationScreen> with TickerProviderStateMixin {
  final UserManager _userManager = UserManager();
  String? userRole;
  bool isLoading = true;
  bool isUser = true; // Add this line
  List<EmergencyDonationEventModel> emergencyDonations = [];
  bool isListLoading = false;
  int currentPage = 1;
  int totalPages = 1;
  bool hasNextPage = false;
  bool hasPreviousPage = false;

  late AnimationController _fadeController;
  late AnimationController _listController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _listController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutQuart),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _listController, curve: Curves.easeOutBack));
    loadUserRole();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _listController.dispose();
    super.dispose();
  }

  Future<void> loadUserRole() async {
    final role = await _userManager.getUserRole();
    setState(() {
      userRole = role;
      // Set isUser based on role
      if (role == 'User' || role == 'Admin') {
        isUser = false;
      } else if (role == 'Manager') {
        isUser = true;
      } else {
        isUser = true;
      }
      isLoading = false;
    });
    fetchEmergencyDonations();
  }

  Future<void> fetchEmergencyDonations({int page = 1}) async {
    setState(() { isListLoading = true; });
    try {
      final pagination = await EmergencyDonationService().getEmergencyEvents(page: page, isUser: isUser);
      setState(() {
        emergencyDonations = pagination.items;
        currentPage = pagination.currentPage;
        totalPages = pagination.totalPages;
        hasNextPage = pagination.hasNextPage;
        hasPreviousPage = pagination.hasPreviousPage;
        isListLoading = false;
      });
      _listController.forward();
    } catch (e) {
      setState(() { isListLoading = false; });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi tải danh sách: $e')),
      );
    }
  }

  Future<void> _onCreatePressed() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreateEmergencyDonationForm()),
    );
    if (result == true) {
      await fetchEmergencyDonations(page: currentPage);
      DialogHelper.showAnimatedSuccessDialog(
        context: context,
        title: 'Thành công',
        message: 'Tạo sự kiện khẩn cấp thành công!',
        buttonText: 'Đóng',
      );
    }
  }

  Future<void> _launchGoogleMaps(String location) async {
    final localizations = AppLocalizations.of(context);
    try {
      // Lấy vị trí hiện tại
      Position? position;
      try {
        bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
        if (!serviceEnabled) {
          await Geolocator.openLocationSettings();
          throw Exception('Location services are disabled.');
        }
        LocationPermission permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.denied) {
          permission = await Geolocator.requestPermission();
          if (permission == LocationPermission.denied) {
            throw Exception('Location permissions are denied');
          }
        }
        if (permission == LocationPermission.deniedForever) {
          throw Exception('Location permissions are permanently denied');
        }
        position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
      } catch (e) {
        position = null;
      }

      final String encodedLocation = Uri.encodeFull(location);
      String url;
      if (position != null) {
        // Chỉ đường từ vị trí hiện tại đến location
        url = 'https://www.google.com/maps/dir/?api=1&origin=${position.latitude},${position.longitude}&destination=$encodedLocation&travelmode=driving';
      } else {
        // Nếu không lấy được vị trí, chỉ mở tìm kiếm địa điểm
        url = 'https://www.google.com/maps/search/?api=1&query=$encodedLocation';
      }
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(localizations.translate('could_not_launch_google_maps') ?? 'Không thể mở Google Maps'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${localizations.translate('could_not_launch_google_maps') ?? 'Không thể mở Google Maps'}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text('Sự kiện hiến máu khẩn cấp', style: AppTheme.headingMedium, selectionColor: Colors.white),
        backgroundColor: AppTheme.primaryRed,
        elevation: 2,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: isListLoading
              ? const Center(child: CircularProgressIndicator())
              : emergencyDonations.isEmpty
                  ? _buildEmptyState(localizations, isSmallScreen)
                  : Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                            itemCount: emergencyDonations.length,
                            itemBuilder: (context, index) {
                              final event = emergencyDonations[index];
                              final progress = event.requiredDonors == 0 ? 0.0 : event.currentDonors / event.requiredDonors;
                              return TweenAnimationBuilder<double>(
                                duration: Duration(milliseconds: 300 + (index * 100)),
                                tween: Tween(begin: 0.0, end: 1.0),
                                builder: (context, value, child) {
                                  return Transform.translate(
                                    offset: Offset(0, 30 * (1 - value)),
                                    child: Opacity(
                                      opacity: value,
                                      child: _EmergencyEventCard(
                                        event: event,
                                        progress: progress,
                                        isSmallScreen: isSmallScreen,
                                        onLocationTap: () => _launchGoogleMaps(event.location),
                                        onTap: userRole == 'User'
                                            ? () async {
                                                final result = await Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => EmergencyAppointmentFormScreen(event: event),
                                                  ),
                                                );
                                                if (result == true) {
                                                  await fetchEmergencyDonations(page: currentPage);
                                                  if (context.mounted) {
                                                    ScaffoldMessenger.of(context).showSnackBar(
                                                      const SnackBar(content: Text('Đăng ký thành công!')),
                                                    );
                                                  }
                                                }
                                              }
                                            : (userRole == 'Manager' || userRole == 'Admin')
                                                ? () async {
                                                    final detail = await EmergencyDonationService().getEmergencyEventDetail(event.id);
                                                    if (!mounted) return;
                                                    await Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) => EmergencyEventDetailScreen(eventDetail: detail),
                                                      ),
                                                    );
                                                  }
                                                : null,
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                        if (hasNextPage || hasPreviousPage)
                          Padding(
                            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                ElevatedButton(
                                  onPressed: hasPreviousPage && currentPage > 1 && !isListLoading
                                      ? () => fetchEmergencyDonations(page: currentPage - 1)
                                      : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryRed,
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16)),
                                  ),
                                  child: Text('Trước', style: AppTheme.bodyLarge.copyWith(color: Colors.white)),
                                ),
                                Text('Trang $currentPage / $totalPages', style: AppTheme.bodyLarge),
                                ElevatedButton(
                                  onPressed: hasNextPage && !isListLoading
                                      ? () => fetchEmergencyDonations(page: currentPage + 1)
                                      : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryRed,
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16)),
                                  ),
                                  child: Text('Sau', style: AppTheme.bodyLarge.copyWith(color: Colors.white)),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: userRole == 'Manager'
          ? Padding(
        padding: const EdgeInsets.only(bottom: 30.0), // Di chuyển lên 5dp
        child: FloatingActionButton.extended(
          onPressed: _onCreatePressed,
          backgroundColor: AppTheme.primaryRed,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text(
            'Tạo sự kiện',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      )
          : null,
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations, bool isSmallScreen) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 24 : 40),
        child: Column(
          mainAxisSize: MainAxisSize.min, // quan trọng để tránh chiếm toàn bộ chiều cao
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
              decoration: BoxDecoration(
                color: AppTheme.primaryRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
              ),
              child: Icon(
                Icons.event_busy,
                size: isSmallScreen ? 36 : 48,
                color: AppTheme.primaryRed,
              ),
            ),
            SizedBox(height: isSmallScreen ? 16 : 24),
            Text(
              'Không có sự kiện khẩn cấp nào.',
              textAlign: TextAlign.center,
              style: AppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _EmergencyEventCard extends StatelessWidget {
  final EmergencyDonationEventModel event;
  final double progress;
  final bool isSmallScreen;
  final VoidCallback onLocationTap;
  final VoidCallback? onTap;

  const _EmergencyEventCard({
    required this.event,
    required this.progress,
    required this.isSmallScreen,
    required this.onLocationTap,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryRed.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
          child: Column(
            children: [
              Container(
                height: isSmallScreen ? 3 : 4,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.primaryRed.withOpacity(0.3),
                      AppTheme.primaryRed,
                      AppColors.darkRed,
                    ],
                    stops: [0.0, progress, 1.0],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: isSmallScreen ? 60 : 80,
                          height: isSmallScreen ? 60 : 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.primaryRed.withOpacity(0.1),
                                AppTheme.primaryRed.withOpacity(0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                            border: Border.all(
                              color: AppTheme.primaryRed.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                            child: event.imageUrl != null && event.imageUrl!.isNotEmpty
                                ? Image.network(event.imageUrl!, fit: BoxFit.contain, errorBuilder: (context, error, stackTrace) => Icon(Icons.warning, color: AppTheme.primaryRed, size: isSmallScreen ? 24 : 36))
                                : Icon(Icons.warning, color: AppTheme.primaryRed, size: isSmallScreen ? 24 : 36),
                          ),
                        ),
                        SizedBox(width: isSmallScreen ? 8 : 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(event.title, style: AppTheme.headingSmall.copyWith(fontWeight: FontWeight.bold, fontSize: isSmallScreen ? 14 : 16, color: AppTheme.textPrimary, height: 1.3), maxLines: 2, overflow: TextOverflow.ellipsis),
                              SizedBox(height: isSmallScreen ? 6 : 8),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryRed.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                  border: Border.all(color: AppTheme.primaryRed.withOpacity(0.3)),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.business, size: isSmallScreen ? 10 : 12, color: AppTheme.primaryRed),
                                    SizedBox(width: isSmallScreen ? 2 : 4),
                                    Text(event.organizationName, style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 12, fontWeight: FontWeight.w500, color: AppTheme.primaryRed), maxLines: 1, overflow: TextOverflow.ellipsis),
                                  ],
                                ),
                              ),
                              SizedBox(height: isSmallScreen ? 6 : 8),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                                decoration: BoxDecoration(
                                  color: Colors.red.withOpacity(0.08),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                  border: Border.all(color: Colors.red.withOpacity(0.2)),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.bloodtype, size: isSmallScreen ? 10 : 12, color: Colors.red),
                                    SizedBox(width: isSmallScreen ? 2 : 4),
                                    Text(event.requiredBloodTypeName, style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 12, fontWeight: FontWeight.w500, color: Colors.red), maxLines: 1, overflow: TextOverflow.ellipsis),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 16),
                    Container(
                      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8FAFC),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                        border: Border.all(color: AppTheme.primaryRed.withOpacity(0.1)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryRed.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                                  ),
                                  child: Icon(Icons.calendar_today, size: isSmallScreen ? 14 : 16, color: AppTheme.primaryRed),
                                ),
                                SizedBox(width: isSmallScreen ? 6 : 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('Ngày', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                                      Text(_formatDate(event.eventDate), style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 13, fontWeight: FontWeight.w600, color: AppTheme.textPrimary)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            height: isSmallScreen ? 30 : 40,
                            width: 1,
                            color: AppTheme.borderColor,
                            margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12),
                          ),
                          Expanded(
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryRed.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                                  ),
                                  child: Icon(Icons.people, size: isSmallScreen ? 14 : 16, color: AppTheme.primaryRed),
                                ),
                                SizedBox(width: isSmallScreen ? 6 : 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('Người cần', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                                      Text('${event.requiredDonors}', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 13, fontWeight: FontWeight.w600, color: AppTheme.textPrimary)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 16),
                    GestureDetector(
                      onTap: onLocationTap,
                      child: Container(
                        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppTheme.primaryRed.withOpacity(0.05),
                              AppTheme.primaryRed.withOpacity(0.02),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                          border: Border.all(color: AppTheme.primaryRed.withOpacity(0.2)),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryRed.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                              ),
                              child: Icon(Icons.location_on, size: isSmallScreen ? 16 : 18, color: AppTheme.primaryRed),
                            ),
                            SizedBox(width: isSmallScreen ? 8 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Địa điểm', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                                  SizedBox(height: isSmallScreen ? 2 : 2),
                                  Text(event.location, style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 14, fontWeight: FontWeight.w600, color: AppTheme.primaryRed, decoration: TextDecoration.underline, decorationColor: AppTheme.primaryRed.withOpacity(0.5)), maxLines: 2, overflow: TextOverflow.ellipsis),
                                ],
                              ),
                            ),
                            Icon(Icons.open_in_new, size: isSmallScreen ? 14 : 16, color: AppTheme.primaryRed.withOpacity(0.7)),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Đã có', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 13, fontWeight: FontWeight.w600, color: AppTheme.textPrimary)),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 2 : 4),
                              decoration: BoxDecoration(
                                color: progress >= 1.0 ? Colors.green.withOpacity(0.1) : progress >= 0.8 ? Colors.orange.withOpacity(0.1) : AppTheme.primaryRed.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                border: Border.all(color: progress >= 1.0 ? Colors.green.withOpacity(0.3) : progress >= 0.8 ? Colors.orange.withOpacity(0.3) : AppTheme.primaryRed.withOpacity(0.3)),
                              ),
                              child: Text('${event.currentDonors}/${event.requiredDonors}', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 12, fontWeight: FontWeight.w700, color: progress >= 1.0 ? Colors.green : progress >= 0.8 ? Colors.orange : AppTheme.primaryRed)),
                            ),
                          ],
                        ),
                        SizedBox(height: isSmallScreen ? 6 : 8),
                        Container(
                          height: isSmallScreen ? 6 : 8,
                          decoration: BoxDecoration(
                            color: AppTheme.borderColor.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(isSmallScreen ? 3 : 4),
                          ),
                          child: Stack(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: AppTheme.borderColor.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 3 : 4),
                                ),
                              ),
                              FractionallySizedBox(
                                widthFactor: progress.clamp(0.0, 1.0),
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: progress >= 1.0 ? [Colors.green, Colors.green.shade700] : progress >= 0.8 ? [Colors.orange, Colors.orange.shade700] : [AppTheme.primaryRed, AppColors.darkRed],
                                    ),
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 3 : 4),
                                    boxShadow: [
                                      BoxShadow(
                                        color: (progress >= 1.0 ? Colors.green : progress >= 0.8 ? Colors.orange : AppTheme.primaryRed).withOpacity(0.3),
                                        blurRadius: isSmallScreen ? 3 : 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: isSmallScreen ? 6 : 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('${(progress * 100).toInt()}% hoàn thành', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 6 : 8, vertical: isSmallScreen ? 1 : 2),
                              decoration: BoxDecoration(
                                color: progress >= 1.0 ? Colors.green.withOpacity(0.1) : progress >= 0.8 ? Colors.orange.withOpacity(0.1) : AppTheme.primaryRed.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                              ),
                              child: Text(progress >= 1.0 ? 'Đủ người' : progress >= 0.8 ? 'Gần đủ' : 'Còn chỗ', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 9 : 10, fontWeight: FontWeight.w600, color: progress >= 1.0 ? Colors.green : progress >= 0.8 ? Colors.orange : AppTheme.primaryRed)),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
