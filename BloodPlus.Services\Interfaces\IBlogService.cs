﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.BlogModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface IBlogService
    {
        Task<string> CreateBlogAsync(CreateBlogModelView model, string userId);
        Task<string> DeleteBlogAsync(string id, string userId);
        Task<BlogModelView> GetBlogByIdAsync(string id);
        Task<BasePaginatedList<ListBlogModelView>> GetAllBlogAsync(int pageNumber, int pageSize, string? title);
        Task<string> UpdateBlogAsync(string id, CreateBlogModelView model, string userId);
    }
}
