﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.ModelViews.OrganizationModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class OrganizationService : IOrganizationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly FirebaseService _firebaseService;

        public OrganizationService(IUnitOfWork unitOfWork, IMapper mapper, FirebaseService firebaseService)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _firebaseService = firebaseService;
        }

        public async Task<string> CreateOrganizationAsync(CreateOrganizationModelView model, string userId)
        {
            try
            {
                Organization organization = _mapper.Map<Organization>(model);
                organization.UserId = Guid.Parse(userId);
                organization.Image = await _firebaseService.UploadImageAsync(model.OrganizationImage);
                organization.CreatedTime = DateTimeOffset.UtcNow;
                organization.CreatedBy = userId;

                await _unitOfWork.GetRepository<Organization>().InsertAsync(organization);
                await _unitOfWork.SaveAsync();

                return "Organization added successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<string> DeleteOrganizationAsync(string id, string userId)
        {
            try
            {
                var organization = await _unitOfWork.GetRepository<Organization>().GetByIdAsync(id);
                if (organization == null || organization.DeletedTime.HasValue)
                {
                    throw new Exception("can not find Organization or deleted");
                }

                organization.DeletedTime = DateTimeOffset.UtcNow;
                organization.DeletedBy = userId;

                await _unitOfWork.GetRepository<Organization>().UpdateAsync(organization);
                await _unitOfWork.SaveAsync();

                return "Organization delete successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<OrganizationModelView> GetOrganizationByIdAsync(string id)
        {
            try
            {
                var organization = await _unitOfWork.GetRepository<Organization>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == id && !u.DeletedTime.HasValue);

                if (organization == null)
                {
                    throw new Exception("can not find or organization is deleted");
                }

                var organizationModelView = _mapper.Map<OrganizationModelView>(organization);

                return organizationModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BasePaginatedList<OrganizationModelView>> GetAllOrganizationAsync(int pageNumber, int pageSize, string? name, string? address)
        {
            IQueryable<Organization> organizationQuery = _unitOfWork.GetRepository<Organization>()
                .Entities
                .Where(p => !p.DeletedTime.HasValue)
                .OrderByDescending(s => s.CreatedTime);

            if (!string.IsNullOrWhiteSpace(name))
            {
                organizationQuery = organizationQuery.Where(p => p.Name.Contains(name));
            }

            if (!string.IsNullOrWhiteSpace(address))
            {
                organizationQuery = organizationQuery.Where(p => p.Address.Contains(address));
            }

            int totalCount = await organizationQuery.CountAsync();

            List<Organization> paginatedServices = await organizationQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            List<OrganizationModelView> organizationModelView = _mapper.Map<List<OrganizationModelView>>(paginatedServices);

            return new BasePaginatedList<OrganizationModelView>(organizationModelView, totalCount, pageNumber, pageSize);
        }

        public async Task<string> UpdateOrganizationAsync(string id, CreateOrganizationModelView model, string userId)
        {
            try
            {
                var organization = await _unitOfWork.GetRepository<Organization>().GetByIdAsync(id);
                if (organization == null || organization.DeletedTime.HasValue)
                {
                    throw new Exception("can not find or Organization is deleted");
                }

                _mapper.Map(model, organization);
                organization.Image = await _firebaseService.UploadImageAsync(model.OrganizationImage);
                organization.LastUpdatedBy = userId;
                organization.LastUpdatedTime = DateTimeOffset.UtcNow;

                await _unitOfWork.GetRepository<Organization>().UpdateAsync(organization);
                await _unitOfWork.SaveAsync();

                return "Organization updated successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<List<OrganizationModelView>> GetOrganizationByUserIdAsync(string userId)
        {
            try
            {
                var organization = await _unitOfWork.GetRepository<Organization>()
                    .Entities
                    .Where(u => u.UserId.ToString() == userId && !u.DeletedTime.HasValue)
                    .ToListAsync();

                if (organization == null || organization.Count == 0)
                {
                    throw new Exception("Không tìm thấy tổ chức hoặc tổ chức đã bị xóa");
                }

                var organizationModelView = _mapper.Map<List<OrganizationModelView>>(organization);
                return organizationModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }
    }
}
