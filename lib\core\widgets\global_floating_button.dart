import 'dart:async';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';

class DraggableFloatingButton extends StatefulWidget {
  const DraggableFloatingButton({super.key});

  @override
  State<DraggableFloatingButton> createState() => _DraggableFloatingButtonState();
}

class _DraggableFloatingButtonState extends State<DraggableFloatingButton>
    with TickerProviderStateMixin {
  Offset position = const Offset(300, 600);
  double opacity = 1.0;
  Timer? _fadeTimer;
  bool _isDragging = false;
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for attention
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Scale animation for interactions
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenSize = MediaQuery.of(context).size;
      final defaultX = screenSize.width - 64 + 16;
      final defaultY = screenSize.height - 64 - 150;
      setState(() {
        position = Offset(defaultX, defaultY);
      });
    });

    _startFadeTimer();
    _startPulseAnimation();
  }

  void _startPulseAnimation() {
    _pulseController.repeat(reverse: true);
  }

  void _startFadeTimer() {
    _fadeTimer?.cancel();
    _fadeTimer = Timer(const Duration(seconds: 4), () {
      if (mounted && !_isDragging) {
        setState(() => opacity = 0.6);
      }
    });
  }

  void _onTap() {
    NavigationService.navigateToChatAI();
    setState(() => opacity = 1.0);
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });
    _startFadeTimer();
  }

  @override
  void dispose() {
    _fadeTimer?.cancel();
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    const buttonSize = 64.0;
    const halfSize = buttonSize / 2;
    const safeMargin = 8.0;

    return Positioned(
      left: position.dx,
      top: position.dy,
      child: GestureDetector(
        onTap: _onTap,
        onPanStart: (_) {
          setState(() {
            opacity = 1.0;
            _isDragging = true;
          });
          _fadeTimer?.cancel();
          _pulseController.stop();
        },
        onPanUpdate: (details) {
          final newPos = position + details.delta;

          final x = newPos.dx.clamp(
            -halfSize + safeMargin,
            screenSize.width - halfSize - safeMargin,
          );
          final y = newPos.dy.clamp(
            safeMargin + MediaQuery.of(context).padding.top,
            screenSize.height - buttonSize - 100,
          );

          setState(() => position = Offset(x, y));
        },
        onPanEnd: (_) {
          final isLeft = position.dx < screenSize.width / 2;
          final newX = isLeft
              ? -halfSize + safeMargin
              : screenSize.width - halfSize - safeMargin;

          setState(() {
            position = Offset(newX, position.dy);
            _isDragging = false;
          });

          _startFadeTimer();
          _startPulseAnimation();
        },
        child: AnimatedBuilder(
          animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value * (_isDragging ? 1.0 : _pulseAnimation.value),
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: opacity,
                child: Container(
                  height: buttonSize,
                  width: buttonSize,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xffff0000),
                        Color(0xffff0000),
                      ],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xffff0000).withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Shine effect
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      // Main icon
                      Center(
                        child: Image.asset(
                          'assets/icons/robot.png',
                          width: 32,
                          height: 32,
                          fit: BoxFit.contain,
                        ),
                      ),
                      // Notification dot (optional - có thể thêm logic để hiển thị)
                      if (false) // Thay đổi thành true khi có notification
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.redAccent,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}