import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/presentation/features/auth/register_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  bool _obscureText = true;
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
    _checkSessionExpired();
    _prefillUsername();
  }

  Future<void> _prefillUsername() async {
    final prefs = await SharedPreferences.getInstance();
    final lastUsername = prefs.getString('last_login_username');
    if (lastUsername != null && lastUsername.isNotEmpty) {
      _emailController.text = lastUsername;
    }
  }

  Future<void> _checkSessionExpired() async {
    final prefs = await SharedPreferences.getInstance();
    final expired = prefs.getBool('session_expired') ?? false;
    if (expired && mounted) {
      final localizations = AppLocalizations.of(context);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showSnackBar(
          localizations.translate('session_expired'),
          isError: true,
        );
      });
      await prefs.remove('session_expired');
    }
  }

  @override
  void dispose() {
    _scaffoldMessengerKey.currentState?.clearSnackBars();
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    final localizations = AppLocalizations.of(context);
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      _showSnackBar(
        localizations.translate('please_fill_all_fields'),
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
      final authService = AuthService(appStateNotifier: appStateNotifier);
      final response = await authService.login(
        _emailController.text,
        _passwordController.text,
        {
          'login_success': localizations.translate('login_success'),
          'login_failed': localizations.translate('login_failed'),
          'connection_error': localizations.translate('connection_error'),
        },
      );

      final userManager = UserManager();
      await userManager.saveUserToken(response['accessToken']);


      _showSnackBar(
        localizations.translate('login_successful'),
        isError: false,
      );
      // Wait for SnackBar duration
      await Future.delayed(const Duration(seconds: 2));
      // Clear any active SnackBars before navigation
      _scaffoldMessengerKey.currentState?.clearSnackBars();
      if (mounted) {
        NavigationService.navigateToHome();
        appStateNotifier.setLoggedIn(true);
      }
    } catch (e) {
      _showSnackBar(
        localizations.translate('login_failed'),
        isError: true,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleGoogleLogin() async {
    final localizations = AppLocalizations.of(context);
    setState(() => _isLoading = true);

    try {
      final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
      final authService = AuthService(appStateNotifier: appStateNotifier);

      final response = await authService.loginWithGoogle({
        'login_success': localizations.translate('login_success'),
        'login_failed': localizations.translate('login_failed'),
        'connection_error': localizations.translate('connection_error'),
      });

      if (response.containsKey('error')) {
        if (response['error'] == localizations.translate('google_login_cancelled')) {
          return;
        }
        throw Exception(response['error']);
      }

      if (!response.containsKey('accessToken') || response['accessToken'] == null) {
        throw Exception(localizations.translate('no_token_received'));
      }

      final userManager = UserManager();
      await userManager.saveUserToken(response['accessToken']);


      _showSnackBar(
        localizations.translate('login_successful'),
        isError: false,
      );
      // Wait for SnackBar duration
      await Future.delayed(const Duration(seconds: 2));
      // Clear any active SnackBars before navigation
      _scaffoldMessengerKey.currentState?.clearSnackBars();
      if (mounted) {
        NavigationService.navigateToHome();
        appStateNotifier.setLoggedIn(true);
      }
    } catch (e) {
      String errorMessage = _getGoogleLoginErrorMessage(e.toString());
      _showSnackBar(errorMessage, isError: true);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    _scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppTheme.primaryRed : Colors.green,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }

  String _getGoogleLoginErrorMessage(String error) {
    final localizations = AppLocalizations.of(context);
    final errorLower = error.toLowerCase();

    if (errorLower.contains('tài khoản chưa tồn tại') ||
        errorLower.contains('không thể tạo tài khoản tự động')) {
      return localizations.translate('creating_google_account');
    } else if (errorLower.contains('đăng ký thành công nhưng đăng nhập lại thất bại')) {
      return localizations.translate('account_created_success');
    } else if (errorLower.contains('lỗi kết nối') || errorLower.contains('connection')) {
      return localizations.translate('network_connection_error');
    } else if (errorLower.contains('không nhận được token')) {
      return localizations.translate('server_auth_error');
    } else if (errorLower.contains('không thể tạo phiên đăng nhập tạm thời')) {
      return localizations.translate('system_error');
    } else {
      return localizations.translate('google_login_failed');
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    required IconData prefixIcon,
    bool isPassword = false,
    Widget? suffixIcon,
  }) {
    return Container(
      decoration: AppTheme.cardDecoration,
      child: TextField(
        controller: controller,
        obscureText: isPassword ? _obscureText : false,
        style: AppTheme.bodyLarge,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: AppTheme.bodyMedium,
          prefixIcon: Icon(
            prefixIcon,
            color: AppTheme.primaryRed,
            size: 24,
          ),
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 20,
            horizontal: 16,
          ),
        ),
        keyboardType: isPassword ? TextInputType.text : TextInputType.emailAddress,
      ),
    );
  }

  Widget _buildLoginButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryRed.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            color: Colors.white,
            strokeWidth: 2,
          ),
        )
            : Text(
          AppLocalizations.of(context).translate('login'),
          style: AppTheme.buttonText,
        ),
      ),
    );
  }

  Widget _buildGoogleButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: AppTheme.cardDecoration,
      child: OutlinedButton(
        onPressed: _isLoading ? null : _handleGoogleLogin,
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.white,
          side: BorderSide(color: AppTheme.borderColor, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/google_logo.png',
              width: 24,
              height: 24,
            ),
            const SizedBox(width: 12),
            Text(
              AppLocalizations.of(context).translate('sign_in_with_google'),
              style: AppTheme.bodyLarge.copyWith(color: AppTheme.textPrimary),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      key: _scaffoldMessengerKey,
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.backgroundColor,
              Colors.white,
              AppTheme.backgroundColor,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    children: [
                      const SizedBox(height: 20),

                      // Logo and Header Section
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: AppTheme.glassmorphismDecoration,
                        child: Column(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: AppTheme.primaryRed,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.primaryRed.withValues(alpha: 0.3),
                                    blurRadius: 16,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: Image.asset(
                                'assets/icons/logo_origin.png',
                                width: 100,
                                height: 100,
                                color: Colors.white,
                                fit: BoxFit.contain,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Máu Cộng',
                              style: AppTheme.headingLarge.copyWith(
                                color: AppTheme.primaryRed,
                                fontSize: 28,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              localizations.translate('Welcome!'),
                              style: AppTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Login Form Section
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: AppTheme.elevatedCardDecoration,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('login'),
                              style: AppTheme.headingMedium,
                            ),
                            const SizedBox(height: 24),

                            // Email Field
                            _buildTextField(
                              controller: _emailController,
                              hintText: localizations.translate('enter_your_username'),
                              prefixIcon: Icons.account_circle_outlined,
                            ),

                            const SizedBox(height: 16),

                            // Password Field
                            _buildTextField(
                              controller: _passwordController,
                              hintText: localizations.translate('enter_your_password'),
                              prefixIcon: Icons.lock_outline,
                              isPassword: true,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureText ? Icons.visibility_off : Icons.visibility,
                                  color: AppTheme.textSecondary,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureText = !_obscureText;
                                  });
                                },
                              ),
                            ),

                            const SizedBox(height: 8),

                            // Forgot Password
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                onPressed: () {
                                  NavigationService.navigateTo(AppRoutes.forgotPassword);
                                },
                                child: Text(
                                  localizations.translate('forgot_password'),
                                  style: AppTheme.bodySmall.copyWith(
                                    color: AppTheme.primaryRed,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Login Button
                            _buildLoginButton(),

                            const SizedBox(height: 24),

                            // Divider
                            Row(
                              children: [
                                Expanded(
                                  child: Divider(color: AppTheme.borderColor),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16),
                                  child: Text(
                                    localizations.translate('or_login_with'),
                                    style: AppTheme.bodySmall,
                                  ),
                                ),
                                Expanded(
                                  child: Divider(color: AppTheme.borderColor),
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // Google Login Button
                            _buildGoogleButton(),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Sign Up Section
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            localizations.translate('dont_have_account'),
                            style: AppTheme.bodyMedium,
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => const RegisterScreen(),
                                ),
                              );
                            },
                            child: Text(
                              localizations.translate('sign_up'),
                              style: AppTheme.bodyMedium.copyWith(
                                color: AppTheme.primaryRed,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}