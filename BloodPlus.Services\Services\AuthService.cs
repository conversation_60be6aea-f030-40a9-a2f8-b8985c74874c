﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Utils;
using BloodPlus.ModelViews.AuthModelViews;
using BloodPlus.ModelViews.UserModelViews;
using BloodPlus.Services.Interfaces;
using Google.Apis.Auth;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPasswordHasher<User> _passwordHasher;
        private readonly FirebaseService _firebaseService;
        private readonly SendMailService _sendMailService;

        public AuthService(IUnitOfWork unitOfWork, IPasswordHasher<User> passwordHasher, FirebaseService firebaseService, SendMailService sendMailService)
        {
            _unitOfWork = unitOfWork;
            _passwordHasher = passwordHasher;
            _firebaseService = firebaseService;
            _sendMailService = sendMailService;
        }

        public async Task<User> AuthenticateAsync(LoginModelView model)
        {
            var accountRepository = _unitOfWork.GetRepository<User>();

            var user = await accountRepository.Entities
                .FirstOrDefaultAsync(x => (x.UserName == model.Username || x.Email == model.Username || x.PhoneNumber == model.Username) && 
                    !x.DeletedTime.HasValue && x.EmailConfirmed == true);

            if (user == null)
            {
                return null; 
            }

            var passwordVerificationResult = _passwordHasher.VerifyHashedPassword(user, user.PasswordHash, model.Password);

            if (passwordVerificationResult == PasswordVerificationResult.Failed)
            {
                return null; 
            }

            if (user.DeviceToken != model.DeviceToken)
            {
                user.DeviceToken = model.DeviceToken ?? "N/A";
                user.LastUpdatedTime = DateTime.Now;
                await accountRepository.UpdateAsync(user);
                await _unitOfWork.SaveAsync();
            }

            return user;
        }

        public async Task<User> ValidateGoogleTokenAsync(string idToken, string deviceToken)
        {
            try
            {
                var settings = new GoogleJsonWebSignature.ValidationSettings()
                {
                    Audience = new List<string>() { "************-0v8qphjmrav3mlh3j9pi4u2459ffcva2.apps.googleusercontent.com" }
                };

                var payload = await GoogleJsonWebSignature.ValidateAsync(idToken, settings); 

                var user = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .FirstOrDefaultAsync(x => x.Email == payload.Email && payload.EmailVerified == true && !x.DeletedTime.HasValue);

                if (user == null)
                {
                    var newAccount = new User
                    {
                        Id = Guid.NewGuid(),
                        Email = payload.Email,
                        Name = payload.Name,
                        DonationCount = 0,
                        Job = "N/A",
                        UserImage = "N/A",
                        Gender =Core.Enum.Gender.Other,
                        Address = "N/A",
                        Point = 0,
                        SecurityStamp = Guid.NewGuid().ToString(),
                        DeviceToken = deviceToken ?? "N/A",
                        CreatedTime = DateTimeOffset.UtcNow,
                        EmailConfirmed = true,
                    };
                    newAccount.CreatedBy = newAccount.Id.ToString();

                    var role = await _unitOfWork.GetRepository<Role>().Entities.FirstOrDefaultAsync(r => r.Name == "User");
                    var userRole = new UserRole
                    {
                        UserId = newAccount.Id,
                        RoleId = role.Id,
                        CreatedBy = newAccount.Id.ToString(),
                        CreatedTime = DateTime.UtcNow,
                    };

                    await _unitOfWork.GetRepository<UserRole>().InsertAsync(userRole);
                    await _unitOfWork.GetRepository<User>().InsertAsync(newAccount);
                    await _unitOfWork.SaveAsync();
                }
                else if(user.DeviceToken != deviceToken)
                {
                    user.DeviceToken = deviceToken ?? "N/A";
                    user.LastUpdatedTime = DateTime.Now;
                    await _unitOfWork.GetRepository<User>().UpdateAsync(user);
                    await _unitOfWork.SaveAsync();
                }

                return user;
            }
            catch
            {
                return null;
            }
        }


        public async Task<string> CreateAccountOtpAsync(CreateAccountModelView model)
        {
            try
            {
                var userRepo = _unitOfWork.GetRepository<User>().Entities;

                bool emailExists = await userRepo.AnyAsync(u => u.Email == model.Email && u.EmailConfirmed == true);
                if (emailExists)
                    throw new Exception("Email already in use");

                if(model.PhoneNumber != null)
                {
                    bool phoneExists = await userRepo.AnyAsync(u => u.PhoneNumber == model.PhoneNumber && u.EmailConfirmed == true);
                    if (phoneExists)
                        throw new Exception("PhoneNumber already in use");
                }

                bool usernameExists = await userRepo.AnyAsync(u => u.UserName == model.UserName && u.EmailConfirmed == true);
                if (usernameExists)
                    throw new Exception("UserName already in use");


                var blood = await _unitOfWork.GetRepository<BloodType>()
                    .Entities
                    .FirstOrDefaultAsync(b => b.BloodName == model.BloodType && !b.DeletedTime.HasValue);

                if (blood == null)
                    throw new Exception("Invalid BloodType");

                var user = new User
                {
                    Id = Guid.NewGuid(),
                    UserName = model.UserName,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber ?? "N/A",
                    Name = model.Name,
                    DateOfBirth = model.DateOfBirth,
                    Address = model.Address ?? "N/A",
                    Point = 0,
                    DonationCount = 0,
                    Job = model.Job ?? "N/A",
                    Gender = model.Gender,
                    PassportNumber = model.PassportNumber ?? "N/A",
                    BloodTypeId = blood.Id,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    CreatedTime = CoreHelper.SystemTimeNow,
                    EmailConfirmed = false,
                };

                if (model.UserImage != null)
                {
                    user.UserImage = await _firebaseService.UploadImageAsync(model.UserImage);
                }
                else
                {
                    user.UserImage = "N/A";
                }

                user.PasswordHash = _passwordHasher.HashPassword(user, model.Password);
                user.CreatedBy = user.Id.ToString();

                await _unitOfWork.GetRepository<User>().InsertAsync(user);

                await _unitOfWork.SaveAsync();

                // Gửi otp để xác nhận
                await _sendMailService.SendOtpAsync(user.Email);

                return "Send otp successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi hệ thống: {ex.Message}");
            }
        }


        public async Task<string> VerifyOtpAsync(VerifyOtpModel model)
        {
            try
            {
                var otpEntity = await _unitOfWork.GetRepository<Otp>()
                    .Entities
                    .Where(o => o.Email == model.Email && o.OtpNumber == model.Otp)
                    .OrderByDescending(u => u.CreatedTime)
                    .FirstOrDefaultAsync();

                if (otpEntity == null)
                {
                    return "OTP không chính xác";
                }

                // Kiểm tra hết hạn dựa trên CreateTime
                if (otpEntity.CreatedTime.AddMinutes(3) < DateTime.UtcNow)
                {
                    // Xoá OTP hết hạn
                    var userCache = await _unitOfWork.GetRepository<User>()
                        .Entities
                        .FirstOrDefaultAsync(u => u.Email == model.Email && u.EmailConfirmed == false);

                    if (userCache != null)
                    {
                        await _unitOfWork.GetRepository<User>().DeleteAsync(userCache);
                    }
                    await _unitOfWork.GetRepository<Otp>().DeleteAsync(otpEntity);
                    await _unitOfWork.SaveAsync();
                    return "OTP đã hết hạn";
                }

                // Lấy thông tin user và role cùng lúc
                var user = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .Where(u => u.Email == model.Email && u.EmailConfirmed == false && !u.DeletedTime.HasValue)
                    .FirstOrDefaultAsync();

                if (user == null)
                {
                    return "User không hợp lệ";
                }

                // Xác nhận email và cấp quyền cho user
                user.EmailConfirmed = true;

                var role = await _unitOfWork.GetRepository<Role>()
                    .Entities
                    .Where(r => r.Name == "User" && !r.DeletedTime.HasValue)
                    .FirstOrDefaultAsync();

                if (role == null)
                {
                    return "Role không hợp lệ";
                }

                // Thêm role cho user
                await _unitOfWork.GetRepository<UserRole>().InsertAsync(new UserRole
                {
                    UserId = user.Id,
                    RoleId = role.Id,
                    CreatedBy = user.Id.ToString(),
                    CreatedTime = DateTime.UtcNow,
                });

                // Xoá OTP đã sử dụng
                await _unitOfWork.GetRepository<Otp>().DeleteAsync(otpEntity);
                await _unitOfWork.SaveAsync();

                return "Xác thực OTP thành công và cấp quyền cho User";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }


        public async Task<string> ForgetPasswordAsync(ForgetPasswordModel model)
        {
            try
            {
                var userExists = await _unitOfWork.GetRepository<User>()
                    .Entities
                    .AnyAsync(u => u.Email == model.Email);

                if (!userExists)
                {
                    return "Email không tồn tại trong hệ thống";
                }

                await _sendMailService.SendOtpAsync(model.Email);

                return "Gửi OTP thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }

        public async Task<string> VerifyOtpForgetPasswordAsync(VerifyOtpModel model)
        {
            try
            {
                var otpEntity = await _unitOfWork.GetRepository<Otp>()
                    .Entities
                    .OrderByDescending(u => u.CreatedTime)
                    .FirstOrDefaultAsync(o => o.Email == model.Email && o.OtpNumber == model.Otp);

                if (otpEntity == null)
                {
                    return "OTP không chính xác";
                }

                // Kiểm tra hết hạn dựa trên CreateTime
                var expireTime = otpEntity.CreatedTime.AddMinutes(3);
                if (expireTime < DateTime.UtcNow)
                {
                    // Xoá OTP hết hạn
                    await _unitOfWork.GetRepository<Otp>().DeleteAsync(otpEntity);
                    await _unitOfWork.SaveAsync();

                    return "OTP đã hết hạn";
                }

                // OTP hợp lệ → xoá sau khi xác thực
                otpEntity.DeletedTime = DateTime.Now;
                await _unitOfWork.GetRepository<Otp>().UpdateAsync(otpEntity);
                await _unitOfWork.SaveAsync();

                return "Xác thực OTP thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }

        public async Task<string> ResetPasswordAsync(ResetPasswordModel model)
        {
            try
            {
                var userRepo = _unitOfWork.GetRepository<User>();
                var otpRepo = _unitOfWork.GetRepository<Otp>();

                var user = await userRepo.Entities.FirstOrDefaultAsync(u => u.Email == model.Email && !u.DeletedTime.HasValue);
                if (user == null)
                    throw new Exception("Người dùng không tồn tại");

                // Tìm OTP còn tồn tại (chưa bị xoá)
                var otpEntity = await otpRepo.Entities
                    .Where(o => o.Email == model.Email && o.DeletedTime != null)
                    .FirstOrDefaultAsync();

                if (otpEntity == null)
                    throw new Exception("OTP chưa được xác thực hoặc không hợp lệ");
                await otpRepo.DeleteAsync(otpEntity);

                // Hash mật khẩu mới và cập nhật
                user.PasswordHash = _passwordHasher.HashPassword(user, model.NewPassword);
                user.LastUpdatedTime = DateTime.UtcNow;
                user.LastUpdatedBy = user.Id.ToString();

                await userRepo.UpdateAsync(user);
                await _unitOfWork.SaveAsync();

                return "Đặt lại mật khẩu thành công";
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi: {ex.Message}");
            }
        }

    }
}
