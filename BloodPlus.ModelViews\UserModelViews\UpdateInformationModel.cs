﻿using BloodPlus.Core.Enum;
using Microsoft.AspNetCore.Http;

namespace BloodPlus.ModelViews.UserModelViews
{
    public class UpdateInformationModel
    {
        public string? BloodType { get; set; }
        public string? Name { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? Job { get; set; }
        public Gender? Gender { get; set; }
        public string? PassportNumber { get; set; }
        public IFormFile? UserImage { get; set; }
    }
}
