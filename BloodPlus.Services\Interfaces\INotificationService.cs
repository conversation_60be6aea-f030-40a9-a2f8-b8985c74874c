﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.NotificationModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface INotificationService
    {
        Task<BasePaginatedList<ListNotificationModel>> GetAllNotificationAsync(int pageNumber, int pageSize, string userId);
        Task<NotificationModelView> GetNotificationByIdAsync(string id);
        Task<bool> SendNotificationToAllUsersAsync(NotificationForUser model);
    }
}
