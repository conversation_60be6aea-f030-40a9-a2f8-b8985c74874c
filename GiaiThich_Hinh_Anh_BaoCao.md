# GIẢI THÍCH CHI TIẾT CÁC HÌNH ẢNH TRONG BÁO CÁO BLOODPLUS

---

## HÌNH 1: SƠ ĐỒ CƠ SỞ DỮ LIỆU (DATABASE SCHEMA - ERD)

### **Mô tả tổng quan:**
Sơ đồ Entity-Relationship Diagram (ERD) thể hiện cấu trúc cơ sở dữ liệu của hệ thống BloodPlus với 12 bảng chính và các mối quan hệ giữa chúng.

### **Các Entity chính và chức năng:**

#### **1. User (Người dùng)**
- **Chức năng**: <PERSON><PERSON><PERSON> trữ thông tin người dùng hệ thống
- **<PERSON><PERSON><PERSON> trường chính**:
  - Id: Kh<PERSON><PERSON> chính
  - UserName, Email: Thông tin đăng nhập
  - FullName, PhoneNumber: Thông tin cá nhân
  - BloodTypeId: Liê<PERSON> kết với bảng BloodType
  - Address, DateOfBirth: Thông tin bổ sung
  - IsActive, IsDeleted: Trạng thái tài khoản

#### **2. Role (<PERSON>ai trò)**
- **Chức năng**: Quản lý phân quyền hệ thống
- **Các vai trò**: Admin, Manager, User
- **Liên kết**: Quan hệ N:N với User thông qua UserRole

#### **3. DonationEvent (Sự kiện hiến máu)**
- **Chức năng**: Quản lý các sự kiện hiến máu
- **Các trường chính**:
  - EventName, Description: Thông tin sự kiện
  - EventDate, StartTime, EndTime: Thời gian
  - Location, Address: Địa điểm
  - RequiredBloodTypes: Nhóm máu cần thiết
  - MaxParticipants: Số lượng tối đa
  - IsEmergency: Đánh dấu sự kiện khẩn cấp

#### **4. Appointment (Lịch hẹn)**
- **Chức năng**: Quản lý lịch hẹn hiến máu của người dùng
- **Liên kết**: 
  - UserId → User (N:1)
  - DonationEventId → DonationEvent (N:1)
- **Trạng thái**: Pending, Confirmed, Completed, Cancelled

#### **5. BloodType (Nhóm máu)**
- **Chức năng**: Định nghĩa các nhóm máu
- **Các nhóm**: A+, A-, B+, B-, AB+, AB-, O+, O-
- **Liên kết**: 1:N với User và DonationEvent

#### **6. Organization (Tổ chức)**
- **Chức năng**: Quản lý các tổ chức y tế
- **Thông tin**: Tên, địa chỉ, liên hệ, loại tổ chức
- **Liên kết**: 1:N với DonationEvent

#### **7. Blog (Bài viết)**
- **Chức năng**: Quản lý tin tức và bài viết
- **Nội dung**: Title, Content, Images, Tags
- **Metadata**: Author, PublishDate, ViewCount

#### **8. Notification (Thông báo)**
- **Chức năng**: Hệ thống thông báo cho người dùng
- **Loại thông báo**: 
  - Sự kiện mới
  - Nhắc nhở lịch hẹn
  - Khẩn cấp cần máu
  - Tin tức mới

#### **9. Voucher (Phần thưởng)**
- **Chức năng**: Quản lý voucher và phần thưởng
- **Thông tin**: Tên, mô tả, giá trị, điều kiện
- **Trạng thái**: Available, Used, Expired

#### **10. ChatMessage (Tin nhắn Chat)**
- **Chức năng**: Lưu trữ lịch sử chat với AI
- **Nội dung**: Message, Response, Timestamp
- **Liên kết**: UserId → User (N:1)

### **Mối quan hệ chính:**
- **User ↔ Appointment**: 1:N (Một user có nhiều lịch hẹn)
- **DonationEvent ↔ Appointment**: 1:N (Một sự kiện có nhiều lịch hẹn)
- **User ↔ Notification**: 1:N (Một user nhận nhiều thông báo)
- **BloodType ↔ User**: 1:N (Một nhóm máu có nhiều user)
- **Organization ↔ DonationEvent**: 1:N (Một tổ chức tổ chức nhiều sự kiện)

### **Cách sử dụng trong báo cáo:**
```markdown
### 5.3 Sơ đồ cơ sở dữ liệu (ERD)

![Database Schema](images/database-schema.png)

*Hình 5.1: Sơ đồ Entity-Relationship của hệ thống BloodPlus*

Cơ sở dữ liệu được thiết kế với 12 bảng chính, đảm bảo tính toàn vẹn dữ liệu và hiệu suất truy vấn cao. Các mối quan hệ được thiết kế theo nguyên tắc chuẩn hóa để tránh redundancy và đảm bảo consistency.
```

---

## HÌNH 2: KIẾN TRÚC HỆ THỐNG (SYSTEM ARCHITECTURE)

### **Mô tả tổng quan:**
Sơ đồ kiến trúc tổng thể thể hiện cách các thành phần của hệ thống BloodPlus tương tác với nhau theo mô hình Clean Architecture.

### **Các Layer chính:**

#### **1. Presentation Layer (API Layer)**
- **Thành phần**: Controllers, Middleware, Authentication
- **Chức năng**: 
  - Xử lý HTTP requests/responses
  - Validation input data
  - Authentication & Authorization
  - Error handling
- **Công nghệ**: ASP.NET Core Controllers, JWT Authentication

#### **2. Business Logic Layer (Service Layer)**
- **Thành phần**: Services, Business Rules, DTOs
- **Chức năng**:
  - Xử lý logic nghiệp vụ
  - Validation business rules
  - Data transformation
  - External service integration
- **Services chính**:
  - AuthService: Xác thực người dùng
  - DonationEventService: Quản lý sự kiện
  - NotificationService: Gửi thông báo
  - ChatGPTService: Tích hợp AI

#### **3. Data Access Layer (Repository Layer)**
- **Thành phần**: Repositories, Unit of Work, Data Context
- **Chức năng**:
  - Truy xuất dữ liệu từ database
  - Implement Repository pattern
  - Transaction management
  - Data mapping
- **Pattern**: Repository + Unit of Work

#### **4. Domain Layer (Core Layer)**
- **Thành phần**: Entities, Enums, Interfaces
- **Chức năng**:
  - Định nghĩa business entities
  - Business rules và constraints
  - Domain interfaces
- **Đặc điểm**: Độc lập, không phụ thuộc vào layer khác

#### **5. Infrastructure Layer**
- **Thành phần**: External Services, File Storage, Email
- **Services**:
  - Firebase: Push notifications, File storage
  - OpenAI: Chatbot AI
  - Google Maps: Geolocation
  - Email Service: Gửi email

### **Data Flow:**
1. **Request**: Mobile App → API Controller
2. **Processing**: Controller → Service → Repository
3. **Database**: Repository → Entity Framework → SQL Server
4. **Response**: Database → Repository → Service → Controller → Mobile App

### **Cách sử dụng trong báo cáo:**
```markdown
### 2.3 Kiến trúc hệ thống tổng thể

![System Architecture](images/system-architecture.png)

*Hình 2.1: Kiến trúc Clean Architecture của BloodPlus Backend*

Hệ thống áp dụng Clean Architecture với 4 layer chính, đảm bảo:
- **Separation of Concerns**: Mỗi layer có trách nhiệm riêng biệt
- **Dependency Inversion**: Layer trong không phụ thuộc vào layer ngoài
- **Testability**: Dễ dàng unit test từng layer
- **Maintainability**: Dễ bảo trì và mở rộng
```

---

## HÌNH 3: LUỒNG HOẠT ĐỘNG NGƯỜI DÙNG (USER FLOW)

### **Mô tả tổng quan:**
Sơ đồ luồng hoạt động mô tả các bước người dùng thực hiện trong ứng dụng BloodPlus từ khi mở app đến khi hoàn thành các tác vụ chính.

### **Các luồng hoạt động chính:**

#### **1. Authentication Flow (Luồng xác thực)**
**Bước 1**: Mở ứng dụng
- Kiểm tra trạng thái đăng nhập
- Nếu chưa đăng nhập → Màn hình Login
- Nếu đã đăng nhập → Màn hình Home

**Bước 2**: Đăng ký/Đăng nhập
- **Đăng ký mới**:
  - Nhập thông tin cá nhân
  - Xác thực OTP qua SMS
  - Tạo tài khoản thành công
- **Đăng nhập**:
  - Email/Password hoặc Google Sign-in
  - Xác thực thành công → Home screen

#### **2. Event Discovery & Registration Flow**
**Bước 1**: Tìm kiếm sự kiện
- Xem danh sách sự kiện gần đó
- Lọc theo: Thời gian, Địa điểm, Nhóm máu
- Xem chi tiết sự kiện

**Bước 2**: Đăng ký tham gia
- Chọn sự kiện muốn tham gia
- Xác nhận thông tin cá nhân
- Chọn thời gian phù hợp
- Xác nhận đăng ký

**Bước 3**: Quản lý lịch hẹn
- Xem lịch hẹn đã đăng ký
- Nhận thông báo nhắc nhở
- Check-in tại sự kiện

#### **3. Emergency Request Flow**
**Bước 1**: Tạo yêu cầu khẩn cấp
- Nhập thông tin bệnh nhân
- Chọn nhóm máu cần thiết
- Xác định vị trí bệnh viện
- Gửi yêu cầu

**Bước 2**: Thông báo khẩn cấp
- Hệ thống tìm người hiến phù hợp
- Gửi push notification theo vị trí
- Người hiến phản hồi

**Bước 3**: Kết nối và hỗ trợ
- Kết nối người hiến với bệnh viện
- Cung cấp thông tin liên lạc
- Theo dõi trạng thái

#### **4. AI Chatbot Flow**
**Bước 1**: Mở chat
- Truy cập chatbot từ menu
- Xem lịch sử chat trước đó

**Bước 2**: Đặt câu hỏi
- Nhập câu hỏi về hiến máu
- AI phân tích context (vị trí, nhóm máu)
- Trả lời tư vấn chuyên môn

**Bước 3**: Nhận hỗ trợ
- Nhận câu trả lời chi tiết
- Gợi ý địa điểm hiến máu gần nhất
- Lưu lịch sử chat

#### **5. Reward System Flow**
**Bước 1**: Tích điểm
- Hoàn thành hiến máu → Nhận điểm
- Xem bảng xếp hạng
- Theo dõi điểm tích lũy

**Bước 2**: Đổi voucher
- Xem danh sách voucher có sẵn
- Chọn voucher muốn đổi
- Xác nhận đổi điểm

**Bước 3**: Sử dụng voucher
- Xem voucher đã có
- Sử dụng tại đối tác
- Theo dõi trạng thái voucher

### **Decision Points (Điểm quyết định):**
- **Đã đăng nhập?** → Yes: Home / No: Login
- **Có sự kiện phù hợp?** → Yes: Đăng ký / No: Tạo alert
- **Khẩn cấp?** → Yes: Emergency flow / No: Normal flow
- **Đủ điểm đổi voucher?** → Yes: Đổi / No: Tiếp tục tích điểm

### **Cách sử dụng trong báo cáo:**
```markdown
### 4.8 Luồng hoạt động người dùng

![User Flow](images/user-flow.png)

*Hình 4.1: Sơ đồ luồng hoạt động chính của người dùng*

Sơ đồ mô tả 5 luồng hoạt động chính:
1. **Authentication Flow**: Đăng ký/đăng nhập với xác thực OTP
2. **Event Flow**: Tìm kiếm và đăng ký sự kiện hiến máu
3. **Emergency Flow**: Xử lý yêu cầu máu khẩn cấp
4. **Chatbot Flow**: Tương tác với AI tư vấn
5. **Reward Flow**: Tích điểm và đổi voucher

Mỗi luồng được thiết kế tối ưu để người dùng có thể hoàn thành tác vụ với ít bước nhất.
```

---

## HƯỚNG DẪN TÍCH HỢP VÀO BÁO CÁO

### **1. Vị trí đặt hình:**
- **Hình 1 (Database Schema)**: Phần 5 - Cơ sở dữ liệu
- **Hình 2 (System Architecture)**: Phần 2 - Kiến trúc hệ thống
- **Hình 3 (User Flow)**: Phần 4 - Tính năng chính

### **2. Format chuẩn:**
```markdown
![Tên hình](đường_dẫn_hình)

*Hình X.Y: Mô tả ngắn gọn*

Giải thích chi tiết...
```

### **3. Yêu cầu kỹ thuật:**
- **Độ phân giải**: Tối thiểu 300 DPI
- **Format**: PNG hoặc JPG
- **Kích thước**: Phù hợp với khổ giấy A4
- **Font chữ**: Arial hoặc Times New Roman, size 10-12

### **4. Lưu ý thiết kế:**
- Sử dụng màu sắc nhất quán
- Đảm bảo rõ ràng khi in đen trắng
- Căn chỉnh và bố cục hợp lý
- Chú thích đầy đủ cho các ký hiệu

---

## CÁC SƠ ĐỒ BỔ SUNG CÓ THỂ TẠO

### **1. Mobile App Architecture Diagram**
**Mô tả**: Kiến trúc ứng dụng Flutter với pattern MVVM
**Nội dung**:
- **Presentation Layer**: Widgets, Screens, UI Components
- **Business Logic Layer**: Providers, ViewModels, State Management
- **Data Layer**: Repositories, Services, Local Storage
- **Platform Layer**: Native Android/iOS integrations

### **2. API Sequence Diagram**
**Mô tả**: Luồng xử lý API từ Mobile đến Database
**Nội dung**:
- Request flow: Mobile → Controller → Service → Repository → Database
- Response flow: Database → Repository → Service → Controller → Mobile
- Error handling và exception management

### **3. Security Architecture Diagram**
**Mô tả**: Các lớp bảo mật trong hệ thống
**Nội dung**:
- Authentication layer (JWT, OAuth)
- Authorization layer (Role-based access)
- Data encryption layer
- Network security (HTTPS, API Gateway)

### **4. Deployment Architecture**
**Mô tả**: Kiến trúc triển khai hệ thống
**Nội dung**:
- Development environment
- Staging environment
- Production environment
- CI/CD pipeline

### **5. Integration Architecture**
**Mô tả**: Tích hợp với các dịch vụ bên ngoài
**Nội dung**:
- Firebase integration (Auth, Storage, FCM)
- OpenAI API integration
- Google Maps API
- Payment gateway integration

---

## TEMPLATE MẪU CHO TỪNG LOẠI HÌNH

### **Template 1: Database Schema**
```markdown
### X.X Sơ đồ cơ sở dữ liệu

![Database Schema](images/database-schema.png)

*Hình X.X: Entity Relationship Diagram của hệ thống BloodPlus*

**Mô tả**: Cơ sở dữ liệu gồm [số] bảng chính với các mối quan hệ được thiết kế theo nguyên tắc chuẩn hóa:

**Các bảng chính**:
- **User**: Quản lý thông tin người dùng và xác thực
- **DonationEvent**: Lưu trữ thông tin sự kiện hiến máu
- **Appointment**: Quản lý lịch hẹn giữa user và event
- **[Liệt kê các bảng khác]**

**Mối quan hệ chính**:
- User (1) ↔ (N) Appointment: Một user có thể có nhiều lịch hẹn
- DonationEvent (1) ↔ (N) Appointment: Một sự kiện có nhiều người đăng ký
- [Mô tả các mối quan hệ khác]

**Ưu điểm thiết kế**:
- Đảm bảo tính toàn vẹn dữ liệu (Data Integrity)
- Tối ưu hiệu suất truy vấn
- Dễ dàng mở rộng và bảo trì
```

### **Template 2: System Architecture**
```markdown
### X.X Kiến trúc hệ thống

![System Architecture](images/system-architecture.png)

*Hình X.X: Kiến trúc tổng thể hệ thống BloodPlus*

**Mô tả**: Hệ thống được thiết kế theo mô hình Clean Architecture với [số] layer chính:

**1. Presentation Layer**:
- Chức năng: Xử lý HTTP requests, authentication, validation
- Công nghệ: ASP.NET Core Controllers, JWT Authentication
- Components: [Liệt kê components]

**2. Business Logic Layer**:
- Chức năng: Xử lý logic nghiệp vụ, business rules
- Công nghệ: C# Services, AutoMapper
- Services: [Liệt kê services chính]

**3. Data Access Layer**:
- Chức năng: Truy xuất dữ liệu, transaction management
- Công nghệ: Entity Framework Core, Repository Pattern
- Patterns: Repository + Unit of Work

**4. Infrastructure Layer**:
- Chức năng: Tích hợp external services
- Services: Firebase, OpenAI, Google Maps, Email

**Ưu điểm**:
- Separation of Concerns
- High Testability
- Easy Maintenance
- Scalable Architecture
```

### **Template 3: User Flow**
```markdown
### X.X Luồng hoạt động người dùng

![User Flow](images/user-flow.png)

*Hình X.X: Sơ đồ luồng hoạt động chính của người dùng*

**Mô tả**: Sơ đồ thể hiện [số] luồng hoạt động chính của người dùng trong ứng dụng:

**1. Authentication Flow**:
- Bước 1: Kiểm tra trạng thái đăng nhập
- Bước 2: Đăng ký/đăng nhập (Email/Google)
- Bước 3: Xác thực OTP (nếu cần)
- Kết quả: Truy cập vào ứng dụng

**2. [Tên luồng khác]**:
- [Mô tả các bước]

**Decision Points**:
- [Liệt kê các điểm quyết định quan trọng]

**Tối ưu UX**:
- Giảm thiểu số bước thực hiện
- Cung cấp feedback rõ ràng
- Xử lý lỗi thân thiện
```

---

## CHECKLIST HOÀN THIỆN HÌNH ẢNH

### **Trước khi thêm vào báo cáo:**
- [ ] Hình ảnh có độ phân giải đủ cao (≥300 DPI)
- [ ] Text trong hình rõ ràng, dễ đọc
- [ ] Màu sắc phù hợp khi in đen trắng
- [ ] Kích thước phù hợp với khổ giấy A4
- [ ] Có caption và số thứ tự hình
- [ ] Giải thích chi tiết trong nội dung
- [ ] Tham chiếu đúng trong text
- [ ] Bố cục hợp lý, không bị méo
- [ ] Chú thích đầy đủ cho ký hiệu
- [ ] Kiểm tra chính tả trong hình
