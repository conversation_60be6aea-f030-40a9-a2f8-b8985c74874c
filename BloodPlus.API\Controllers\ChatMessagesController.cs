﻿using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.ChatMessageModelView;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] // Bắt buộc đăng nhập
    public class ChatController : ControllerBase
    {
        private readonly IChatMessageService _chatService;

        public ChatController(IChatMessageService chatService)
        {
            _chatService = chatService;
        }

        // Helper lấy UserId từ JWT
        private Guid GetUserId()
        {
            var claim = User.FindFirst(ClaimTypes.NameIdentifier) ?? User.FindFirst("userId");
            if (claim == null)
                throw new UnauthorizedAccessException("Missing userId in token");

            return Guid.Parse(claim.Value);
        }

        // Gửi message và nhận phản hồi AI
        [HttpPost("send")]
        public async Task<ActionResult<ChatMessageModelView>> SendMessage([FromBody] CreateChatMessageModelViewController input)
        {
            var userId = GetUserId();

            var messageInput = new CreateChatMessageModelView
            {
                UserId = userId,
                Role = RoleChat.user.ToString(),
                Message = input.Message,
                ConversationId = input.ConversationId
            };

            var result = await _chatService.AddMessageAsync(messageInput);
            return Ok(result);
        }

        // Lấy danh sách tin nhắn trong hội thoại
        [HttpGet("{conversationId}")]
        public async Task<ActionResult<List<ChatMessageModelView>>> GetMessages(string conversationId, [FromQuery] int limit = 50)
        {
            var userId = GetUserId();
            var messages = await _chatService.GetMessagesByConversationAsync(conversationId, userId, limit);
            return Ok(messages);
        }

        // Danh sách hội thoại của người dùng
        [HttpGet("conversations")]
        public async Task<ActionResult<List<string>>> GetConversations()
        {
            var userId = GetUserId();
            var result = await _chatService.GetUserConversationFirstMessagesAsync(userId);
            return Ok(result);
        }


        // Xoá toàn bộ hội thoại
        [HttpDelete("conversation/{conversationId}")]
        public async Task<IActionResult> DeleteConversation(string conversationId)
        {
            var userId = GetUserId();
            var success = await _chatService.DeleteConversationAsync(conversationId, userId);
            return success ? Ok() : NotFound("Conversation not found");
        }

    }
}
