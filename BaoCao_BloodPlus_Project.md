# BÁO CÁO DỰ ÁN BLOODPLUS
## Ứng dụng hỗ trợ cộng đồng hiến máu

---

**Sinh viên thực hiện:** [Tên sinh viên]  
**M<PERSON> số sinh viên:** [MSSV]  
**Lớp:** [Tên lớp]  
**Mô<PERSON> học:** PRM392 - Mobile Programming  
**Giảng viên hướng dẫn:** [Tên giảng viên]  
**Ngày nộp:** 19/07/2025  

---

## 1. TỔNG QUAN DỰ ÁN

### 1.1 Giới thiệu
BloodPlus là một hệ thống ứng dụng di động toàn diện được phát triển để hỗ trợ cộng đồng hiến máu. Dự án bao gồm:
- **Backend API**: Được xây dựng bằng ASP.NET Core với kiến trúc Clean Architecture
- **Mobile App**: Ứng dụng Flutter hỗ trợ đa nền tảng (Android/iOS)

### 1.2 Mục tiêu dự án
- Tạo cầu nối giữa người hiến máu và các tổ chức y tế
- Quản lý hiệu quả các sự kiện hiến máu
- Cung cấp thông tin và tư vấn về hiến máu
- Xây dựng cộng đồng hiến máu tích cực

### 1.3 Đối tượng sử dụng
- **Người dân**: Có nhu cầu hiến máu hoặc tìm kiếm máu
- **Tổ chức y tế**: Bệnh viện, trung tâm y tế
- **Quản trị viên**: Quản lý hệ thống và sự kiện

---

## 2. KIẾN TRÚC HỆ THỐNG

### 2.1 Kiến trúc tổng thể
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │    Database     │
│   (Flutter)     │◄──►│  (ASP.NET Core) │◄──►│  (SQL Server)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│  External APIs  │◄─────────────┘
                        │ Firebase, OpenAI│
                        └─────────────────┘
```

### 2.2 Backend Architecture (Clean Architecture)
```
BloodPlus.API/                 # Presentation Layer
├── Controllers/               # API Controllers
├── Middleware/               # Custom Middleware
└── Program.cs                # Application Entry Point

BloodPlus.Services/           # Business Logic Layer
├── Services/                 # Business Services
├── Interfaces/              # Service Contracts
└── Hubs/                    # SignalR Hubs

BloodPlus.Repositories/       # Data Access Layer
├── Context/                 # Database Context
├── UOW/                     # Unit of Work Pattern
└── Migrations/              # Database Migrations

BloodPlus.Core/              # Domain Layer
├── Base/                    # Base Entities
├── Enum/                    # Enumerations
└── Utils/                   # Utilities

BloodPlus.ModelViews/        # DTOs & ViewModels
BloodPlus.Contract.Repositories/ # Repository Contracts
```

### 2.3 Mobile App Architecture
```
lib/
├── core/                    # Core configurations
│   ├── config/             # API & App configs
│   ├── constants/          # Constants & themes
│   └── routes/             # Navigation routes
├── data/                   # Data layer
│   ├── models/             # Data models
│   ├── services/           # API services
│   ├── repositories/       # Data repositories
│   └── manager/            # State management
└── presentation/           # UI layer
    └── features/           # Feature-based UI
        ├── auth/           # Authentication
        ├── home/           # Home screen
        ├── schedule/       # Event scheduling
        ├── blog/           # News & blogs
        ├── user/           # User profile
        └── voucher/        # Rewards system
```

---

## 3. CÔNG NGHỆ SỬ DỤNG

### 3.1 Backend Technologies
- **Framework**: ASP.NET Core 8.0
- **Database**: SQL Server với Entity Framework Core
- **Authentication**: JWT Token + Identity Framework
- **Architecture Pattern**: Clean Architecture, Repository Pattern, Unit of Work
- **Real-time Communication**: SignalR
- **External Integrations**:
  - Firebase (Push notifications, File storage)
  - OpenAI GPT-4 (Chatbot tư vấn)
  - Google Gemini AI
  - Geolocation Services

### 3.2 Mobile Technologies
- **Framework**: Flutter (Dart)
- **State Management**: Provider Pattern
- **Local Storage**: SharedPreferences
- **Maps**: Google Maps Flutter
- **Authentication**: Google Sign-In
- **Notifications**: OneSignal
- **Biometric**: Local Authentication
- **Internationalization**: Flutter Localizations (7 ngôn ngữ)

### 3.3 DevOps & Deployment
- **Version Control**: Git
- **Database**: SQL Server
- **Cloud Services**: Firebase
- **API Documentation**: Swagger/OpenAPI

---

## 4. TÍNH NĂNG CHÍNH

### 4.1 Hệ thống Authentication & Authorization
- Đăng ký/Đăng nhập bằng email/password
- Đăng nhập bằng Google
- Xác thực OTP qua số điện thoại
- Quên mật khẩu và đặt lại
- Phân quyền: Admin, Manager, User
- Bảo mật sinh trắc học (vân tay, Face ID)

### 4.2 Quản lý Sự kiện Hiến máu
- **Sự kiện thường**: Lên lịch trước, đăng ký tham gia
- **Sự kiện khẩn cấp**: Tạo nhanh, thông báo theo vị trí
- Tìm kiếm sự kiện theo vị trí, thời gian, nhóm máu
- Lịch sử tham gia và theo dõi
- Quản lý appointment và check-in

### 4.3 Hệ thống Thông báo Thông minh
- Push notification qua Firebase
- Thông báo theo vị trí địa lý (Geofencing)
- Thông báo khẩn cấp cho nhóm máu phù hợp
- Nhắc nhở lịch hẹn hiến máu
- Thông báo tin tức và blog mới

### 4.4 Chatbot AI Tư vấn
- Tích hợp OpenAI GPT-4
- Tư vấn về sức khỏe và hiến máu
- Hỗ trợ đa ngôn ngữ
- Cung cấp thông tin địa điểm hiến máu gần nhất
- Lưu trữ lịch sử chat

### 4.5 Hệ thống Blog & Tin tức
- Quản lý bài viết về hiến máu
- Phân loại theo chủ đề
- Tìm kiếm và lọc nội dung
- Chia sẻ bài viết
- Đánh giá và bình luận

### 4.6 Hệ thống Voucher & Phần thưởng
- Tích điểm theo số lần hiến máu
- Đổi voucher và quà tặng
- Bảng xếp hạng người hiến máu
- Chương trình khuyến khích
- Quản lý voucher đã sử dụng

### 4.7 Quản lý Hồ sơ Người dùng
- Thông tin cá nhân chi tiết
- Lịch sử hiến máu
- Nhóm máu và thông tin y tế
- Cài đặt thông báo
- Quản lý quyền riêng tư

---

## 5. CƠ SỞ DỮ LIỆU

### 5.1 Các Entity chính
- **User**: Thông tin người dùng, authentication
- **DonationEvent**: Sự kiện hiến máu
- **Appointment**: Lịch hẹn hiến máu
- **BloodType**: Nhóm máu (A, B, AB, O với Rh+/-)
- **Notification**: Hệ thống thông báo
- **Blog**: Bài viết và tin tức
- **Voucher**: Phần thưởng và ưu đãi
- **ChatMessage**: Lịch sử chat với AI
- **Organization**: Tổ chức y tế

### 5.2 Relationships
- User 1:N Appointment
- User 1:N Notification
- DonationEvent 1:N Appointment
- BloodType 1:N User
- User 1:N ChatMessage
- Organization 1:N DonationEvent

---

## 6. API ENDPOINTS

### 6.1 Authentication APIs
```
POST /api/auth/auth-account          # Đăng nhập
POST /api/auth/create-account        # Đăng ký
POST /api/auth/login-google          # Đăng nhập Google
POST /api/auth/verify-phonenumber    # Xác thực OTP
```

### 6.2 User Management APIs
```
GET  /api/user/profile               # Lấy thông tin profile
PUT  /api/user/update-profile        # Cập nhật profile
GET  /api/user/donation-history      # Lịch sử hiến máu
```

### 6.3 Event Management APIs
```
GET  /api/donationevent              # Danh sách sự kiện
POST /api/donationevent              # Tạo sự kiện mới
GET  /api/donationevent/{id}         # Chi tiết sự kiện
PUT  /api/donationevent/{id}         # Cập nhật sự kiện
```

### 6.4 Emergency APIs
```
POST /api/emergencydonation          # Tạo sự kiện khẩn cấp
GET  /api/emergencydonation/nearby   # Sự kiện khẩn cấp gần đó
```

### 6.5 Other APIs
```
GET  /api/blog                       # Danh sách blog
GET  /api/voucher                    # Danh sách voucher
POST /api/chatgpt/completion         # Chat với AI
GET  /api/notification               # Thông báo
GET  /api/bloodtype                  # Danh sách nhóm máu
```

---

## 7. TÍNH NĂNG NỔI BẬT

### 7.1 Geolocation Intelligence
- Tự động phát hiện vị trí người dùng
- Tìm sự kiện hiến máu gần nhất
- Thông báo khẩn cấp theo bán kính
- Tính toán khoảng cách và thời gian di chuyển

### 7.2 AI-Powered Chatbot
- Sử dụng GPT-4 cho tư vấn chuyên sâu
- Hiểu context về vị trí, nhóm máu người dùng
- Đa ngôn ngữ (7 ngôn ngữ được hỗ trợ)
- Học hỏi từ lịch sử chat

### 7.3 Real-time Notifications
- SignalR cho thông báo real-time
- Firebase Cloud Messaging
- Thông báo theo nhóm máu
- Thông báo khẩn cấp ưu tiên cao

### 7.4 Gamification
- Hệ thống điểm thưởng
- Bảng xếp hạng
- Badges và achievements
- Voucher và quà tặng

---

## 8. BẢO MẬT VÀ QUYỀN RIÊNG TƯ

### 8.1 Authentication & Authorization
- JWT Token với refresh token
- Role-based access control
- Mã hóa mật khẩu với BCrypt
- Session management

### 8.2 Data Protection
- HTTPS cho tất cả API calls
- Mã hóa dữ liệu nhạy cảm
- Validation và sanitization
- SQL Injection prevention

### 8.3 Privacy Compliance
- Tuân thủ quy định bảo vệ dữ liệu
- Cho phép người dùng xóa dữ liệu
- Minh bạch về việc sử dụng dữ liệu
- Đồng ý của người dùng

---

## 9. TESTING VÀ QUALITY ASSURANCE

### 9.1 Backend Testing
- Unit Tests cho Business Logic
- Integration Tests cho APIs
- Database Testing với In-Memory DB
- Performance Testing

### 9.2 Mobile Testing
- Widget Testing
- Integration Testing
- Platform-specific Testing (Android/iOS)
- User Acceptance Testing

### 9.3 Quality Metrics
- Code Coverage > 80%
- API Response Time < 200ms
- Mobile App Size < 50MB
- Crash Rate < 0.1%

---

## 10. DEPLOYMENT VÀ DEVOPS

### 10.1 Backend Deployment
- Docker containerization
- CI/CD pipeline
- Database migration scripts
- Environment configuration

### 10.2 Mobile Deployment
- Google Play Store (Android)
- Apple App Store (iOS)
- Code signing và certificates
- Beta testing với TestFlight/Play Console

---

## 11. KẾT LUẬN VÀ HƯỚNG PHÁT TRIỂN

### 11.1 Kết quả đạt được
- Xây dựng thành công hệ thống BloodPlus hoàn chỉnh
- Tích hợp thành công các công nghệ hiện đại
- Đáp ứng đầy đủ yêu cầu chức năng
- Giao diện thân thiện và dễ sử dụng

### 11.2 Hướng phát triển tương lai
- **Machine Learning**: Dự đoán nhu cầu máu
- **IoT Integration**: Kết nối với thiết bị y tế
- **Blockchain**: Truy xuất nguồn gốc máu
- **AR/VR**: Giáo dục về hiến máu
- **Wearable Integration**: Theo dõi sức khỏe

### 11.3 Bài học kinh nghiệm
- Tầm quan trọng của việc thiết kế kiến trúc tốt
- Sự cần thiết của testing và quality assurance
- Giá trị của việc tích hợp AI và công nghệ mới
- Tầm quan trọng của UX/UI trong ứng dụng y tế

---

## 12. TÀI LIỆU THAM KHẢO

1. Microsoft Documentation - ASP.NET Core
2. Flutter Documentation - flutter.dev
3. Firebase Documentation
4. OpenAI API Documentation
5. Clean Architecture - Robert C. Martin
6. Entity Framework Core Documentation
7. SignalR Documentation

---

**Chữ ký sinh viên**

[Tên sinh viên]  
[Ngày tháng năm]
