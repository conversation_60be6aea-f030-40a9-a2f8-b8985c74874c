﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.ModelViews.AuthModelViews;
using BloodPlus.ModelViews.UserModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ITokenService _tokenService;
        
        public AuthController(IAuthService authService, ITokenService tokenService)
        {
            _authService = authService;
            _tokenService = tokenService;
        }

        [HttpPost("auth-account")]
        public async Task<IActionResult> Login(LoginModelView model)
        {
            User account = await _authService.AuthenticateAsync(model);
            if (account == null)
            {
                return Unauthorized("Invalid credentials");
            }

            var token = await _tokenService.GenerateJwtTokenAsync(account.Id.ToString());
            return Ok(token);
        }

        [HttpPost("login-google")]
        public async Task<IActionResult> LoginGoogle(string idToken, string deviceToken)
        {
            User account = await _authService.ValidateGoogleTokenAsync(idToken, deviceToken);
            if (account == null)
            {
                return Unauthorized("Invalid credentials");
            }

            var token = await _tokenService.GenerateJwtTokenAsync(account.Id.ToString());
            return Ok(token);
        }

        [HttpPost("register-account")]
        public async Task<IActionResult> CreateAccount(CreateAccountModelView model)
        {
            var checker = await _authService.CreateAccountOtpAsync(model);

            return Ok(checker);
        }

        [HttpPost("verify-otp")]
        public async Task<IActionResult> VerifyOtp(VerifyOtpModel model)
        {
            var checker = await _authService.VerifyOtpAsync(model);

            return Ok(checker);
        }

        [HttpPost("forget-password")]
        public async Task<ActionResult<string>> ForgetPassword(ForgetPasswordModel model)
        {
            var result = await _authService.ForgetPasswordAsync(model);

            return Ok(result);
        }

        [HttpPatch("verify-otp-password")]
        public async Task<ActionResult<string>> VerifyOtpForgetPassword(VerifyOtpModel model)
        {
            var result = await _authService.VerifyOtpForgetPasswordAsync(model);

            return Ok(result);
        }

        [HttpPatch("reset-password")]
        public async Task<ActionResult<string>> ResetPassword(ResetPasswordModel model)
        {
            var result = await _authService.ResetPasswordAsync(model);

            return Ok(result);
        }
    }
}
