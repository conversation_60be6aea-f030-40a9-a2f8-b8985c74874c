import 'package:flutter/material.dart';
import 'package:bloodplusmobile/data/models/emergency_donation_model.dart';
import 'package:bloodplusmobile/data/models/emergency_appointment_model.dart';
import 'package:bloodplusmobile/data/services/appointment_service.dart';
import 'package:bloodplusmobile/data/services/blood_type_service.dart';
import 'package:bloodplusmobile/data/models/blood_type_model.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/constants/app_colors.dart';

class EmergencyAppointmentFormScreen extends StatefulWidget {
  final EmergencyDonationEventModel event;
  const EmergencyAppointmentFormScreen({Key? key, required this.event}) : super(key: key);

  @override
  State<EmergencyAppointmentFormScreen> createState() => _EmergencyAppointmentFormScreenState();
}

class _EmergencyAppointmentFormScreenState extends State<EmergencyAppointmentFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  List<BloodTypeModel> _bloodTypes = [];
  bool _isBloodTypeLoading = true;
  String? _selectedBloodTypeId;
  int? _selectedBloodComponent;
  bool _isLoading = false;

  final List<Map<String, String>> _bloodComponents = [
    {'value': '0', 'vi': 'Hồng cầu', 'en': 'Red Blood Cells'},
    {'value': '1', 'vi': 'Huyết tương', 'en': 'Plasma'},
    {'value': '2', 'vi': 'Tiểu cầu', 'en': 'Platelets'},
    {'value': '3', 'vi': 'Bạch cầu', 'en': 'White Blood Cells'},
    {'value': '4', 'vi': 'Máu toàn phần', 'en': 'Whole Blood'},
  ];
  String? _selectedBloodComponentValue;

  @override
  void initState() {
    super.initState();
    // Lấy user từ Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = Provider.of<AppStateNotifier>(context, listen: false).user;
      if (user != null) {
        _nameController.text = user.name ?? '';
        _phoneController.text = user.phoneNumber ?? '';
        _emailController.text = user.email ?? '';
        _locationController.text = user.address ?? '';
      }
    });
    _fetchBloodTypes();
  }

  Future<void> _fetchBloodTypes() async {
    try {
      final types = await BloodTypeService().fetchBloodTypes();
      setState(() {
        _bloodTypes = types;
        _isBloodTypeLoading = false;
      });
    } catch (e) {
      setState(() { _isBloodTypeLoading = false; });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Lỗi lấy nhóm máu: $e')));
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate() || _selectedBloodTypeId == null || _selectedBloodComponentValue == null) return;
    setState(() { _isLoading = true; });
    final request = EmergencyAppointmentRequest(
      donationEventId: widget.event.id,
      userName: _nameController.text.trim(),
      phoneNumber: _phoneController.text.trim(),
      location: _locationController.text.trim(),
      email: _emailController.text.trim(),
      bloodTypeId: _selectedBloodTypeId!,
      bloodComponent: int.parse(_selectedBloodComponentValue!),
    );
    try {
      final result = await AppointmentService().createEmergencyAppointment(request);
      if (result && mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      String errorMsg = e.toString();
      if (errorMsg.contains('500') && (errorMsg.contains('already has') || errorMsg.contains('đã có lịch'))) {
        DialogHelper.showAnimatedErrorDialog(
          context: context,
          title: 'Đã có lịch',
          message: 'Bạn đã có lịch hiến máu rồi! Không thể đăng kí tiếp',
          buttonText: 'OK',
          onPressed: () {
            Navigator.of(context).pop(); // Đóng dialog
            Navigator.of(context).pop(); // Quay lại trang trước
          },
        );
      } else if (errorMsg.contains('500')) {
        DialogHelper.showAnimatedErrorDialog(
          context: context,
          title: 'Lỗi hệ thống',
          message: 'Có lỗi xảy ra, vui lòng thử lại sau.',
          buttonText: 'OK',
        );
      } else {
        DialogHelper.showAnimatedErrorDialog(
          context: context,
          title: 'Lỗi',
          message: errorMsg,
          buttonText: 'OK',
        );
      }
    } finally {
      setState(() { _isLoading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text('Đăng ký hiến máu khẩn cấp', style: AppTheme.headingMedium.copyWith(color: Colors.white)),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Stack(
        children: [
          Center(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 12 : 24),
                child: Card(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
                  elevation: 8,
                  color: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 32, vertical: isSmallScreen ? 20 : 36),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryRed.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                padding: const EdgeInsets.all(10),
                                child: Icon(Icons.bloodtype, color: AppTheme.primaryRed, size: isSmallScreen ? 28 : 36),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Đăng ký hiến máu khẩn cấp', style: AppTheme.headingMedium.copyWith(color: AppTheme.primaryRed, fontSize: isSmallScreen ? 18 : 22)),
                                    const SizedBox(height: 4),
                                    Text('Vui lòng điền thông tin để đăng ký tham gia sự kiện:', style: AppTheme.bodyLarge.copyWith(color: AppTheme.textSecondary)),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryRed.withOpacity(0.07),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.event, color: AppTheme.primaryRed, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(widget.event.title, style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.bold, color: AppTheme.primaryRed)),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          _buildStyledTextField(
                            controller: _nameController,
                            label: 'Họ tên',
                            icon: Icons.person,
                            validator: (v) => v == null || v.isEmpty ? 'Vui lòng nhập họ tên' : null,
                          ),
                          const SizedBox(height: 14),
                          _buildStyledTextField(
                            controller: _phoneController,
                            label: 'Số điện thoại',
                            icon: Icons.phone,
                            keyboardType: TextInputType.phone,
                            validator: (v) => v == null || v.isEmpty ? 'Vui lòng nhập số điện thoại' : null,
                          ),
                          const SizedBox(height: 14),
                          _buildStyledTextField(
                            controller: _emailController,
                            label: 'Email',
                            icon: Icons.email,
                            keyboardType: TextInputType.emailAddress,
                            validator: (v) => v == null || v.isEmpty ? 'Vui lòng nhập email' : null,
                          ),
                          const SizedBox(height: 14),
                          _buildStyledTextField(
                            controller: _locationController,
                            label: 'Địa chỉ hiện tại',
                            icon: Icons.location_on,
                            validator: (v) => v == null || v.isEmpty ? 'Vui lòng nhập địa chỉ' : null,
                          ),
                          const SizedBox(height: 14),
                          _isBloodTypeLoading
                            ? const Center(child: CircularProgressIndicator())
                            : _buildStyledDropdown<String>(
                                value: _selectedBloodTypeId,
                                items: _bloodTypes.map((b) => DropdownMenuItem<String>(
                                  value: b.bloodTypeId,
                                  child: Text(b.bloodTypeName),
                                )).toList(),
                                onChanged: (v) => setState(() => _selectedBloodTypeId = v),
                                label: 'Nhóm máu',
                                icon: Icons.bloodtype,
                                validator: (v) => v == null || v.isEmpty ? 'Vui lòng chọn nhóm máu' : null,
                              ),
                          const SizedBox(height: 14),
                          _buildStyledDropdown<String>(
                            value: _selectedBloodComponentValue,
                            items: _bloodComponents.map((b) => DropdownMenuItem<String>(
                              value: b['value'],
                              child: Text(localizations.locale.languageCode == 'vi' ? b['vi']! : b['en']!),
                            )).toList(),
                            onChanged: (v) => setState(() => _selectedBloodComponentValue = v),
                            label: 'Thành phần máu',
                            icon: Icons.opacity,
                            validator: (v) => v == null || v.isEmpty ? 'Vui lòng chọn thành phần máu' : null,
                          ),
                          const SizedBox(height: 28),
                          SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton.icon(
                              onPressed: _isLoading ? null : _submit,
                              icon: const Icon(Icons.send, color: Colors.white),
                              label: const Text('Đăng ký', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white)),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.primaryRed,
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                                textStyle: TextStyle(fontSize: isSmallScreen ? 16 : 18),
                                elevation: 2,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.2),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: AppTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppTheme.primaryRed),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(14)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide: BorderSide(color: AppTheme.primaryRed.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide: BorderSide(color: AppTheme.primaryRed, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        fillColor: Colors.white,
        filled: true,
      ),
    );
  }

  Widget _buildStyledDropdown<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
    required String label,
    required IconData icon,
    String? Function(T?)? validator,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      validator: validator,
      style: AppTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppTheme.primaryRed),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(14)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide: BorderSide(color: AppTheme.primaryRed.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide: BorderSide(color: AppTheme.primaryRed, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        fillColor: Colors.white,
        filled: true,
      ),
      icon: Icon(Icons.arrow_drop_down, color: AppTheme.primaryRed),
      dropdownColor: Colors.white,
    );
  }
} 