import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/blood_type_model.dart';

class BloodTypeService {
  final UserManager _userManager = UserManager();

  Future<List<BloodTypeModel>> fetchBloodTypes() async {
    final token = await _userManager.getUserToken() ?? '';

    final response = await ApiConfig.authenticatedGet(
      ApiConfig.bloodType,
      token: token,
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((e) => BloodTypeModel.fromJson(e)).toList();
    } else {
      throw Exception('L<PERSON>y danh sách nhóm máu thất bại: ${response.statusCode}');
    }
  }
}
