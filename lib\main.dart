import 'dart:io';
import 'package:bloodplusmobile/core/language_helper/language_manager.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/route_generator.dart';
import 'package:bloodplusmobile/core/widgets/global_floating_button.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/notification_manager.dart';
import 'package:bloodplusmobile/data/services/auth_service.dart';
import 'package:bloodplusmobile/data/services/notification_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'core/language_helper/localization.dart';
import 'core/helpers/idle_timer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bloodplusmobile/presentation/features/schedule/donation_history_screen.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  HttpOverrides.global = MyHttpOverrides();

  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  OneSignal.initialize("************************************");
  OneSignal.Notifications.requestPermission(false);

  await NotificationService().initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => LanguageManager()..loadLanguage(),
        ),
        ChangeNotifierProvider(create: (_) => AppStateNotifier()),
        ChangeNotifierProvider(create: (_) => NotificationManager()),
        Provider(create: (_) => AuthService()),
      ],
      child: const BloodDonationApp(),
    ),
  );
}

class BloodDonationApp extends StatelessWidget {
  const BloodDonationApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageManager, AppStateNotifier>(
      builder: (context, languageManager, appState, child) {
        return Listener(
          behavior: HitTestBehavior.translucent,
          onPointerDown: (_) => IdleTimer().userActivityDetected(),
          onPointerMove: (_) => IdleTimer().userActivityDetected(),
          onPointerUp: (_) => IdleTimer().userActivityDetected(),
          child: MaterialApp(
            title: 'Mau Cong',
            theme: ThemeData(primarySwatch: Colors.red),
            initialRoute: '/splash',
            navigatorKey: NavigationService.navigatorKey,
            onGenerateRoute: RouteGenerator.generateRoute,
            debugShowCheckedModeBanner: false,
            locale: languageManager.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            navigatorObservers: [routeObserver],
            builder: (context, child) {
              final routeName = ModalRoute.of(context)?.settings.name ?? '';
              final isSplashScreen =
                  routeName == AppRoutes.splash || routeName == '';
              final shouldShowChat =
                  appState.isLoggedIn &&
                      routeName != AppRoutes.splash &&
                      routeName != AppRoutes.started &&
                      routeName != AppRoutes.chatAI;
              // Khởi động IdleTimer khi đăng nhập
              if (appState.isLoggedIn) {
                IdleTimer().start(onTimeout: () async {
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.setBool('session_expired', true);
                  appState.resetState();
                  appState.setLoggedIn(false);
                  NavigationService.navigateToAndRemoveUntil(AppRoutes.login);
                });
              } else {
                IdleTimer().stop();
              }
              return Stack(
                children: [
                  child ?? const SizedBox(),
                  if (shouldShowChat) const DraggableFloatingButton(),
                ],
              );
            },
          ),
        );
      },
    );
  }
}
