﻿using BloodPlus.Core.Enum;

namespace BloodPlus.ModelViews.UserModelViews
{
    public class UserModelView
    {
        public string Id { get; set; }
        public string? UserImage { get; set; }
        public string? Name { get; set; }
        public Gender? Gender { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? BloodType { get; set; }
        public string? Job { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public ushort? DonationCount { get; set; }
        public string? Address { get; set; }
        public string? PassportNumber { get; set; }
        public int Point { get; set; }
    }
}
