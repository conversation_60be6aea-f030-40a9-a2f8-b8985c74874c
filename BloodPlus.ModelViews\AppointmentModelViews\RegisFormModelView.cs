﻿namespace BloodPlus.ModelViews.AppointmentModelViews
{
    public class CreateFormModelView
    {
        public bool HasDonatedBefore { get; set; }
        public bool HasDiseases { get; set; }
        public string? DiseaseDetails { get; set; }
        public bool IsTakingMedicine { get; set; }
        public string? MedicineDetails { get; set; }
        public string? Symptoms { get; set; }
        public string? RiskBehavior { get; set; }
        public string? TravelHistory { get; set; }
        public string? TattooOrSurgery { get; set; }
        public bool WeightOver45kg { get; set; }
        public string? Notes { get; set; }
    }
}
