                        -HC:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86_64
-DC<PERSON>KE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\FPT\SE8\PRM392\FP\FEv3\build\app\intermediates\cxx\Debug\1i75b6c4\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\FPT\SE8\PRM392\FP\FEv3\build\app\intermediates\cxx\Debug\1i75b6c4\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\FPT\SE8\PRM392\FP\FEv3\android\app\.cxx\Debug\1i75b6c4\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2