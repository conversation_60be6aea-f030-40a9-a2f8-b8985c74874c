﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Core.Base;
using BloodPlus.ModelViews.AppointmentModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface IAppointmentService
    {
        Task<string> CreateAppointmentAsync(CreateAppointmentModelView model, string userId);
        Task<AppointmentModelView> GetAppointmentByIdAsync(string id);
        Task<BasePaginatedList<ListAppointmentForUser>> GetAllAppointmentAsync(int pageNumber, int pageSize, string apmUserId);
        Task<string> DeleteAppointmentAsync(string id, string userId);
        Task<string> MarkCancelAppointmentAsync(string id, string userId);
        Task<string> MarkNeedConfirmAppointmentAsync(MarkCompletedModelView model, string userId);
        Task<string> MarkCompletedAppointmentAsync(string id, string userId);
        Task<BasePaginatedList<ListAppointmentForAdmin>> GetAllAppointmentForAdminAsync(int pageNumber, int pageSize);
        Task<string> CreateEmergencyAppointmentAsync(EmergencyAppointmentCreateModelView model, string userId);
    }
}
