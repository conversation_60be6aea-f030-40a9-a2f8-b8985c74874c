﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;

namespace BloodPlus.Services.Services
{
    public class SendMailService
    {
        private readonly IUnitOfWork _unitOfWork;
        public SendMailService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task SendOtpAsync(string email)
        {
            var otp = new Random().Next(100000, 999999).ToString();

            // Lưu vào DB
            var entity = new Otp
            {
                Email = email,
                OtpNumber = otp,
                CreatedBy = "admin",
                CreatedTime = DateTime.Now,
            };
            await _unitOfWork.GetRepository<Otp>().InsertAsync(entity);
            await _unitOfWork.SaveAsync();

            // Gửi email
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("BloodPlus", "<EMAIL>"));
            message.To.Add(MailboxAddress.Parse(email));
            message.Subject = "Mã OTP của bạn";
            message.Body = new TextPart("html")
            {
                Text = $@"
                    <div style='font-family: Arial, sans-serif; background-color: #f7f7f7; padding: 20px;'>
                        <div style='max-width: 500px; margin: auto; background-color: #ffffff; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 0 10px rgba(0,0,0,0.1);'>
                        <img src='https://firebasestorage.googleapis.com/v0/b/bloodplus-e8d34.firebasestorage.app/o/images%2Flogo_bloodplus.jpg?alt=media&token=9296a500-07ea-418b-9c61-bd618fc687af' 
		                    alt='BloodPlus Logo' style='max-width: 120px; margin-bottom: 20px;' />
                        <h2 style='color: #e53935;'>Mã xác thực OTP của bạn</h2>
                        <p style='font-size: 16px;'>Xin chào,</p>
                        <p style='font-size: 16px;'>Mã xác thực <strong>Máu+</strong> của bạn là:</p>
                        <p style='font-size: 24px; font-weight: bold; color: #e53935;'>{otp}</p>
                        <p style='font-size: 14px; color: #888;'>Mã này sẽ hết hạn sau 3 phút.</p>
                        <hr style='margin: 20px 0;' />
                        <p style='font-size: 12px; color: #aaa;'>Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email.</p>
                    </div>
                   </div>"
            };

            using var client = new SmtpClient();
            await client.ConnectAsync("smtp.gmail.com", 587, SecureSocketOptions.StartTls);
            await client.AuthenticateAsync("<EMAIL>", "bdmgdcfgqgblgiew");
            await client.SendAsync(message);
            await client.DisconnectAsync(true);
        }
    }
}
