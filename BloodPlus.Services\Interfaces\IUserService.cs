﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.UserModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface IUserService
    {
        Task<UserModelView> GetUserByIdAsync(string id);
        Task<BloodDonationWaitTimeModel> ViewDayToDonateBloodAsync(string userId);
        Task<string> DeleteUserAsync(string id, string deletedBy);
        Task<BasePaginatedList<ListUserModelView>> GetAllUserAsync(int pageNumber, int pageSize, string? role);
        Task<string> UpdateUserForAdminAsync(string id, UpdateUserModelView model, string updatedBy);
        Task<BasePaginatedList<ListRankDonate>> GetRankDonate(int pageNumber, int pageSize);
        Task<DetailRankingModel> GetDetailRankerByIdAsync(string id);
        Task<string> ChangePasswordAsync(ChangePasswordModel model, string userId);
        Task<UpdateUserResponse> UpdateUserAsync(UpdateInformationModel model, string userId);


        Task<string> CreateManagerAccountAsync(CreateManagerAccountModel model);
        Task<string> UpdateEmailAsync(string currentUserId, UpdateEmailRequest request);
        Task<string> VerifyOtpEmailAsync(VerifyOtpModel model);
        Task<string> RequestUpdateEmail(string userId);
    }
}
