<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- <PERSON><PERSON><PERSON><PERSON> cần thiết -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />

    <application
        android:label="<PERSON><PERSON><PERSON>"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!-- Khai báo Flutter Downloader service -->
        <service
            android:name="com.thoaidt.bloodplusmobile.flutter_downloader.DownloadWorker"
            android:permission="android.permission.FOREGROUND_SERVICE"
            android:exported="false" />

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- Cấu hình Google Sign-In -->
        <meta-data
            android:name="com.google.android.gms.signin.client_id"
            android:value="200053914917-0v8qphjmrav3mlh3j9pi4u2459ffcva2.apps.googleusercontent.com" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
    </application>

    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>

        <!-- Hỗ trợ mở ứng dụng lịch mặc định -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="content" />
            <data android:host="com.android.calendar" />
        </intent>

        <!-- Hỗ trợ mở Google Calendar -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
            <data android:host="calendar.google.com" />
        </intent>

        <!-- Hỗ trợ mở Google Maps -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
            <data android:host="www.google.com" />
        </intent>

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="geo" />
        </intent>
        <package android:name="com.google.android.apps.maps" />

        <!-- Hỗ trợ mở trình duyệt -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="https" />
        </intent>

    </queries>

</manifest>
