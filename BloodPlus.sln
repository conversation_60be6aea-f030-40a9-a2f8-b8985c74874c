﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "1.SERVER", "1.SERVER", "{B460B466-2E37-4CED-9C78-0F3BFAE5D22E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BloodPlus.API", "BloodPlus.API\BloodPlus.API.csproj", "{948E9D9F-548B-4BE5-9085-F10F600CABF6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2.Services", "2.Services", "{FD3577B8-F075-4DD1-BFA0-5284A0817D1F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3.REPOSITORIES", "3.REP<PERSON><PERSON>ORIES", "{F7CFDEF2-9334-47A9-8B62-91A910702C9D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4.CORE", "4.CORE", "{0355D415-0F46-4553-9F8D-1299FFC09894}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BloodPlus.Core", "BloodPlus.Core\BloodPlus.Core.csproj", "{5E068EF5-86F4-4CEF-B3CC-06CE41639D88}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BloodPlus.Services", "BloodPlus.Services\BloodPlus.Services.csproj", "{488EBF81-9C6C-428C-AF13-C747B3A7E412}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BloodPlus.Repositories", "BloodPlus.Repositories\BloodPlus.Repositories.csproj", "{1DCFF771-CA47-42D6-AA22-E8D4D6A8FACE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BloodPlus.ModelViews", "BloodPlus.ModelViews\BloodPlus.ModelViews.csproj", "{A2964C8C-D60B-4DCB-83EB-125C70ED668A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BloodPlus.Contract.Repositories", "BloodPlus.Contract.Repositories\BloodPlus.Contract.Repositories.csproj", "{D42143A6-FDF4-4046-AF04-2A4AAFD0147B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{948E9D9F-548B-4BE5-9085-F10F600CABF6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{948E9D9F-548B-4BE5-9085-F10F600CABF6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{948E9D9F-548B-4BE5-9085-F10F600CABF6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{948E9D9F-548B-4BE5-9085-F10F600CABF6}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E068EF5-86F4-4CEF-B3CC-06CE41639D88}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E068EF5-86F4-4CEF-B3CC-06CE41639D88}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E068EF5-86F4-4CEF-B3CC-06CE41639D88}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E068EF5-86F4-4CEF-B3CC-06CE41639D88}.Release|Any CPU.Build.0 = Release|Any CPU
		{488EBF81-9C6C-428C-AF13-C747B3A7E412}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{488EBF81-9C6C-428C-AF13-C747B3A7E412}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{488EBF81-9C6C-428C-AF13-C747B3A7E412}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{488EBF81-9C6C-428C-AF13-C747B3A7E412}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DCFF771-CA47-42D6-AA22-E8D4D6A8FACE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DCFF771-CA47-42D6-AA22-E8D4D6A8FACE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DCFF771-CA47-42D6-AA22-E8D4D6A8FACE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DCFF771-CA47-42D6-AA22-E8D4D6A8FACE}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2964C8C-D60B-4DCB-83EB-125C70ED668A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2964C8C-D60B-4DCB-83EB-125C70ED668A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2964C8C-D60B-4DCB-83EB-125C70ED668A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2964C8C-D60B-4DCB-83EB-125C70ED668A}.Release|Any CPU.Build.0 = Release|Any CPU
		{D42143A6-FDF4-4046-AF04-2A4AAFD0147B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D42143A6-FDF4-4046-AF04-2A4AAFD0147B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D42143A6-FDF4-4046-AF04-2A4AAFD0147B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D42143A6-FDF4-4046-AF04-2A4AAFD0147B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{948E9D9F-548B-4BE5-9085-F10F600CABF6} = {B460B466-2E37-4CED-9C78-0F3BFAE5D22E}
		{5E068EF5-86F4-4CEF-B3CC-06CE41639D88} = {0355D415-0F46-4553-9F8D-1299FFC09894}
		{488EBF81-9C6C-428C-AF13-C747B3A7E412} = {FD3577B8-F075-4DD1-BFA0-5284A0817D1F}
		{1DCFF771-CA47-42D6-AA22-E8D4D6A8FACE} = {F7CFDEF2-9334-47A9-8B62-91A910702C9D}
		{A2964C8C-D60B-4DCB-83EB-125C70ED668A} = {F7CFDEF2-9334-47A9-8B62-91A910702C9D}
		{D42143A6-FDF4-4046-AF04-2A4AAFD0147B} = {F7CFDEF2-9334-47A9-8B62-91A910702C9D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CE4B33B8-DB5C-427D-B7EC-1F78FD3E49A6}
	EndGlobalSection
EndGlobal
