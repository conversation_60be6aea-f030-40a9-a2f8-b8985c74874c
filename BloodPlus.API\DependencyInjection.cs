﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Repositories.Context;
using BloodPlus.Repositories.UOW;
using BloodPlus.Services;
using BloodPlus.Services.Interfaces;
using BloodPlus.Services.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace BloodPlus.API
{
    public static class DependencyInjection
    {
        public static void AddConfig(this IServiceCollection services, IConfiguration configuration)
        {
            services.ConfigRoute();
            services.AddDatabase(configuration);
            services.AddInfrastructure(configuration);
            services.AddIdentity();
            services.AddServices();
            services.ConfigJwt(configuration);

            services.Configure<CookiePolicyOptions>(options =>
            {
                options.CheckConsentNeeded = context => true;
            });

            services.AddControllers();
        }

        public static void ConfigRoute(this IServiceCollection services)
        {
            services.Configure<RouteOptions>(options =>
            {
                options.LowercaseUrls = true;
            });
        }

        public static void AddDatabase(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<DatabaseContext>(options =>
            {
                options.UseLazyLoadingProxies().UseSqlServer(configuration.GetConnectionString("database"));
            });
        }

        public static void AddIdentity(this IServiceCollection services)
        {
            services.AddIdentity<User, Contract.Repositories.Entity.Role>(options =>
            {
            })
             .AddEntityFrameworkStores<DatabaseContext>()
             .AddDefaultTokenProviders();
        }

        public static void AddServices(this IServiceCollection services)
        {
            services
                .AddScoped<IUnitOfWork, UnitOfWork>()
                .AddScoped<IPasswordHasher<User>, PasswordHasher<User>>()
                .AddScoped<IAuthService, AuthService>()
                .AddScoped<ITokenService, TokenService>()
                .AddScoped<IOrganizationService, OrganizationService>()
                .AddScoped<IBlogService, BlogService>()
                .AddScoped<IUserService, UserService>()
                .AddScoped<IDonationEventService, DonationEventService>()
                .AddScoped<IAppointmentService, AppointmentService>()
                .AddScoped<IVoucherService, VoucherService>()
                .AddScoped<INotificationService, NotificationService>()
                .AddScoped<IChatGPTService, ChatGPTService>()
                .AddScoped<IChatMessageService, ChatMessageService>()
                .AddScoped<IChatGPTService, ChatGPTService>()
                .AddScoped<IChatGeminiService, ChatGeminiService>()
                .AddScoped<IBloodTypeService, BloodTypeService>()
                .AddScoped<IAIChatService, FallbackAIService>()
                .AddScoped<IEmergencyDonationService, EmergencyDonationService>()
                .AddScoped<IGeolocationService, LocationIQGeolocationService>()
                .AddScoped<FirebaseService>()
                .AddScoped<OneSignalService>()
                .AddScoped<SendMailService>()
                .AddScoped<ReminderJobService>()
                .AddSignalR();
        }

        public static void ConfigJwt(this IServiceCollection services, IConfiguration configuration)
        {
            var jwtSettings = configuration.GetSection("Jwt");
            var key = Encoding.ASCII.GetBytes(jwtSettings["Key"]);

            services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.RequireHttpsMetadata = true;
                x.SaveToken = true;
                x.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings["Issuer"],
                    ValidAudience = jwtSettings["Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromMinutes(1)
                };
            });
        }

    }
}
