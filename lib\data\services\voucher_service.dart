import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/voucher_model.dart';
import 'package:bloodplusmobile/data/repositories/voucher_response.dart';
import 'package:http/http.dart' as http;

class VoucherService {
  final UserManager _userManager = UserManager();

  Future<VoucherResponse> getVouchers({
    int pageNumber = 1,
    int pageSize = 5,
    int? type,
  }) async {
    final token = (await _userManager.getUserToken()) ?? '';
    final queryParameters = {
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
      if (type != null) 'type': type.toString(),
    };

    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.voucherForUser,
        token: token,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final voucherResponse = VoucherResponse.fromJson(data['Message']);
        return voucherResponse;
      } else {
        throw Exception('Failed to fetch voucher list: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Get voucher list error: $e');
      throw Exception('Connection error when fetching voucher list: $e');
    }
  }

  Future<Voucher> getVoucherById(String voucherId) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedGet(
        '${ApiConfig.voucher}/$voucherId',
        token: token,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final voucher = Voucher.fromJson(data['Message']);
        return voucher;
      } else {
        throw Exception('Failed to fetch voucher detail: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Get voucher detail error: $e');
      throw Exception('Connection error when fetching voucher detail: $e');
    }
  }

  Future<VoucherResponse> getMyVouchers({
    int pageNumber = 1,
    int pageSize = 10,
    int? type,
  }) async {
    final token = (await _userManager.getUserToken()) ?? '';
    final queryParameters = {
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
      if (type != null) 'type': type.toString(),
    };

    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.voucherOfUser,
        token: token,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final voucherResponse = VoucherResponse.fromJson(data['Message']);
        return voucherResponse;
      } else {
        throw Exception('Failed to fetch my voucher list: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Get my voucher list error: $e');
      throw Exception('Connection error when fetching my voucher list: $e');
    }
  }

  Future<String> addVoucherForUser(String voucherId) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedPatch(
        '${ApiConfig.voucher}/$voucherId',
        token: token,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['Message'] as String;
      } else {
        throw Exception('Failed to redeem voucher: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Redeem voucher error: $e');
      throw Exception('Connection error when redeeming voucher: $e');
    }
  }
}