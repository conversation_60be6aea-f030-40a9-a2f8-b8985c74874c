﻿using Microsoft.AspNetCore.Http;

namespace BloodPlus.ModelViews.BlogModelViews
{
    public class CreateBlogModelView
    {
        public string Title { get; set; }
        public string Author { get; set; }
        public string Description { get; set; }
        public string Content { get; set; }
        public int? ViewNumber { get; set; }
        public IFormFile? Image1 { get; set; }
        public IFormFile? Image2 { get; set; }
        public IFormFile? Image3 { get; set; }
        public IFormFile? Image4 { get; set; }
    }
}
