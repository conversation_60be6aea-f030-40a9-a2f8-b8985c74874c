import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

enum NotificationType {
  appointment,
  event,
  system,
  emergency,
  unknown,
}

class NotificationModel {
  final String id;
  final String title;
  final String content;
  final bool isRead;
  final DateTime? sentDate;
  final NotificationType type;

  NotificationModel({
    required this.id,
    required this.title,
    required this.content,
    this.isRead = false,
    this.sentDate,
    this.type = NotificationType.system,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['Id']?.toString() ?? json['id']?.toString() ?? '',
      title: json['Title']?.toString() ?? json['title']?.toString() ?? '',
      content: json['Content']?.toString() ?? json['content']?.toString() ?? '',
      isRead: json['IsRead'] as bool? ?? json['isRead'] as bool? ?? false,
      sentDate: json['SentDate'] != null || json['sentDate'] != null
          ? DateTime.tryParse((json['SentDate'] ?? json['sentDate']).toString())
          : null,
      type: _parseNotificationType(json['Title']?.toString() ?? ''),
    );
  }

  static NotificationType _parseNotificationType(String title) {
    final lowerTitle = title.toLowerCase();
    if (lowerTitle.contains('lịch hẹn') || lowerTitle.contains('appointment')) {
      return NotificationType.appointment;
    } else if (lowerTitle.contains('sự kiện') || lowerTitle.contains('event') || lowerTitle.contains('hiến máu')) {
      return NotificationType.event;
    } else if (lowerTitle.contains('khẩn cấp') || lowerTitle.contains('emergency')) {
      return NotificationType.emergency;
    } else {
      return NotificationType.system;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'Id': id,
      'Title': title,
      'Content': content,
      'IsRead': isRead,
      'SentDate': sentDate?.toIso8601String(),
    };
  }

  String getFormattedDate() {
    if (sentDate == null) return '';
    try {
      return DateFormat('dd/MM/yyyy HH:mm').format(sentDate!);
    } catch (e) {
      return sentDate!.toIso8601String();
    }
  }

  String getTypeDisplayName(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (type) {
      case NotificationType.appointment:
        return localizations.translate('notification_types.appointment');
      case NotificationType.event:
        return localizations.translate('notification_types.event');
      case NotificationType.emergency:
        return localizations.translate('notification_types.emergency');
      case NotificationType.system:
        return localizations.translate('notification_types.system');
      case NotificationType.unknown:
        return localizations.translate('notification_types.unknown');
    }
  }

  IconData getTypeIcon() {
    switch (type) {
      case NotificationType.appointment:
        return Icons.calendar_today;
      case NotificationType.event:
        return Icons.event;
      case NotificationType.emergency:
        return Icons.emergency;
      case NotificationType.system:
        return Icons.notifications;
      case NotificationType.unknown:
        return Icons.notifications_none;
    }
  }

  Color getTypeColor() {
    switch (type) {
      case NotificationType.appointment:
        return Colors.blue;
      case NotificationType.event:
        return Colors.green;
      case NotificationType.emergency:
        return Colors.red;
      case NotificationType.system:
        return Colors.orange;
      case NotificationType.unknown:
        return Colors.grey;
    }
  }

  // For backward compatibility
  String get body => content;
  DateTime? get createdAt => sentDate;
} 