import 'dart:math';
import 'package:flutter/material.dart';

class SplashPainter extends CustomPainter {
  final double animationValue;

  SplashPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    if (animationValue <= 0) return;

    final paint = Paint()
      ..color = Colors.red.shade800
      ..style = PaintingStyle.fill;

    // Create multiple splash circles with different sizes and positions
    final splashCenters = [
      Offset(size.width * 0.5, size.height * 1.1), // Main center splash
      Offset(size.width * 0.3, size.height * 1.05), // Left splash
      Offset(size.width * 0.7, size.height * 1.05), // Right splash
      Offset(size.width * 0.1, size.height * 1.08), // Far left
      Offset(size.width * 0.9, size.height * 1.08), // Far right
    ];

    final splashRadii = [
      size.width * 1.2, // Main splash
      size.width * 0.8, // Left splash
      size.width * 0.8, // Right splash
      size.width * 0.6, // Far left
      size.width * 0.6, // Far right
    ];

    // Draw splash effects
    for (int i = 0; i < splashCenters.length; i++) {
      final center = splashCenters[i];
      final maxRadius = splashRadii[i];

      // Calculate current radius based on animation value
      final currentRadius = maxRadius * animationValue;

      // Add some variation to the animation timing
      final delayFactor = i * 0.1;
      final adjustedValue = (animationValue - delayFactor).clamp(0.0, 1.0);

      if (adjustedValue > 0) {
        final radius = maxRadius * adjustedValue;

        // Create gradient effect
        final gradient = RadialGradient(
          colors: [
            Colors.red.shade700.withOpacity(0.9),
            Colors.red.shade800.withOpacity(0.7),
            Colors.red.shade900.withOpacity(0.5),
            Colors.transparent,
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        );

        final gradientPaint = Paint()
          ..shader = gradient.createShader(
            Rect.fromCircle(center: center, radius: radius),
          );

        canvas.drawCircle(center, radius, gradientPaint);
      }
    }

    // Add some splatter effects
    _drawSplatterEffects(canvas, size, paint);
  }

  void _drawSplatterEffects(Canvas canvas, Size size, Paint paint) {
    final random = Random(42); // Fixed seed for consistent splatter
    final splatterCount = 20;

    for (int i = 0; i < splatterCount; i++) {
      final progress = (animationValue - i * 0.02).clamp(0.0, 1.0);
      if (progress <= 0) continue;

      final x = size.width * (0.2 + random.nextDouble() * 0.6);
      final y = size.height * (0.8 + random.nextDouble() * 0.3);
      final radius = (5 + random.nextDouble() * 15) * progress;

      final splatterPaint = Paint()
        ..color = Colors.red.shade700.withOpacity(0.6 * progress)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(x, y), radius, splatterPaint);
    }
  }

  @override
  bool shouldRepaint(SplashPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}