import 'dart:io';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:flutter/material.dart';

class ProfileAvatarWidget extends StatelessWidget {
  final String? userImagePath;
  final String? userImage;
  final VoidCallback onTap;

  const ProfileAvatarWidget({
    Key? key,
    this.userImagePath,
    this.userImage,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: AppTheme.primaryGradient,
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryRed.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          padding: const EdgeInsets.all(4),
          child: GestureDetector(
            onTap: onTap,
            child: CircleAvatar(
              radius: 60,
              backgroundColor: Colors.white,
              child: CircleAvatar(
                radius: 56,
                backgroundImage: _getImageProvider(),
                backgroundColor: AppTheme.backgroundColor,
                child: _getImageProvider() == null
                    ? Icon(
                  Icons.person,
                  size: 60,
                  color: AppTheme.textSecondary,
                )
                    : null,
              ),
            ),
          ),
        ),

        // Edit button
        Positioned(
          bottom: 4,
          right: 4,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: AppTheme.borderColor,
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.camera_alt,
                size: 20,
                color: AppTheme.primaryRed,
              ),
            ),
          ),
        ),
      ],
    );
  }

  ImageProvider? _getImageProvider() {
    if (userImagePath != null) {
      return FileImage(File(userImagePath!));
    }

    if (userImage != null && userImage!.isNotEmpty) {
      if (userImage!.startsWith('http')) {
        return NetworkImage(userImage!);
      } else {
        return AssetImage(userImage!);
      }
    }

    return null;
  }
}