﻿using BloodPlus.Repositories.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace BloodPlus.Services.Services
{
    public class ReminderJobService : BackgroundService
    {
        private readonly IServiceScopeFactory _scopeFactory;

        public ReminderJobService(IServiceScopeFactory scopeFactory)
        {
            _scopeFactory = scopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                using var scope = _scopeFactory.CreateScope();
                var db = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
                var oneSingalService = scope.ServiceProvider.GetRequiredService<OneSignalService>();

                var now = DateTime.Now;
                var reminderTime = now.AddMinutes(120);

                var appointments = await db.Appointment
                    .Where(a => a.DonationEvent.EventDate >= now && a.DonationEvent.EventDate <= reminderTime)
                    .Include(a => a.User)
                    .ToListAsync();

                foreach (var appt in appointments)
                {
                    var token = appt.User.DeviceToken;
                    if (!string.IsNullOrEmpty(token))
                    {
                        string title = "Máu+ thông báo";
                        string body = $"Bạn có lịch hẹn hiến máu lúc {appt.DonationEvent.EventDate:HH:mm}, hãy đến đúng giờ nhé!";
                        await oneSingalService.SendNotificationAsync(title, body, appt.UserId.ToString(), token);
                    }
                }

                // Chờ 5 phút rồi lặp lại
                await Task.Delay(TimeSpan.FromHours(12), stoppingToken);
            }
        }
    }
}
