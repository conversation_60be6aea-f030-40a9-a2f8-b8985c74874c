class UserModel {
  final String id;
  final String? userImage;
  final String name;
  final String email;
  final String? bloodType;
  final String? job;
  final DateTime? dateOfBirth;
  final int donationCount;
  final String? address;
  final String? passportNumber;
  final double? latitude;
  final double? longitude;
  final int? gender;
  final String? phoneNumber;
  final int point;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    String? userImage,
    this.bloodType,
    this.job,
    this.dateOfBirth,
    required this.donationCount,
    this.address,
    this.passportNumber,
    this.latitude,
    this.longitude,
    this.gender,
    this.phoneNumber,
    this.point = 0,
  }) : userImage = _validateImage(userImage);

  // Hàm kiểm tra và gán ảnh mặc định nếu userImage không hợp lệ
  static String? _validateImage(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty || !imageUrl.startsWith('http')) {
      print('Invalid or missing userImage, using default: assets/images/profile.jpg');
      return 'assets/images/profile.jpg';
    }
    return imageUrl;
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    // API trả về data trong key "Message" với PascalCase
    final data = json['Message'] ?? json['message'] ?? json;

    print('UserModel.fromJson - Raw data: $data');

    return UserModel(
      id: data['Id']?.toString() ?? data['id']?.toString() ?? '',
      userImage: data['UserImage']?.toString() ?? data['userImage']?.toString(),
      name: data['Name']?.toString() ?? data['name']?.toString() ?? '',
      email: data['Email']?.toString() ?? data['email']?.toString() ?? '',
      bloodType: data['BloodType']?.toString() ?? data['bloodType']?.toString(),
      job: data['Job']?.toString() ?? data['job']?.toString(),
      dateOfBirth: (data['DateOfBirth'] ?? data['dateOfBirth']) != null
          ? DateTime.tryParse((data['DateOfBirth'] ?? data['dateOfBirth']).toString())
          : null,
      donationCount: (data['DonationCount'] as num?)?.toInt() ??
                     (data['donationCount'] as num?)?.toInt() ?? 0,
      address: data['Address']?.toString() ?? data['address']?.toString(),
      passportNumber: data['PassportNumber']?.toString() ?? data['passportNumber']?.toString(),
      latitude: (data['Latitude'] as num?)?.toDouble() ??
                (data['latitude'] as num?)?.toDouble(),
      longitude: (data['Longitude'] as num?)?.toDouble() ??
                 (data['longitude'] as num?)?.toDouble(),
      gender: (data['Gender'] as num?)?.toInt() ??
              (data['gender'] as num?)?.toInt(),
      phoneNumber: data['PhoneNumber']?.toString() ?? data['phoneNumber']?.toString(),
      point: (data['Point'] as num?)?.toInt() ?? (data['point'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userImage': userImage,
      'name': name,
      'email': email,
      'bloodType': bloodType,
      'job': job,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'donationCount': donationCount,
      'address': address,
      'passportNumber': passportNumber,
      'latitude': latitude,
      'longitude': longitude,
      'gender': gender,
      'phoneNumber': phoneNumber,
      'point': point,
    };
  }
}