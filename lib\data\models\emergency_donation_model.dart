import 'dart:io';

class EmergencyDonationRequest {
  final bool isEmergency;
  final DateTime eventDate;
  final DateTime endTime;
  final int requiredDonors;
  final String organizationId;
  final String requiredBloodType;
  final String location;
  final File? imageFile;         // nullable vì có thể gửi image URL thay thế
  final String title;
  final String image;            // image URL nếu không gửi file
  final String description;

  EmergencyDonationRequest({
    required this.isEmergency,
    required this.eventDate,
    required this.endTime,
    required this.requiredDonors,
    required this.organizationId,
    required this.requiredBloodType,
    required this.location,
    this.imageFile,
    required this.title,
    required this.image,
    required this.description,
  });
}
class EmergencyDonationEventModel {
  final String id;
  final String title;
  final String organizationName;
  final String requiredBloodTypeName;
  final String location;
  final String? imageUrl;
  final DateTime eventDate;
  final int requiredDonors;
  final int currentDonors;

  EmergencyDonationEventModel({
    required this.id,
    required this.title,
    required this.organizationName,
    required this.requiredBloodTypeName,
    required this.location,
    this.imageUrl,
    required this.eventDate,
    required this.requiredDonors,
    required this.currentDonors,
  });

  factory EmergencyDonationEventModel.fromJson(Map<String, dynamic> json) {
    return EmergencyDonationEventModel(
      id: json['Id'],
      title: json['Title'],
      organizationName: json['OrganizationName'],
      requiredBloodTypeName: json['RequiredBloodTypeName'],
      location: json['Location'],
      imageUrl: json['ImageUrl'],
      eventDate: DateTime.parse(json['EventDate']),
      requiredDonors: json['RequiredDonors'],
      currentDonors: json['CurrentDonors'],
    );
  }
}

class PaginationModel<T> {
  final List<T> items;
  final int totalItems;
  final int currentPage;
  final int totalPages;
  final int pageSize;
  final bool hasPreviousPage;
  final bool hasNextPage;

  PaginationModel({
    required this.items,
    required this.totalItems,
    required this.currentPage,
    required this.totalPages,
    required this.pageSize,
    required this.hasPreviousPage,
    required this.hasNextPage,
  });

  factory PaginationModel.fromJson(
      Map<String, dynamic> json, T Function(Map<String, dynamic>) fromJsonT) {
    return PaginationModel(
      items: List<T>.from(json['Items'].map(fromJsonT)),
      totalItems: json['TotalItems'],
      currentPage: json['CurrentPage'],
      totalPages: json['TotalPages'],
      pageSize: json['PageSize'],
      hasPreviousPage: json['HasPreviousPage'],
      hasNextPage: json['HasNextPage'],
    );
  }
}
