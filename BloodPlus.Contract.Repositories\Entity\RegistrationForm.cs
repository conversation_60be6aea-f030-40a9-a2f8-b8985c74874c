﻿using BloodPlus.Core.Base;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace BloodPlus.Contract.Repositories.Entity
{
    public class RegistrationForm : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        public string AppointmentId { get; set; }

        [ForeignKey("AppointmentId")]
        public virtual Appointment Appointment { get; set; }

        public bool HasDonatedBefore { get; set; }
        public bool HasDiseases { get; set; }
        public string? DiseaseDetails { get; set; }
        public bool IsTakingMedicine { get; set; }
        public string? MedicineDetails { get; set; }
        public string? Symptoms { get; set; }
        public string? RiskBehavior { get; set; }
        public string? TravelHistory { get; set; }
        public string? TattooOrSurgery { get; set; }
        public bool WeightOver45kg { get; set; }
        public string? Notes { get; set; }
        public bool HasPreviousInfections { get; set; }
        public string? PreviousInfectionsDetails { get; set; }
        public bool HadRecentIllness12Months { get; set; }
        public string? RecentIllness12MonthsDetails { get; set; }
        public bool HadRecentIllness6Months { get; set; }
        public bool HadRecentIllness1Month { get; set; }
        public bool HadRecentIllness14Days { get; set; }
        public string? RecentIllness14DaysDetails { get; set; }
        public bool UsedAntibiotics7Days { get; set; }
        public string? AntibioticsDetails { get; set; }
        public bool IsPregnantOrRecentMother { get; set; }
        public string? PregnancyDetails { get; set; }
    }
}
