﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.OrganizationModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface IOrganizationService
    {
        Task<string> CreateOrganizationAsync(CreateOrganizationModelView model, string userId);
        Task<List<OrganizationModelView>> GetOrganizationByUserIdAsync(string userId);
        Task<string> DeleteOrganizationAsync(string id, string userId);
        Task<OrganizationModelView> GetOrganizationByIdAsync(string id);
        Task<BasePaginatedList<OrganizationModelView>> GetAllOrganizationAsync(int pageNumber, int pageSize, string? name, string? address);
        Task<string> UpdateOrganizationAsync(string id, CreateOrganizationModelView model, string userId);
    }
}
