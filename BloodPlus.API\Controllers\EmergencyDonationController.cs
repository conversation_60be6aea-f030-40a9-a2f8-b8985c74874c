﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Core.Base;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.AppointmentModelViews;
using BloodPlus.ModelViews.ChatMessageModelView;
using BloodPlus.ModelViews.DonationEventModelView;
using BloodPlus.ModelViews.EmergencyEventModelView;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmergencyDonationController : ControllerBase
    {
        private readonly IEmergencyDonationService _emergencyDonationService;

        public EmergencyDonationController(IEmergencyDonationService emergencyDonationService)
        {
            _emergencyDonationService = emergencyDonationService;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost]
        public async Task<IActionResult> CreateEmergencyDonation([FromForm] DonationEmergencyEventCreateModelView input)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _emergencyDonationService.CreateEmergencyEventAsync(input, userId);
            return Ok(result);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<ActionResult<BasePaginatedList<EDModelView>>> GetAllEmergencyDonationEvent(bool isUser,int pageNumber = 1, int pageSize = 5)
        {
            string userId = User.FindFirst("userId")?.Value;
            var result = await _emergencyDonationService.GetAllEmergencyEventAsync(pageNumber, pageSize, userId, isUser);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("{id}")]
        public async Task<ActionResult<BasePaginatedList<EDModelView>>> GetEmergencyDonationEventById(string id)
        {
            var result = await _emergencyDonationService.GetEmergencyEventDetailAsync(id);

            return Ok(new { Message = result });
        }
    }
}
