class OrganizationModel {
  final String id;
  final String name;
  final String address;
  final String contactPhone;
  final String contactEmail;
  final String? image;

  OrganizationModel({
    required this.id,
    required this.name,
    required this.address,
    required this.contactPhone,
    required this.contactEmail,
    this.image,
  });

  factory OrganizationModel.fromJson(Map<String, dynamic> json) {
    return OrganizationModel(
      id: json['Id'],
      name: json['Name'],
      address: json['Address'],
      contactPhone: json['ContactPhone'],
      contactEmail: json['ContactEmail'],
      image: json['Image'],
    );
  }
}
