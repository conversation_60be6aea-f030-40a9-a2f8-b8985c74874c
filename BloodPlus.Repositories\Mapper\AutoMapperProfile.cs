﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.BlogModelViews;
using BloodPlus.ModelViews.ChatMessageModelView;
using BloodPlus.ModelViews.DonationEventModelView;
using BloodPlus.ModelViews.OrganizationModelViews;
using BloodPlus.ModelViews.UserModelViews;
using BloodPlus.ModelViews.VoucherModelViews;

namespace BloodPlus.Repositories.Mapper
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<CreateOrganizationModelView, Organization>();
            CreateMap<Organization, OrganizationModelView>();

            CreateMap<CreateBlogModelView, Blog>();
            CreateMap<Blog, BlogModelView>();
            CreateMap<Blog, ListBlogModelView>();

            CreateMap<User, UserModelView>();

            CreateMap<CreateDEModelView, DonationEvent>();
            CreateMap<UpdateDEModelView, DonationEvent>();
            CreateMap<DonationEvent, DonationEventModelView>();
            CreateMap<DonationEvent, ListDEModelView>();

            CreateMap<Voucher, VoucherModelView>();

            CreateMap<UpdateChatMessageModelView, ChatMessage>();
            CreateMap<ChatMessage, ChatMessageModelView>();


        }
    }
}
