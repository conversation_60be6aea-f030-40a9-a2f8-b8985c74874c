import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:flutter/material.dart';

class CustomBottomNavBar extends StatefulWidget {
  final int selectedIndex;
  final ValueChanged<int> onItemTapped;

  const CustomBottomNavBar({
    required this.selectedIndex,
    required this.onItemTapped,
    Key? key,
  }) : super(key: key);

  @override
  State<CustomBottomNavBar> createState() => _CustomBottomNavBarState();
}

class _CustomBottomNavBarState extends State<CustomBottomNavBar>
    with TickerProviderStateMixin {
  late AnimationController _fabController;
  late Animation<double> _fabScale;
  late Animation<double> _fabRotation;

  @override
  void initState() {
    super.initState();
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _fabScale = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _fabController, curve: Curves.easeInOut),
    );
    _fabRotation = Tween<double>(begin: 0.0, end: 0.1).animate(
      CurvedAnimation(parent: _fabController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _fabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final bottomPadding = mediaQuery.padding.bottom;
    // Responsive height for the nav bar
    final navBarHeight = screenWidth < 600 ? 80.0 : 96.0; // Larger for tablets
    final iconSize = screenWidth < 600 ? 24.0 : 28.0;
    final fabSize = screenWidth < 600 ? 72.0 : 80.0;
    final fontSize = screenWidth < 600 ? 11.0 : 12.0;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 40,
            offset: const Offset(0, -8),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          height: navBarHeight + bottomPadding, // Account for safe area
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.center,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.04, // 4% of screen width
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildNavItem(
                            icon: Icons.home_rounded,
                            label: localizations.translate('home'),
                            index: 0,
                            isSelected: widget.selectedIndex == 0,
                            onTap: widget.onItemTapped,
                            iconSize: iconSize,
                            fontSize: fontSize,
                          ),
                          _buildNavItem(
                            icon: Icons.agriculture,
                            label: localizations.translate('voucher'),
                            index: 1,
                            isSelected: widget.selectedIndex == 1,
                            onTap: (i) {
                              widget.onItemTapped(i);
                              NavigationService.navigateTo(AppRoutes.myVouchersScreen);
                            },
                            iconSize: iconSize,
                            fontSize: fontSize,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: fabSize * 0.5), // Space for center FAB
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildNavItem(
                            icon: Icons.handshake_rounded,
                            label: localizations.translate('history'),
                            index: 3,
                            isSelected: widget.selectedIndex == 3,
                            onTap: widget.onItemTapped,
                            iconSize: iconSize,
                            fontSize: fontSize,
                          ),
                          _buildNavItem(
                            icon: Icons.person_rounded,
                            label: localizations.translate('profile'),
                            index: 4,
                            isSelected: widget.selectedIndex == 4,
                            onTap: widget.onItemTapped,
                            iconSize: iconSize,
                            fontSize: fontSize,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                top: -(fabSize * 0.4), // Adjust FAB position
                child: AnimatedBuilder(
                  animation: _fabController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _fabScale.value,
                      child: Transform.rotate(
                        angle: _fabRotation.value,
                        child: GestureDetector(
                          onTapDown: (_) => _fabController.forward(),
                          onTapUp: (_) => _fabController.reverse(),
                          onTapCancel: () => _fabController.reverse(),
                          onTap: () {
                            widget.onItemTapped(2);
                            NavigationService.navigateTo(AppRoutes.donationEvent);
                          },
                          child: Container(
                            width: fabSize,
                            height: fabSize,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primaryRed,
                                  AppColors.primaryRed.withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primaryRed.withOpacity(0.4),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: AppColors.primaryRed.withOpacity(0.2),
                                  blurRadius: 40,
                                  offset: const Offset(0, 16),
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 2,
                                ),
                              ),
                              child: Icon(
                                Icons.bloodtype_rounded,
                                color: Colors.white,
                                size: fabSize * 0.45, // Scale icon with FAB
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    IconData? icon,
    String? customImage,
    required String label,
    required int index,
    required bool isSelected,
    required ValueChanged<int> onTap,
    required double iconSize,
    required double fontSize,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 200),
      tween: Tween<double>(begin: 0, end: isSelected ? 1 : 0),
      builder: (context, animation, child) {
        return GestureDetector(
          onTap: () => onTap(index),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.03,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Color.lerp(
                Colors.transparent,
                AppColors.primaryRed.withOpacity(0.1),
                animation,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Transform.scale(
                  scale: 1 + (animation * 0.1),
                  child: icon != null
                      ? Icon(
                    icon,
                    size: iconSize,
                    color: Color.lerp(
                      Colors.grey.shade500,
                      AppColors.primaryRed,
                      animation,
                    ),
                  )
                      : Image.asset(
                    customImage!,
                    width: iconSize,
                    height: iconSize,
                    color: Color.lerp(
                      Colors.grey.shade500,
                      AppColors.primaryRed,
                      animation,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                if (label.isNotEmpty)
                  Text(
                    label,
                    style: TextStyle(
                      color: Color.lerp(
                        Colors.grey.shade500,
                        AppColors.primaryRed,
                        animation,
                      ),
                      fontSize: fontSize,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                    textScaler: TextScaler.linear(
                      MediaQuery.textScalerOf(context).scale(1.0),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}