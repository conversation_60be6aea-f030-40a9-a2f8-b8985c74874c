import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/constants/app_colors.dart';

class ExitAppDialog {
  static Future<bool> showExitDialog(BuildContext context) async {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing based on screen width
    final dialogWidth = screenSize.width * 0.85 > 400.0 ? 400.0 : screenSize.width * 0.85;
    final padding = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final iconSize = isSmallScreen ? 40.0 : (isLargeScreen ? 56.0 : 48.0);
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final messageFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final buttonFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final buttonPadding = isSmallScreen ? 10.0 : (isLargeScreen ? 16.0 : 12.0);
    final spacing = isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);

    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => false,
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
            ),
            insetPadding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 16.0 : 24.0,
              vertical: isSmallScreen ? 16.0 : 24.0,
            ),
            child: Container(
              width: dialogWidth,
              padding: EdgeInsets.all(padding),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                color: Colors.white,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                    decoration: BoxDecoration(
                      color: AppColors.primaryRed.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.exit_to_app,
                      size: iconSize,
                      color: AppColors.primaryRed,
                    ),
                  ),
                  SizedBox(height: spacing),
                  // Title
                  Text(
                    localizations.translate('exit_app'),
                    style: TextStyle(
                      fontSize: titleFontSize,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: spacing),
                  // Message
                  Text(
                    localizations.translate('exit_app_message'),
                    style: TextStyle(
                      fontSize: messageFontSize,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: isSmallScreen ? 16 : 24),
                  // Buttons
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final buttonWidth = constraints.maxWidth / 2 - spacing / 2;
                      return Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(false),
                              style: TextButton.styleFrom(
                                padding: EdgeInsets.symmetric(vertical: buttonPadding),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                  side: BorderSide(color: Colors.grey.shade300),
                                ),
                              ),
                              child: Text(
                                localizations.translate('cancel'),
                                style: TextStyle(
                                  fontSize: buttonFontSize,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: spacing),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () => Navigator.of(context).pop(true),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primaryRed,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(vertical: buttonPadding),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                ),
                                elevation: 2,
                              ),
                              child: Text(
                                localizations.translate('exit'),
                                style: TextStyle(
                                  fontSize: buttonFontSize,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    ) ?? false;
  }
}