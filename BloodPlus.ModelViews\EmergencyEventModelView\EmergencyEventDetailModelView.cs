﻿using BloodPlus.ModelViews.EmergencyEventModelView;
namespace BloodPlus.ModelViews.EmergencyEventModelView
{
    public class EmergencyEventDetailModelView
    {
        public string Id { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Location { get; set; } = null!;
        public DateTime EventDate { get; set; }
        public DateTime EndTime { get; set; }
        public int RequiredDonors { get; set; }
        public int CurrentDonors { get; set; }
        public string? Image { get; set; }
        public bool IsEmergency { get; set; }
        public string? RequiredBloodType { get; set; }
        public string OrganizationName { get; set; } = null!;

        public List<EmergencyEventParticipantModelView> Participants { get; set; } = new();
    }
}