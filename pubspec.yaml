name: bloodplusmobile
description: "Blood donor android app"

version: 1.0.0+1

environment:
  sdk: ^3.7.2


dependencies:
  flutter:
    sdk: flutter
  geolocator: ^10.0.0
  google_maps_flutter: ^2.5.0
  flutter_localizations:
    sdk: flutter
  intl: any
  flutter_markdown: ^0.6.14
  cupertino_icons: ^1.0.8
  carousel_slider: ^5.0.0
  auto_size_text: ^3.0.0
  google_fonts: ^4.0.3
  qr_flutter: ^4.0.0
  screen_brightness: ^0.2.2
  flutter_launcher_icons: ^0.14.3
  shared_preferences: ^2.5.0
  provider: ^6.1.5
  url_launcher: ^6.3.0
  http: ^1.2.2
  google_sign_in: ^6.1.5
  http_parser: ^4.0.2
  flutter_datetime_picker_plus: ^2.2.0
  connectivity_plus: ^6.0.5
  image_picker: ^1.0.7
  permission_handler: ^11.0.1
  path_provider: ^2.1.1
  flutter_downloader: ^1.11.0
  flutter_map: ^6.0.0
  latlong2: ^0.9.0
  onesignal_flutter: ^5.3.3
  introduction_screen: ^3.1.17
  local_auth: ^2.3.0
  app_settings: ^6.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter


  flutter_lints: ^5.0.0


flutter:


  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/locales/

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icons/logo_origin.png"

