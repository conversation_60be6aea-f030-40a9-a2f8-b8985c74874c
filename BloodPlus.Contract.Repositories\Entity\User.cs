﻿using BloodPlus.Core.Enum;
using BloodPlus.Core.Utils;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations.Schema;

namespace BloodPlus.Contract.Repositories.Entity
{
    public class User : IdentityUser<Guid>
    {
        public string? BloodTypeId { get; set; }

        [ForeignKey("BloodTypeId")]
        public virtual BloodType BloodType { get; set; }
        public string? UserImage { get; set; }
        public string Name { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public ushort DonationCount { get; set; }
        public string? Address { get; set; }
        public int Point { get; set; }
        public string? Job { get; set; }
        public Gender? Gender { get; set; }
        public string? PassportNumber { get; set; }
        public string? CreatedBy { get; set; }
        public string? LastUpdatedBy { get; set; }
        public string? DeletedBy { get; set; }
        public string? DeviceToken { get; set; }
        public DateTimeOffset CreatedTime { get; set; }
        public DateTimeOffset LastUpdatedTime { get; set; }
        public DateTimeOffset? DeletedTime { get; set; }

        public User()
        {
            CreatedTime = CoreHelper.SystemTimeNow;
            LastUpdatedTime = CreatedTime;
        }
        public virtual ICollection<UserRole> UserRoles { get; set; }
        public virtual ICollection<Blog> Blogs { get; set; }
        public virtual ICollection<Notification> Notifications { get; set; }
        public virtual ICollection<Organization> Organizations { get; set; }
        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<RegistrationForm> RegistrationForms { get; set; }
        public virtual ICollection<Voucher> Vouchers { get; set; }
        public virtual ICollection<ChatMessage> ChatMessages { get; set; } = new List<ChatMessage>();
    }
}
