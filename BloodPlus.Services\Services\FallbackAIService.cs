﻿
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.ModelViews.ChatGPT;
using BloodPlus.ModelViews.Gemini;
using BloodPlus.Services.Interfaces;

namespace BloodPlus.Services.Services
{
    public class FallbackAIService : IAIChatService
    {
        private readonly IChatGPTService _chatGPTService;
        private readonly IChatGeminiService _geminiService;

        public FallbackAIService(IChatGPTService chatGPTService, IChatGeminiService geminiService)
        {
            _chatGPTService = chatGPTService;
            _geminiService = geminiService;
        }

        public async Task<string> GetCompletionAsync(string prompt,string location, string userName, string bloodType)
        {
            try
            {
                return await _chatGPTService.GetCompletionAsync(prompt, location, userName, bloodType);
            }
            catch (Exception ex) when (IsQuotaOrServerError(ex))
            {
                return await _geminiService.GetCompletionAsync(prompt, location, userName, bloodType);
            }
        }

        public async Task<string> GetCompletionWithHistoryAsync(string prompts, List<ChatAIMessage> messages, string location, string userName, string bloodType)
        {
            try
            {
                return await _chatGPTService.GetCompletionWithHistoryAsync(prompts, messages);
            }
            catch (Exception ex) when (IsQuotaOrServerError(ex))
            {
                return await _geminiService.GetCompletionWithHistoryAsync(prompts, messages, location, userName, bloodType);
            }
        }

        private bool IsQuotaOrServerError(Exception ex)
        {
            return ex.Message.Contains("insufficient_quota") || ex.Message.Contains("Too Many Requests");
        }
    }

}
