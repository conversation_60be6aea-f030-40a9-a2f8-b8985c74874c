import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageManager with ChangeNotifier {
  Locale _locale = const Locale('vi', 'VN'); // Default language
  static const List<Locale> supportedLocales = [
    Locale('vi', 'VN'),
    Locale('en', 'US'),
    Locale('zh', 'CN'),
    Locale('ko', 'KR'),
    Locale('th', 'TH'),
    Locale('es', 'ES'),
    Locale('ja', 'JP'),
  ];

  Locale get locale => _locale;

  Future<void> loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('languageCode') ?? 'vi';
    _locale = _getLocaleFromCode(languageCode);
    notifyListeners();
  }

  Future<void> changeLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', languageCode);
    _locale = _getLocaleFromCode(languageCode);
    notifyListeners();
  }

  Locale _getLocaleFromCode(String code) {
    switch (code) {
      case 'vi':
        return const Locale('vi', 'VN');
      case 'en':
        return const Locale('en', 'US');
      case 'zh':
        return const Locale('zh', 'CN');
      case 'ko':
        return const Locale('ko', 'KR');
      case 'th':
        return const Locale('th', 'TH');
      case 'es':
        return const Locale('es', 'ES');
      case 'ja':
        return const Locale('ja', 'JP');
      default:
        return const Locale('vi', 'VN');
    }
  }
}