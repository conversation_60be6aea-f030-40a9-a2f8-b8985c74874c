﻿using BloodPlus.ModelViews.UserModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService) 
        {
            _userService = userService;
        }

        [Authorize(Roles = "Admin")]
        [HttpPost("create-manager-account")]
        public async Task<ActionResult<UserModelView>> CreateManagerAccount(CreateManagerAccountModel model)
        {
            var result = await _userService.CreateManagerAccountAsync(model);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<UserModelView>> GetUserById(string id)
        {
            var result = await _userService.GetUserByIdAsync(id);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("day-watting")]
        public async Task<ActionResult<BloodDonationWaitTimeModel>> ViewDayToDonateBlood(string id)
        {
            var result = await _userService.ViewDayToDonateBloodAsync(id);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("{id}")]
        public async Task<ActionResult<string>> DeleteUser(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _userService.DeleteUserAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet]
        public async Task<ActionResult<string>> GetAllUser(string? role, int pageNumber = 1, int pageSize = 5)
        {
            var result = await _userService.GetAllUserAsync(pageNumber, pageSize, role);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("{id}/admin")]
        public async Task<ActionResult<string>> UpdateUser(string id, UpdateUserModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _userService.UpdateUserForAdminAsync(id, model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPut("update-profile")]
        public async Task<ActionResult<UpdateUserResponse>> UpdateUserInformation(UpdateInformationModel model)
        {
            var userId = User.FindFirst("userId")?.Value;
            UpdateUserResponse result = await _userService.UpdateUserAsync(model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("ranking")]
        public async Task<ActionResult<string>> GetRankDonate(int pageNumber = 1, int pageSize = 10)
        {
            var result = await _userService.GetRankDonate(pageNumber, pageSize);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("ranking/{id}")]
        public async Task<ActionResult<UserModelView>> GetDetailRankerById(string id)
        {
            var result = await _userService.GetDetailRankerByIdAsync(id);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("change-password")]
        public async Task<ActionResult<string>> ChangePassword(ChangePasswordModel model)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _userService.ChangePasswordAsync(model, userId);

            return Ok(result);
        }

        
        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("request-update-email")]
        public async Task<ActionResult<string>> RequestUpdateEmail()
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _userService.RequestUpdateEmail(userId);

            return Ok(result);
        }

        [HttpPatch("verify-otp-email")]
        public async Task<ActionResult<string>> VerifyOtpEmail(VerifyOtpModel model)
        {
            var result = await _userService.VerifyOtpEmailAsync(model);

            return Ok(result);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("update-email")]
        public async Task<ActionResult<string>> UpdateEmail(UpdateEmailRequest model)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _userService.UpdateEmailAsync(userId, model);

            return Ok(result);
        }
    }
}
