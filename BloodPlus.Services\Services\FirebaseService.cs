﻿using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.AspNetCore.Http;

public class FirebaseService
{
    private readonly StorageClient _storageClient;
    private readonly string _bucketName;

    public FirebaseService()
    {
        var credentialPath = Path.Combine(AppContext.BaseDirectory, "BloodPlus_Firebase.json");

        // Load Google credential từ file JSON
        GoogleCredential credential;
        using (var stream = File.OpenRead(credentialPath))
        {
            credential = GoogleCredential.FromStream(stream);
        }

        // Tạo StorageClient với credential đã load
        _storageClient = StorageClient.Create(credential);

        // Lấy bucket name từ file JSON
        var json = File.ReadAllText(credentialPath);
        var serviceAccount = System.Text.Json.JsonDocument.Parse(json);
        _bucketName = serviceAccount.RootElement.GetProperty("storage_bucket").GetString()
            ?? throw new Exception("Missing 'storage_bucket' in Firebase JSON file.");
    }

    public async Task<string> UploadImageAsync(IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new ArgumentException("File upload is empty or null.");

        var extension = Path.GetExtension(file.FileName);
        var fileName = $"images/{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid()}{extension}";

        try
        {
            using var stream = file.OpenReadStream();

            var obj = await _storageClient.UploadObjectAsync(_bucketName, fileName, file.ContentType, stream);

            // Bạn có thể vẫn giữ PublicRead nếu bucket = GCS thuần
            await _storageClient.UpdateObjectAsync(obj, new UpdateObjectOptions
            {
                PredefinedAcl = PredefinedObjectAcl.PublicRead
            });

            // ✅ Trả về link đúng Firebase REST API
            return $"https://firebasestorage.googleapis.com/v0/b/{_bucketName}/o/{Uri.EscapeDataString(fileName)}?alt=media";
        }
        catch (Exception ex)
        {
            throw new Exception("Lỗi khi upload ảnh lên Firebase Storage", ex);
        }
    }

}
