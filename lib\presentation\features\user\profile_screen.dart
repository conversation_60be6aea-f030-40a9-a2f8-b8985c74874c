import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button_navBar.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'dart:math';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with TickerProviderStateMixin {
  bool _isQrEnlarged = false;
  double _originalBrightness = 0.5;
  UserModel? _user;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );
    _storeCurrentBrightness();
    _loadUserInfo();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AppStateNotifier>(context, listen: false).fetchUserProfile(forceRefresh: true);
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _storeCurrentBrightness() async {
    try {
      final currentBrightness = await ScreenBrightness().current;
      setState(() {
        _originalBrightness = currentBrightness;
      });
    } catch (e) {
      debugPrint('Error getting current brightness: $e');
    }
  }

  Future<void> _loadUserInfo() async {
    final userManager = UserManager();
    final userId = await userManager.getUserId();
    if (userId != null) {
      final user = await userManager.getUserInfo(userId);
      if (user != null) {
        setState(() {
          _user = user;
        });
        _fadeController.forward();
      }
    }
  }

  Future<void> _setMaxBrightness() async {
    try {
      await ScreenBrightness().setScreenBrightness(1.0);
    } catch (e) {
      debugPrint('Error setting max brightness: $e');
    }
  }

  Future<void> _restoreBrightness() async {
    try {
      await ScreenBrightness().setScreenBrightness(_originalBrightness);
    } catch (e) {
      debugPrint('Error restoring brightness: $e');
    }
  }

  String _generateQrData(UserModel? user) {
    if (user == null) {
      return 'No user data available';
    }
    final qrData = '''
ID: ${user.id ?? 'N/A'}
Name: ${user.name ?? 'N/A'}
Address: ${user.address ?? 'N/A'}
DOB: ${user.dateOfBirth?.toString().split(' ')[0] ?? 'N/A'}
Passport: ${user.passportNumber ?? 'N/A'}
Donations: ${user.donationCount?.toString() ?? '0'}
Points: ${user.point.toString()}
Email: ${user.email ?? 'N/A'}
Blood Type: ${user.bloodType ?? 'N/A'}
Phone: ${user.phoneNumber ?? 'N/A'}
'''.trim();
    return qrData;
  }

  void _onItemTapped(int index) {
    if (index == 0) {
      NavigationService.navigateToHome();
    } else if (index == 1) {
      // NavigationService.navigateTo(AppRoutes.daysWaiting);
    } else if (index == 2) {
      NavigationService.navigateTo(AppRoutes.donationEvent);
    } else if (index == 3) {
      NavigationService.navigateTo(AppRoutes.donationHistory);
    } else if (index == 4) {
      // Already on ProfileScreen
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing based on screen width
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final qrIconSize = isSmallScreen ? 28.0 : (isLargeScreen ? 38.0 : 33.0);
    final qrPadding = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final qrSize = min(screenSize.width * 0.6, isSmallScreen ? 160.0 : (isLargeScreen ? 240.0 : 200.0));
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
        title: Text(
          localizations.translate('profile'),
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: titleFontSize,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
            size: iconSize,
          ),
          onPressed: () => NavigationService.goBack(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.qr_code,
              color: Colors.white,
              size: qrIconSize,
            ),
            onPressed: () {
              setState(() {
                _isQrEnlarged = true;
              });
              _setMaxBrightness();
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          FadeTransition(
            opacity: _fadeAnimation,
            child: _user == null
                ? _buildLoadingWidget(localizations, isSmallScreen, isLargeScreen)
                : SingleChildScrollView(
              child: Column(
                children: [
                  _buildProfileHeader(context, isSmallScreen, isLargeScreen),
                  _buildMenuSection(context, isSmallScreen, isLargeScreen),
                ],
              ),
            ),
          ),
          if (_isQrEnlarged)
            GestureDetector(
              onTap: () {
                setState(() {
                  _isQrEnlarged = false;
                });
                _restoreBrightness();
              },
              child: Container(
                color: Colors.black87,
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(qrPadding),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(borderRadius),
                    ),
                    child: QrImageView(
                      data: _generateQrData(_user),
                      version: QrVersions.auto,
                      size: qrSize,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavBar(
        selectedIndex: 4,
        onItemTapped: _onItemTapped,
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final localizations = AppLocalizations.of(context);
    final appState = Provider.of<AppStateNotifier>(context);

    // Dynamic sizing
    final avatarSize = isSmallScreen ? 80.0 : (isLargeScreen ? 120.0 : 90.0);
    final nameFontSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final addressFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final locationIconSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final cardWidth = min(MediaQuery.of(context).size.width * 0.35, isSmallScreen ? 120.0 : (isLargeScreen ? 160.0 : 140.0));
    final spacing = isSmallScreen ? 6.0 : (isLargeScreen ? 12.0 : 8.0);
    final borderRadius = isSmallScreen ? 16.0 : 20.0;
    final padding = EdgeInsets.all(isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0));

    return Container(
      width: double.infinity,
      padding: padding,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(borderRadius)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: isSmallScreen ? 8 : 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Hero(
            tag: 'profile_image',
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: isSmallScreen ? 2 : 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: isSmallScreen ? 8 : 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipOval(
                child: FadeInImage(
                  placeholder: const AssetImage('assets/images/profile.jpg'),
                  image: _user?.userImage != null && _user!.userImage!.startsWith('http')
                      ? NetworkImage(_user!.userImage!)
                      : const AssetImage('assets/images/profile.jpg') as ImageProvider,
                  width: avatarSize,
                  height: avatarSize,
                  fit: BoxFit.cover,
                  imageErrorBuilder: (context, error, stackTrace) {
                    debugPrint('Image load error for ${_user?.userImage}: $error');
                    return Image.asset(
                      'assets/images/profile.jpg',
                      width: avatarSize,
                      height: avatarSize,
                      fit: BoxFit.cover,
                    );
                  },
                ),
              ),
            ),
          ),
          SizedBox(height: spacing * 2),
          Text(
            _user?.name ?? localizations.translate('loading'),
            style: AppTheme.headingLarge.copyWith(
              color: Colors.white,
              fontSize: nameFontSize,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: spacing),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.location_on,
                color: Colors.white70,
                size: locationIconSize,
              ),
              SizedBox(width: spacing / 2),
              Flexible(
                child: Text(
                  _user?.address ?? localizations.translate('loading'),
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white70,
                    fontSize: addressFontSize,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: spacing * 2),
          Wrap(
            alignment: WrapAlignment.center,
            spacing: spacing * 2,
            runSpacing: spacing,
            children: [
              _buildInfoCard(
                title: localizations.translate('blood_type'),
                value: _user?.bloodType ?? 'N/A',
                icon: Icons.bloodtype,
                color: Colors.red,
                isSmallScreen: isSmallScreen,
                isLargeScreen: isLargeScreen,
                cardWidth: cardWidth,
              ),
              _buildInfoCard(
                title: localizations.translate('donation_count'),
                value: appState.donationCount.toString(),
                icon: Icons.favorite,
                color: Colors.orange,
                isSmallScreen: isSmallScreen,
                isLargeScreen: isLargeScreen,
                cardWidth: cardWidth,
              ),
            ],
          ),
          SizedBox(height: spacing),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required bool isSmallScreen,
    required bool isLargeScreen,
    required double cardWidth,
  }) {
    // Dynamic sizing
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final titleFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final valueFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final padding = isSmallScreen ? 8.0 : 12.0;
    final spacing = isSmallScreen ? 4.0 : (isLargeScreen ? 10.0 : 8.0);

    return Container(
      width: cardWidth,
      padding: EdgeInsets.all(padding),
      decoration: AppTheme.cardDecoration,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(padding),
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [color.withOpacity(0.3), color.withOpacity(0.1)],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: iconSize),
          ),
          SizedBox(height: spacing),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
              fontSize: titleFontSize,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            value,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontSize: valueFontSize,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final localizations = AppLocalizations.of(context);

    // Dynamic sizing
    final padding = EdgeInsets.symmetric(
      horizontal: isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0),
      vertical: isSmallScreen ? 12.0 : 16.0,
    );
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final iconSize = isSmallScreen ? 24.0 : (isLargeScreen ? 32.0 : 28.0);
    final textFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final borderRadius = isSmallScreen ? 12.0 : 16.0;
    final spacing = isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);
    final cardPadding = EdgeInsets.symmetric(
      horizontal: isSmallScreen ? 12.0 : 16.0,
      vertical: isSmallScreen ? 6.0 : 8.0,
    );

    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.translate('options'),
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textPrimary,
              fontSize: titleFontSize,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: spacing),
          _buildMenuItem(
            context: context,
            icon: Icons.person_outline,
            title: localizations.translate('user_info'),
            onTap: () => NavigationService.navigateTo(AppRoutes.accountInfo),
            iconSize: iconSize,
            textFontSize: textFontSize,
            borderRadius: borderRadius,
            cardPadding: cardPadding,
            isSmallScreen: isSmallScreen,
            isLargeScreen: isLargeScreen,
          ),
          _buildMenuItem(
            context: context,
            icon: Icons.settings,
            title: localizations.translate('settings'),
            onTap: () => NavigationService.navigateTo(AppRoutes.settings),
            iconSize: iconSize,
            textFontSize: textFontSize,
            borderRadius: borderRadius,
            cardPadding: cardPadding,
            isSmallScreen: isSmallScreen,
            isLargeScreen: isLargeScreen,
          ),
          _buildMenuItem(
            context: context,
            icon: Icons.bookmark_border,
            title: localizations.translate('app_info'),
            onTap: () {
              DialogHelper.showAnimatedSuccessDialog(
                context: context,
                title: localizations.translate('app_info_title'),
                message: localizations.translate('app_info_message') ,
                buttonText: localizations.translate('close'),
                icon: Icons.info_outline,
                iconColor: AppTheme.primaryRed,
              );
            },
            iconSize: iconSize,
            textFontSize: textFontSize,
            borderRadius: borderRadius,
            cardPadding: cardPadding,
            isSmallScreen: isSmallScreen,
            isLargeScreen: isLargeScreen,
          ),
          _buildMenuItem(
            context: context,
            icon: Icons.history,
            title: localizations.translate('donation_history'),
            onTap: () => NavigationService.navigateTo(AppRoutes.donationHistory),
            iconSize: iconSize,
            textFontSize: textFontSize,
            borderRadius: borderRadius,
            cardPadding: cardPadding,
            isSmallScreen: isSmallScreen,
            isLargeScreen: isLargeScreen,
          ),
          _buildMenuItem(
            context: context,
            icon: Icons.logout,
            title: localizations.translate('logout'),
            onTap: () => _showLogoutConfirmationDialog(context),
            iconColor: Colors.red,
            iconSize: iconSize,
            textFontSize: textFontSize,
            borderRadius: borderRadius,
            cardPadding: cardPadding,
            isSmallScreen: isSmallScreen,
            isLargeScreen: isLargeScreen,
          ),
          _buildMenuItem(
            context: context,
            icon: Icons.help_outline,
            title: localizations.translate('support'),
            onTap: () {
              DialogHelper.showAnimatedSuccessDialog(
                context: context,
                title: localizations.translate('support_title'),
                message: '${localizations.translate('support_message')}\n\nEmail: <EMAIL>.\nPhone: 0337 252 208',
                buttonText: localizations.translate('close'),
                icon: Icons.help_outline,
                iconColor: AppTheme.primaryRed,
              );
            },
            iconSize: iconSize,
            textFontSize: textFontSize,
            borderRadius: borderRadius,
            cardPadding: cardPadding,
            isSmallScreen: isSmallScreen,
            isLargeScreen: isLargeScreen,
          ),
          SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    required double iconSize,
    required double textFontSize,
    required double borderRadius,
    required EdgeInsets cardPadding,
    required bool isSmallScreen,
    required bool isLargeScreen,
  }) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
      child: ListTile(
        leading: Icon(
          icon,
          size: iconSize,
          color: iconColor ?? AppTheme.primaryRed,
        ),
        title: Text(
          title,
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontSize: textFontSize,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Icon(
          Icons.chevron_right,
          size: iconSize,
          color: AppTheme.textSecondary,
        ),
        onTap: onTap,
        contentPadding: cardPadding,
        tileColor: AppTheme.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
      ),
    );
  }

  Widget _buildLoadingWidget(AppLocalizations localizations, bool isSmallScreen, bool isLargeScreen) {
    // Dynamic sizing
    final fontSize = isSmallScreen ? 16.0 : (isLargeScreen ? 20.0 : 18.0);
    final spacing = isSmallScreen ? 12.0 : 16.0;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
            strokeWidth: isSmallScreen ? 3 : 4,
          ),
          SizedBox(height: spacing),
          Text(
            localizations.translate('loading'),
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.textPrimary,
              fontSize: fontSize,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _showLogoutConfirmationDialog(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final userManager = UserManager();

    // Dynamic sizing for dialog
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    DialogHelper.showConfirmationDialog(
      context: context,
      title: localizations.translate('logout_confirm_title'),
      message: localizations.translate('logout_confirm_message'),
      cancelButtonText: localizations.translate('cancel'),
      confirmButtonText: localizations.translate('confirm'),
      onConfirm: () async {
        try {
          final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
          await userManager.clearUserData();
          appStateNotifier.resetState();
          appStateNotifier.setLoggedIn(false);
          if (context.mounted) {
            NavigationService.navigateToAndRemoveUntil(AppRoutes.login);
          }
        } catch (e) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '${localizations.translate('logout_failed')}: $e',
                  style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.all(isSmallScreen ? 12 : 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 10),
                ),
              ),
            );
          }
        }
      },
      icon: Icons.logout_rounded,
      iconColor: AppColors.primaryRed,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 350),
    );
  }
}