﻿// <auto-generated />
using System;
using BloodPlus.Repositories.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace BloodPlus.Repositories.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20250715144803_init")]
    partial class init
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true)
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Appointment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("BloodComponent")
                        .HasColumnType("int");

                    b.Property<string>("BloodType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Certification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DonationEventId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("IsEmergency")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DonationEventId");

                    b.HasIndex("UserId");

                    b.ToTable("Appointment");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Blog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Author")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("ViewNumber")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Blog");

                    b.HasData(
                        new
                        {
                            Id = "848cdecf-de27-4816-b6f5-03a50a0ed166",
                            Author = "Bo truong bo y te",
                            Content = "Ngày 8-5, tại TPHCM, Trung ương Hội Chữ thập đỏ Việt Nam và UBND TPHCM phối hợp tổ chức lễ phát động Tháng Nhân đạo cấp quốc gia năm 2025 với chủ đề “Hành trình nhân đạo - Lan tỏa yêu thương”.",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7214), new TimeSpan(0, 0, 0, 0, 0)),
                            Description = "Tham dự có các đồng chí: Trương Tấn Sang, nguyên Ủy viên Bộ Chính trị, nguyên Chủ tịch nước, nguyên Chủ tịch danh dự Hội Chữ thập đỏ Việt Nam; Đỗ Văn Chiến, Ủy viên Bộ Chính trị, Bí thư Trung ương Đảng, Chủ tịch Ủy ban Trung ương MTTQ Việt Nam; Nguyễn Phước Lộc, Phó Bí thư Thành ủy, Chủ tịch Ủy ban MTTQ Việt Nam TPHCM; Vũ Chiến Thắng, Thứ trưởng Bộ Nội vụ; Nguyễn Phạm Duy Trang, Bí thư Trung ương Đoàn, Chủ tịch Hội Đồng đội Trung ương; Nguyễn Mạnh Cường, Ủy viên Ban Thường vụ Thành ủy, Trưởng Ba",
                            Image1 = "https://giotmauvang.org.vn/assets/images/271b5fe5f864d480023593de2e8aaf3a.png",
                            Image2 = "https://giotmauvang.org.vn/assets/images/b9c617aa727d51010018f897eef31504.png",
                            Image3 = "https://giotmauvang.org.vn/assets/images/34708fa4b1dbe203ff87e16eeb077bed.png",
                            Image4 = "https://giotmauvang.org.vn/assets/images/79c1c457007a40d40997edf42ce709dd.png",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7203), new TimeSpan(0, 7, 0, 0, 0)),
                            Title = "Khởi động Tháng Nhân đạo năm 2025: Hành trình nhân đạo - Lan tỏa yêu thương",
                            UserId = new Guid("89de8847-9d95-4916-81d0-e06c5321d2c4"),
                            ViewNumber = 10
                        });
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.BloodType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BloodName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("BloodType");

                    b.HasData(
                        new
                        {
                            Id = "241b0816-4573-48a4-bc4a-3d0ee9988d54",
                            BloodName = "A+",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9844), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9840), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "d156e445-05a9-4409-9125-9a49e22d472d",
                            BloodName = "A-",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9849), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9846), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "061e9731-9b10-41af-ac23-29f510a1ba98",
                            BloodName = "B+",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9852), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9851), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "b1c6baa5-a417-45de-b3a9-0b957041b224",
                            BloodName = "AB-",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9856), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9854), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "73f75e24-391b-4ea5-8da4-cb40d805d1f5",
                            BloodName = "O+",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9861), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9857), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "62af53be-4e3c-4287-9f2a-4b4d127828f1",
                            BloodName = "O-",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9864), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9863), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "d9ee45ae-5c0e-44be-870c-46b804032e55",
                            BloodName = "B-",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9866), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9866), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            Id = "0f529bee-6a9b-412e-87a6-53b11c8d9c77",
                            BloodName = "AB+",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9869), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9868), new TimeSpan(0, 7, 0, 0, 0))
                        });
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.ChatMessage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ConversationId")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetadataJson")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId");

                    b.HasIndex("Timestamp");

                    b.HasIndex("UserId");

                    b.ToTable("ChatMessages");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.DonationEvent", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("CurrentDonors")
                        .HasColumnType("int");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EventDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEmergency")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RequiredBloodType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RequiredDonors")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("DonationEvent");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.LocationIQResult", b =>
                {
                    b.Property<int>("LocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LocationId"));

                    b.Property<string>("Lat")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LocationName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Lon")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("LocationId");

                    b.ToTable("LocationIQResults");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Notification", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("SentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Notification");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Organization", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Organization");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Otp", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OtpNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Otps");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.RegistrationForm", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AntibioticsDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AppointmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DiseaseDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HadRecentIllness12Months")
                        .HasColumnType("bit");

                    b.Property<bool>("HadRecentIllness14Days")
                        .HasColumnType("bit");

                    b.Property<bool>("HadRecentIllness1Month")
                        .HasColumnType("bit");

                    b.Property<bool>("HadRecentIllness6Months")
                        .HasColumnType("bit");

                    b.Property<bool>("HasDiseases")
                        .HasColumnType("bit");

                    b.Property<bool>("HasDonatedBefore")
                        .HasColumnType("bit");

                    b.Property<bool>("HasPreviousInfections")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPregnantOrRecentMother")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTakingMedicine")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicineDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PregnancyDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PreviousInfectionsDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecentIllness12MonthsDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecentIllness14DaysDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskBehavior")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Symptoms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TattooOrSurgery")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TravelHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("UsedAntibiotics7Days")
                        .HasColumnType("bit");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("WeightOver45kg")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("AppointmentId");

                    b.HasIndex("UserId");

                    b.ToTable("RegistrationForm");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Role");

                    b.HasData(
                        new
                        {
                            Id = new Guid("85cf1130-ba32-4590-ab32-8cde6b49860e"),
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9554), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9554), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Admin",
                            NormalizedName = "ADMIN"
                        },
                        new
                        {
                            Id = new Guid("4af18b70-045a-4b96-b8e0-1ad8d00bd611"),
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9556), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9557), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Manager",
                            NormalizedName = "MANAGER"
                        },
                        new
                        {
                            Id = new Guid("23938420-3efc-41e1-9567-b85e9a883e00"),
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9559), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 665, DateTimeKind.Unspecified).AddTicks(9560), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "User",
                            NormalizedName = "USER"
                        });
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BloodTypeId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeviceToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DonationCount")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<int?>("Gender")
                        .HasColumnType("int");

                    b.Property<string>("Job")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PassportNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<int>("Point")
                        .HasColumnType("int");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserImage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BloodTypeId");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = new Guid("65f645fb-a980-4a71-bf36-296223d3b68a"),
                            AccessFailedCount = 0,
                            Address = "Thanh Vân Môn",
                            BloodTypeId = "241b0816-4573-48a4-bc4a-3d0ee9988d54",
                            ConcurrencyStamp = "84fa351f-ba7b-4223-88be-e159b19948bc",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7044), new TimeSpan(0, 0, 0, 0, 0)),
                            DateOfBirth = new DateTime(2003, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DonationCount = 0,
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            Gender = 1,
                            Job = "Admin",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(6995), new TimeSpan(0, 7, 0, 0, 0)),
                            LockoutEnabled = false,
                            Name = "Lục Tuyết Kỳ",
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "ADMIN",
                            PasswordHash = "AQAAAAIAAYagAAAAEDZLA3xOoY6q7uzzPd0QSbjC2vi6fbreBcItU9bZYJYggC/IpGuaVtM/JBFkaVUrmg==",
                            PhoneNumber = "0912213443",
                            PhoneNumberConfirmed = false,
                            Point = 0,
                            SecurityStamp = "SeedData",
                            TwoFactorEnabled = false,
                            UserName = "admin"
                        },
                        new
                        {
                            Id = new Guid("89de8847-9d95-4916-81d0-e06c5321d2c4"),
                            AccessFailedCount = 0,
                            Address = "Thanh Vân Môn",
                            BloodTypeId = "d9ee45ae-5c0e-44be-870c-46b804032e55",
                            ConcurrencyStamp = "f8f8d008-cdaf-4e49-8265-9300f01f9922",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7053), new TimeSpan(0, 0, 0, 0, 0)),
                            DateOfBirth = new DateTime(2003, 9, 18, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DonationCount = 0,
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            Gender = 0,
                            Job = "Manager",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7049), new TimeSpan(0, 7, 0, 0, 0)),
                            LockoutEnabled = false,
                            Name = "Trương Tiểu Phàm",
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "MANAGER",
                            PasswordHash = "AQAAAAIAAYagAAAAEOmajMx0Kq0ZunrgWS51JTLcc6g7hteWfaqD16Zjyut8dVlugnguz7Yc8Uuk6CMcqw==",
                            PhoneNumber = "0912332199",
                            PhoneNumberConfirmed = false,
                            Point = 0,
                            SecurityStamp = "SeedData",
                            TwoFactorEnabled = false,
                            UserName = "manager"
                        });
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRole");

                    b.HasData(
                        new
                        {
                            UserId = new Guid("65f645fb-a980-4a71-bf36-296223d3b68a"),
                            RoleId = new Guid("85cf1130-ba32-4590-ab32-8cde6b49860e"),
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7158), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7151), new TimeSpan(0, 7, 0, 0, 0))
                        },
                        new
                        {
                            UserId = new Guid("89de8847-9d95-4916-81d0-e06c5321d2c4"),
                            RoleId = new Guid("4af18b70-045a-4b96-b8e0-1ad8d00bd611"),
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 14, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7160), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 15, 21, 48, 1, 783, DateTimeKind.Unspecified).AddTicks(7159), new TimeSpan(0, 7, 0, 0, 0))
                        });
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.UserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserToken");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Voucher", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Panel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Point")
                        .HasColumnType("int");

                    b.Property<string>("SponsorImage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SponsorName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Support")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserManual")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VoucherImage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VoucherName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Voucher");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Appointment", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.DonationEvent", "DonationEvent")
                        .WithMany("Appointments")
                        .HasForeignKey("DonationEventId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("Appointments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DonationEvent");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Blog", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("Blogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.ChatMessage", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("ChatMessages")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.DonationEvent", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.Organization", "Organization")
                        .WithMany("DonationEvents")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Notification", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("Notifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Organization", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("Organizations")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.RegistrationForm", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.Appointment", "Appointment")
                        .WithMany("RegistrationForms")
                        .HasForeignKey("AppointmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("RegistrationForms")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Appointment");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.User", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.BloodType", "BloodType")
                        .WithMany("Users")
                        .HasForeignKey("BloodTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("BloodType");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.UserRole", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Voucher", b =>
                {
                    b.HasOne("BloodPlus.Contract.Repositories.Entity.User", "User")
                        .WithMany("Vouchers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("User");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Appointment", b =>
                {
                    b.Navigation("RegistrationForms");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.BloodType", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.DonationEvent", b =>
                {
                    b.Navigation("Appointments");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Organization", b =>
                {
                    b.Navigation("DonationEvents");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.Role", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("BloodPlus.Contract.Repositories.Entity.User", b =>
                {
                    b.Navigation("Appointments");

                    b.Navigation("Blogs");

                    b.Navigation("ChatMessages");

                    b.Navigation("Notifications");

                    b.Navigation("Organizations");

                    b.Navigation("RegistrationForms");

                    b.Navigation("UserRoles");

                    b.Navigation("Vouchers");
                });
#pragma warning restore 612, 618
        }
    }
}
