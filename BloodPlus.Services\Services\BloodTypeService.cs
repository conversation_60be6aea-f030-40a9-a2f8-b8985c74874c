﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Utils;
using BloodPlus.ModelViews.BloodTypeModelViews;
using BloodPlus.Services.Interfaces;

namespace BloodPlus.Services.Services
{
    public class BloodTypeService : IBloodTypeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public BloodTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task<List<BloodTypeModelView>> GetAllBloodType()
        {
            var data = await _unitOfWork.GetRepository<BloodType>().GetAllAsync();

            return data.Select(bt => new BloodTypeModelView{
                            BloodTypeId = bt.Id,
                            BloodTypeName = bt.BloodName
                            }).ToList();
        }
    }
}
