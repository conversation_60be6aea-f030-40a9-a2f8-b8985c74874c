import 'package:bloodplusmobile/data/models/voucher_model.dart';

class VoucherResponse {
  final List<Voucher> items;
  final int totalItems;
  final int currentPage;
  final int totalPages;
  final int pageSize;
  final bool hasPreviousPage;
  final bool hasNextPage;

  VoucherResponse({
    required this.items,
    required this.totalItems,
    required this.currentPage,
    required this.totalPages,
    required this.pageSize,
    required this.hasPreviousPage,
    required this.hasNextPage,
  });

  factory VoucherResponse.fromJson(Map<String, dynamic> json) {
    var list = json['Items'] as List;
    List<Voucher> itemsList = list.map((i) => Voucher.fromJson(i)).toList();

    return VoucherResponse(
      items: itemsList,
      totalItems: json['TotalItems'],
      currentPage: json['CurrentPage'],
      totalPages: json['TotalPages'],
      pageSize: json['PageSize'],
      hasPreviousPage: json['HasPreviousPage'],
      hasNextPage: json['HasNextPage'],
    );
  }
} 