﻿using BloodPlus.Core.Enum;

namespace BloodPlus.ModelViews.VoucherModelViews
{
    public class UpdateVoucherModelView
    {
        public string? UserName { get; set; }
        public string? VoucherName { get; set; }          // Tên voucher
        public VoucherType? Type { get; set; }            // Loại voucher (enum)
        public string? SponsorName { get; set; }          // Tên nhà tài trợ
        public DateTime? ExpiryDate { get; set; }         // Ngày hết hạn
        public string? Description { get; set; }          // Mô tả
        public string? UserManual { get; set; }           // Hướng dẫn sử dụng
        public string? Support { get; set; }              // Thông tin hỗ trợ
        public int? Point { get; set; }
    }
}
