﻿using BloodPlus.Core.Enum;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace BloodPlus.ModelViews.AuthModelViews
{
    public class CreateAccountModelView
    {

        [Required]
        [StringLength(50)]
        public string UserName { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "Mật khẩu xác nhận không khớp.")]
        public string VerifyPassword { get; set; }

        [Required]
        public string Email { get; set; }

        
        [StringLength(10)]
        [RegularExpression(@"^\d+$", ErrorMessage = "Phone number must contain only digits.")]
        public string? PhoneNumber { get; set; }

        [Required]
        public string BloodType { get; set; }

        [Required]
        public string? Name { get; set; }

        [Required]
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? Job { get; set; }

        [Required]
        public Gender? Gender { get; set; }

        public string? PassportNumber { get; set; }

        public IFormFile? UserImage { get; set; }
    }
}
