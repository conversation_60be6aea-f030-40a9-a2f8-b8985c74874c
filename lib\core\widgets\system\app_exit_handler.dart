import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bloodplusmobile/core/widgets/dialog/exit_app_dialog.dart';

class AppExitHandler extends StatelessWidget {
  final Widget child;
  final bool enableExitDialog;

  const AppExitHandler({
    Key? key,
    required this.child,
    this.enableExitDialog = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!enableExitDialog) {
      return child;
    }

    return WillPopScope(
      onWillPop: () async {
        // Show exit confirmation dialog
        final shouldExit = await ExitAppDialog.showExitDialog(context);
        if (shouldExit) {
          // Exit the application
          SystemNavigator.pop();
        }
        return false; // Prevent default back navigation
      },
      child: child,
    );
  }
}