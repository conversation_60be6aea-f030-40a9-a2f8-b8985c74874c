    import 'package:bloodplusmobile/core/constants/app_colors.dart';
    import 'package:bloodplusmobile/core/routes/navigation_service.dart';
    import 'package:bloodplusmobile/core/routes/app_routes.dart';
    import 'package:bloodplusmobile/core/widgets/system/app_exit_handler.dart';
    import 'package:bloodplusmobile/core/widgets/system/custom_button_navBar.dart';
    import 'package:bloodplusmobile/core/widgets/system/feature_grid.dart';
    import 'package:bloodplusmobile/core/widgets/system/header_section.dart';
    import 'package:bloodplusmobile/core/widgets/system/news_carousel.dart';
    import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
    import 'package:bloodplusmobile/data/manager/notification_manager.dart';
    import 'package:flutter/material.dart';
    import 'package:flutter/services.dart';
    import 'package:provider/provider.dart';

    class HomeScreen extends StatefulWidget {
      const HomeScreen({Key? key}) : super(key: key);

      @override
      State<HomeScreen> createState() => _HomeScreenState();
    }

    class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
      int _selectedIndex = 0;
      late AnimationController _fadeController;
      late Animation<double> _fadeAnimation;

      final List<Map<String, dynamic>> features = [
        {'icon': 'assets/icons/schedule.png', 'title': 'schedule_donation', 'color': Colors.red},
        {'icon': 'assets/icons/emergency.png', 'title': 'emergency_donation', 'color': Colors.red},
        {'icon': 'assets/icons/nearly_hospital.png', 'title': 'nearby_event', 'color': Colors.red},
        {'icon': 'assets/icons/news.png', 'title': 'blog_list', 'color': Colors.red},
        {'icon': 'assets/icons/doctor.png', 'title': 'expert_advice', 'color': Colors.red},
        {'icon': 'assets/icons/information.png', 'title': 'information', 'color': Colors.red},
        {'icon': 'assets/icons/leaderboard.png', 'title': 'leaderboard', 'color': Colors.red},
        {'icon': 'assets/icons/voucher.png', 'title': 'voucher', 'color': Colors.red},
        {'icon': 'assets/icons/shopping.png', 'title': 'shopping', 'color': Colors.red},
      ];

      @override
      void initState() {
        super.initState();
        _fadeController = AnimationController(
          duration: const Duration(milliseconds: 800),
          vsync: this,
        );
        _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
        );
        _fadeController.forward();

        WidgetsBinding.instance.addPostFrameCallback((_) {
          final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
          appStateNotifier.fetchAllData(forceRefresh: true);
          appStateNotifier.setLoggedIn(true);

          // Bắt đầu auto sync notification
          final notificationManager = Provider.of<NotificationManager>(context, listen: false);
          notificationManager.startAutoSync();
        });
      }

      @override
      void dispose() {
        _fadeController.dispose();
        // Dừng auto sync notification
        final notificationManager = Provider.of<NotificationManager>(context, listen: false);
        notificationManager.stopAutoSync();
        super.dispose();
      }

      void _onItemTapped(int index) {
        setState(() => _selectedIndex = index);
        if (index == 4) {
          NavigationService.navigateTo(AppRoutes.profile);
        }
        if (index == 3) {
          NavigationService.navigateTo(AppRoutes.donationHistory);
        }
      }

      @override
      Widget build(BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final padding = screenWidth * 0.04; // 4% of screen width for horizontal padding

        return AppExitHandler(
          child: AnnotatedRegion<SystemUiOverlayStyle>(
            value: const SystemUiOverlayStyle(
              statusBarColor: AppColors.primaryRed, // hoặc Color(0xFFEB222A)
              statusBarIconBrightness: Brightness.light,
              statusBarBrightness: Brightness.dark,
            ),
            child: Scaffold(
              backgroundColor: const Color(0xFFFFFFFF),
              body: LayoutBuilder(
                builder: (context, constraints) {
                  final screenHeight = constraints.maxHeight;
                  final content = CustomScrollView(
                    physics: const ClampingScrollPhysics(),
                    slivers: [
                      SliverToBoxAdapter(child: HeaderSection()),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: padding),
                          child: Column(
                            children: [
                              SizedBox(height: screenWidth * 0.02),
                              const NewsCarousel(),
                              SizedBox(height: screenWidth * 0.04),
                              FeatureGrid(features: features),
                              SizedBox(height: screenWidth * 0.05),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                  // Đo chiều cao header + các phần còn lại
                  // Giả định header cao khoảng 0.38 * screenHeight (tuỳ thiết kế), có thể tinh chỉnh nếu cần
                  final estimatedContentHeight = screenWidth * 0.38 +
                      screenWidth * 0.02 +
                      200 + // NewsCarousel + FeatureGrid (ước lượng)
                      screenWidth * 0.04 +
                      screenWidth * 0.05;
                  if (estimatedContentHeight > screenHeight) {
                    return RefreshIndicator(
                      onRefresh: () async {
                        final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
                        await appStateNotifier.fetchAllData(forceRefresh: true);
                      },
                      color: AppColors.primaryRed,
                      child: content,
                    );
                  } else {
                    return content;
                  }
                },
              ),
              bottomNavigationBar: CustomBottomNavBar(
                selectedIndex: _selectedIndex,
                onItemTapped: _onItemTapped,
              ),
            ),
          ),
        );
      }
    }