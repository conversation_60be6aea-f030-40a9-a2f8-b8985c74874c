﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.AppointmentModelViews;
using BloodPlus.Services.Interfaces;
using BloodPlus.Services.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AppointmentController : ControllerBase
    {
        private readonly IAppointmentService _appointmentService;

        public AppointmentController(IAppointmentService appointmentService)
        {
            _appointmentService = appointmentService;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost]
        public async Task<ActionResult<string>> CreateAppointment(CreateAppointmentModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _appointmentService.CreateAppointmentAsync(model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpDelete("{id}")]
        public async Task<ActionResult<string>> DeleteAppointment(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _appointmentService.DeleteAppointmentAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("markCancel-{id}")]
        public async Task<ActionResult<string>> MarkCancelAppointment(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _appointmentService.MarkCancelAppointmentAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("markCompleted-{id}")]
        public async Task<ActionResult<string>> MarkCompletedAppointment(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _appointmentService.MarkCompletedAppointmentAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("markNeedConfirm")]
        public async Task<ActionResult<string>> MarkNeedComfirmAppointment(MarkCompletedModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _appointmentService.MarkNeedConfirmAppointmentAsync(model, userId);

            return Ok(new { Message = result });
        }


        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<AppointmentModelView>> GetAppointmentById(string id)
        {
            var result = await _appointmentService.GetAppointmentByIdAsync(id);

            return Ok(new { Message = result });
        }


        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<ActionResult<BasePaginatedList<ListAppointmentForUser>>> GetAllAppointment(int pageNumber = 1, int pageSize = 5)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _appointmentService.GetAllAppointmentAsync(pageNumber, pageSize, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("admin")]
        public async Task<ActionResult<BasePaginatedList<ListAppointmentForAdmin>>> GetAllAppointmentForAdmin(int pageNumber = 1, int pageSize = 5)
        {
            var result = await _appointmentService.GetAllAppointmentForAdminAsync(pageNumber, pageSize);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("emergency")]
        public async Task<ActionResult<string>> CreateEmergencyAppointmentAsync(EmergencyAppointmentCreateModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _appointmentService.CreateEmergencyAppointmentAsync(model, userId);

            return Ok(new { Message = result });
        }
    }
}
