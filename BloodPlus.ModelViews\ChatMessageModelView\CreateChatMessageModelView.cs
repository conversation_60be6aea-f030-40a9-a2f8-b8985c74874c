﻿namespace BloodPlus.ModelViews.ChatMessageModelView
{
    public class CreateChatMessageModelView
    {
        public Guid UserId { get; set; }
        public string Role { get; set; } = null!;
        public string Message { get; set; } = null!;
        public string? ConversationId { get; set; }
    }

    public class CreateChatMessageModelViewController
    {
        public string Message { get; set; } = null!;
        public string? ConversationId { get; set; }
    }

}
