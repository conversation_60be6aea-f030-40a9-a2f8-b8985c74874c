import 'dart:convert';
import 'dart:io';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/appointment_model.dart';
import 'package:bloodplusmobile/data/models/emergency_appointment_model.dart';
import 'package:bloodplusmobile/data/repositories/appointment_response.dart';
import 'package:http/http.dart' as http;

class AppointmentService {
  final UserManager _userManager = UserManager();

  // Tạo cuộc hẹn
  Future<void> createAppointment(Map<String, dynamic> payload) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedPost(
        ApiConfig.appointment,
        token: token,
        body: payload,
        headers: {'Accept': 'text/plain'},
      );

      if (response.statusCode == 200) {
        // Appointment created successfully
      } else {
        throw Exception('Tạo cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  // L<PERSON>y danh sách cuộc hẹn của người dùng
  Future<List<Appointment>> getAppointments({
    int pageNumber = 1,
    int pageSize = 5,
    bool forceRefresh = false,
  }) async {
    final token = (await _userManager.getUserToken()) ?? '';
    final queryParameters = {
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
    };

    try {
      print('=== GET APPOINTMENTS REQUEST ===');
      print('URL: ${ApiConfig.getFullUrl(ApiConfig.appointment)}');
      print('Token: ${token != null ? 'exists' : 'null'}');

      final headers = {
        if (forceRefresh) 'Cache-Control': 'no-cache',
      };

      final response = await ApiConfig.authenticatedGet(
        ApiConfig.appointment,
        token: token,
        queryParameters: queryParameters,
        headers: headers,
      );

      print('=== GET APPOINTMENTS RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final appointmentResponse = AppointmentResponse.fromJson(data);
        print('AppointmentService: Successfully loaded ${appointmentResponse.items.length} appointments');
        return appointmentResponse.items;
      } else {
        throw Exception('Lấy danh sách cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('AppointmentService error: $e');
      throw Exception('Lỗi kết nối: $e');
    }
  }

  // Lấy cuộc hẹn theo ID
  Future<Appointment> getAppointmentById(String appointmentId) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      print('=== GET APPOINTMENT BY ID REQUEST ===');
      print('URL: ${ApiConfig.getFullUrl("${ApiConfig.appointment}/$appointmentId")}');
      print('Token: ${token != null ? 'exists' : 'null'}');

      final response = await ApiConfig.authenticatedGet(
        '${ApiConfig.appointment}/$appointmentId',
        token: token,
      );

      print('=== GET APPOINTMENT BY ID RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        // API trả về data trong key "Message"
        final message = data['Message'] as Map<String, dynamic>? ?? {};
        final appointment = Appointment.fromJson(message);
        print('AppointmentService: Successfully loaded appointment with ID: ${appointment.id}');
        return appointment;
      } else {
        throw Exception('Lấy cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('AppointmentService error: $e');
      throw Exception('Lỗi kết nối: $e');
    }
  }

  // Hủy cuộc hẹn
  Future<void> cancelAppointment(String appointmentId) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedPatch(
        '${ApiConfig.appointment}/markcancel-$appointmentId',
        token: token,
        headers: {'Accept': 'text/plain'},
      );

      if (response.statusCode == 200) {
        // Appointment canceled successfully
      } else {
        throw Exception('Failed to cancel appointment: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<void> MarkcompleteAppointment(String appointmentId, {File? certificationImage}) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      Map<String, String> fields = {'id': appointmentId};
      Map<String, http.MultipartFile>? files;

      // Add certification image if provided
      if (certificationImage != null) {
        files = {
          'Certification': await http.MultipartFile.fromPath(
            'Certification',
            certificationImage.path,
          ),
        };
      }

      final response = await ApiConfig.multipartPatch(
        '${ApiConfig.appointment}/markneedconfirm',
        token: token,
        fields: fields,
        files: files,
      );

      if (response.statusCode == 200) {
        // Appointment completed successfully
      } else {
        throw Exception('Hoàn thành cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<bool> createEmergencyAppointment(EmergencyAppointmentRequest request) async {
    final token = await _userManager.getUserToken();

    try {
      final response = await ApiConfig.authenticatedPost(
        '${ApiConfig.appointment}/emergency',
        token: token ?? '',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/plain',
        },
        body: request.toJson(),
      );
      print("$response.statusCode");

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Tạo lịch hẹn khẩn cấp thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối khi tạo cuộc hẹn khẩn cấp: $e');
    }
  }
}