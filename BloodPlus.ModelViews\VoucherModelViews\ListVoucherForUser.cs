﻿using BloodPlus.Core.Enum;

namespace BloodPlus.ModelViews.VoucherModelViews
{
    public class ListVoucherForUser
    {
        public string Id { get; set; }
        public string VoucherName { get; set; }          // Tên voucher
        public VoucherType Type { get; set; }            // Loại voucher (enum)
        public string SponsorName { get; set; }          // Tên nhà tài trợ
        public DateTime ExpiryDate { get; set; }
        public string? SponsorImage { get; set; }
        public int Point { get; set; }
        public string? Description { get; set; }
        public string? Panel { get; set; }
    }
}
