﻿
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.ModelViews.LocationModelViews;
using BloodPlus.ModelViews.LocationViewModel;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Text.Json;

namespace BloodPlus.Services.Services
{
    public class LocationIQGeolocationService : IGeolocationService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IGenericRepository<LocationIQResult> _locationIQResult;

        public LocationIQGeolocationService(HttpClient httpClient, IConfiguration config, IUnitOfWork unitOfWork)
        {
            _httpClient = httpClient;
            _apiKey = config["LocationIQ:ApiKey"];
            _unitOfWork = unitOfWork;
            _locationIQResult = _unitOfWork.GetRepository<LocationIQResult>(); 
        }


        public async Task<LocationIQResultRPModelView?> GetCoordinatesFromAddressAsync(string address)
        {
            var cached = await _locationIQResult.Entities
                                                .FirstOrDefaultAsync(x => x.LocationName.ToLower() == address.ToLower());

            if (cached != null)
            {
                return new LocationIQResultRPModelView
                {
                    lat = double.Parse(cached.Lat),
                    lng = double.Parse(cached.Lon)
                };
            }

            var encoded = Uri.EscapeDataString(address);
            var url = $"https://us1.locationiq.com/v1/search.php?key={_apiKey}&q={encoded}&format=json&limit=1";

            var response = await _httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
                return null;

            var content = await response.Content.ReadAsStringAsync();
            var results = JsonSerializer.Deserialize<List<LocationIQResultModelView>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (results == null || results.Count == 0)
                return null;

            var location = new LocationIQResult
            {
                LocationName = address,
                Lat = results[0].Lat.ToString(),
                Lon = results[0].Lon.ToString(),
            };
            await _locationIQResult.InsertAsync(location);
            await _unitOfWork.SaveAsync(); 

            return new LocationIQResultRPModelView
            {
                lat = double.Parse(results[0].Lat),
                lng = double.Parse(results[0].Lon)
            };
        }

        public async Task<bool> IsUserNearEventAsync(string userLocation, string eventLocation, double maxDistanceKm = 50)
        {
            var userCoord = await GetCoordinatesFromAddressAsync(userLocation);
            var eventCoord = await GetCoordinatesFromAddressAsync(eventLocation);

            if (userCoord == null || eventCoord == null)
                return false;

            var distance = CalculateDistanceKm(
                userCoord.lat, userCoord.lng,
                eventCoord.lat, eventCoord.lng);

            return distance <= maxDistanceKm;
        }

        private double CalculateDistanceKm(double lat1, double lon1, double lat2, double lon2)
        {
            const double R = 6371;
            var dLat = (lat2 - lat1) * Math.PI / 180;
            var dLon = (lon2 - lon1) * Math.PI / 180;
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return R * c;
        }

    }
}
