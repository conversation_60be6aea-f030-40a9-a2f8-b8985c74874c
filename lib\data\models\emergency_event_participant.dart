class EmergencyEventParticipant {
  final String? userName;
  final String? phoneNumber;
  final String? email;
  final String? address;
  final String? bloodType;
  final int bloodComponent;
  final int status;

  EmergencyEventParticipant({
    this.userName,
    this.phoneNumber,
    this.email,
    this.address,
    this.bloodType,
    required this.bloodComponent,
    required this.status,
  });

  factory EmergencyEventParticipant.fromJson(Map<String, dynamic> json) {
    return EmergencyEventParticipant(
      userName: json['UserName'],
      phoneNumber: json['PhoneNumber'],
      email: json['Email'],
      address: json['Address'],
      bloodType: json['BloodType'],
      bloodComponent: json['BloodComponent'],
      status: json['Status'],
    );
  }
}
