﻿using BloodPlus.Contract.Repositories.Entity;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Repositories.Context
{
    public class DatabaseContext : IdentityDbContext<User, Role, Guid, UserClaim, UserRole, UserLogin, RoleClaim, UserToken>
    {
        public DatabaseContext(DbContextOptions<DatabaseContext> options) : base(options) { }

        public virtual DbSet<User> ApplicationUsers => Set<User>();
        public virtual DbSet<Role> ApplicationRoles => Set<Role>();
        public virtual DbSet<UserClaim> ApplicationUserClaims => Set<UserClaim>();
        public virtual DbSet<UserRole> ApplicationUserRoles => Set<UserRole>();
        public virtual DbSet<UserLogin> ApplicationUserLogins => Set<UserLogin>();
        public virtual DbSet<RoleClaim> ApplicationRoleClaims => Set<RoleClaim>();
        public virtual DbSet<UserToken> ApplicationUserTokens => Set<UserToken>();
        public virtual DbSet<Appointment> Appointment { get; set; }
        public virtual DbSet<DonationEvent> DonationEvent { get; set; }
        public virtual DbSet<Blog> Blog { get; set; }
        public virtual DbSet<Notification> Notification { get; set; }
        public virtual DbSet<Organization> Organization { get; set; }
        public virtual DbSet<RegistrationForm> RegistrationForm { get; set; }
        public virtual DbSet<Voucher> Voucher { get; set; }
        public virtual DbSet<Otp> Otps { get; set; }
        public virtual DbSet<LocationIQResult> LocationIQResults { get; set; }
        public virtual DbSet<ChatMessage> ChatMessages { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Ignore<UserClaim>();
            modelBuilder.Ignore<RoleClaim>();
            modelBuilder.Ignore<UserLogin>();

            modelBuilder.Entity<UserRole>(userRole =>
            {
                userRole.HasKey(ur => new { ur.UserId, ur.RoleId });
                userRole.HasOne(ur => ur.User)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(ur => ur.UserId)
                    .IsRequired();
                userRole.HasOne(ur => ur.Role)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(ur => ur.RoleId)
                    .IsRequired();

            });

            modelBuilder.Entity<UserToken>()
                .HasKey(token => new { token.UserId, token.LoginProvider, token.Name });

            // blog - User (N-1)
            modelBuilder.Entity<Blog>()
                .HasOne(w => w.User)
                .WithMany(u => u.Blogs)
                .HasForeignKey(w => w.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Voucher - User (N-1)
            modelBuilder.Entity<Voucher>()
                .HasOne(w => w.User)
                .WithMany(u => u.Vouchers)
                .HasForeignKey(w => w.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Organization - User (N-1)
            modelBuilder.Entity<Organization>()
                .HasOne(w => w.User)
                .WithMany(ai => ai.Organizations)
                .HasForeignKey(w => w.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // DonationEvent - Organization (N-1)
            modelBuilder.Entity<DonationEvent>()
                .HasOne(ai => ai.Organization)
                .WithMany(ay => ay.DonationEvents)
                .HasForeignKey(ai => ai.OrganizationId)
                .OnDelete(DeleteBehavior.Restrict);

            // Appointment - DonationEvent (N-1)
            modelBuilder.Entity<Appointment>()
                .HasOne(im => im.DonationEvent)
                .WithMany(ai => ai.Appointments)
                .HasForeignKey(im => im.DonationEventId)
                .OnDelete(DeleteBehavior.Restrict);

            // Appointment - User (N-1)
            modelBuilder.Entity<Appointment>()
                .HasOne(im => im.User)
                .WithMany(ai => ai.Appointments)
                .HasForeignKey(im => im.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // RegisForm - User (N-1)
            modelBuilder.Entity<RegistrationForm>()
                .HasOne(im => im.User)
                .WithMany(ai => ai.RegistrationForms)
                .HasForeignKey(im => im.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // RegisForm - Appointment (N-1)
            modelBuilder.Entity<RegistrationForm>()
                .HasOne(im => im.Appointment)
                .WithMany(ai => ai.RegistrationForms)
                .HasForeignKey(im => im.AppointmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Noti - User (N-1)
            modelBuilder.Entity<Notification>()
                .HasOne(im => im.User)
                .WithMany(ai => ai.Notifications)
                .HasForeignKey(im => im.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // BloodType - User (N-1)
            modelBuilder.Entity<User>()
                .HasOne(im => im.BloodType)
                .WithMany(ai => ai.Users)
                .HasForeignKey(im => im.BloodTypeId)
                .OnDelete(DeleteBehavior.Restrict);


            // ChatMessage - User (N-1)
            modelBuilder.Entity<ChatMessage>()
                .HasOne(cm => cm.User)
                .WithMany(u => u.ChatMessages)
                .HasForeignKey(cm => cm.UserId)
                .OnDelete(DeleteBehavior.Restrict); // Tránh xóa user làm mất dữ liệu chat

            modelBuilder.Entity<ChatMessage>()
                .Property(cm => cm.Role)
                .HasConversion<string>(); // Enum -> string ("user", "ai")

            modelBuilder.Entity<ChatMessage>()
                .HasIndex(cm => cm.UserId);

            modelBuilder.Entity<ChatMessage>()
                .HasIndex(cm => cm.Timestamp);

            modelBuilder.Entity<ChatMessage>()
                .HasIndex(cm => cm.ConversationId);

            //===================================================Seed data================================================================

            //role
            var roleIdAdmin = Guid.NewGuid();
            var roleIdUser = Guid.NewGuid();
            var roleIdManager = Guid.NewGuid();
            modelBuilder.Entity<Role>().HasData(
                new Role
                {
                    Id = roleIdAdmin,
                    Name = "Admin",
                    NormalizedName = "ADMIN",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                    LastUpdatedTime = DateTimeOffset.UtcNow
                },
                new Role
                {
                    Id = roleIdManager,
                    Name = "Manager",
                    NormalizedName = "MANAGER",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                    LastUpdatedTime = DateTimeOffset.UtcNow
                },
                new Role
                {
                    Id = roleIdUser,
                    Name = "User",
                    NormalizedName = "USER",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                    LastUpdatedTime = DateTimeOffset.UtcNow
                });

            // blood type
            var bloodTypeId1 = Guid.NewGuid().ToString();
            var bloodTypeId2 = Guid.NewGuid().ToString();
            var bloodTypeId3 = Guid.NewGuid().ToString();
            modelBuilder.Entity<BloodType>().HasData(
                new BloodType
                {
                    Id = bloodTypeId1,
                    BloodName = "A+",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = Guid.NewGuid().ToString(),
                    BloodName = "A-",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = Guid.NewGuid().ToString(),
                    BloodName = "B+",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = Guid.NewGuid().ToString(),
                    BloodName = "AB-",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = Guid.NewGuid().ToString(),
                    BloodName = "O+",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = Guid.NewGuid().ToString(),
                    BloodName = "O-",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = bloodTypeId2,
                    BloodName = "B-",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new BloodType
                {
                    Id = bloodTypeId3,
                    BloodName = "AB+",
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.UtcNow,
                });

            // user
            var adminId = Guid.NewGuid();
            var managerId = Guid.NewGuid();
            var adminUser = new User { Id = adminId };
            var managerUser = new User { Id = managerId };
            var passwordHasher = new PasswordHasher<User>();
            var adminPasswordHash = passwordHasher.HashPassword(adminUser, "thoaideptraibodoiqua");
            var managerPasswordHash = passwordHasher.HashPassword(managerUser, "kietdeptraibodoiqua");

            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = adminId,
                    BloodTypeId = bloodTypeId1,
                    Name = "Lục Tuyết Kỳ",
                    UserName = "admin",
                    DateOfBirth = new DateTime(2003, 2, 5),
                    Address = "Thanh Vân Môn",
                    Job = "Admin",
                    DonationCount = 0,
                    Gender = Core.Enum.Gender.Female,
                    PhoneNumber = "0912213443",
                    NormalizedUserName = "ADMIN",
                    Email = "<EMAIL>",
                    NormalizedEmail = "<EMAIL>",
                    Point = 0,
                    EmailConfirmed = true,
                    PasswordHash = adminPasswordHash,
                    SecurityStamp = "SeedData",
                    CreatedBy = "SeedData",
                    CreatedTime = DateTimeOffset.UtcNow,
                    UserImage = "https://firebasestorage.googleapis.com/v0/b/bloodplus-e8d34.firebasestorage.app/o/images%2Fluctuyetky.png?alt=media&token=bf680223-c75c-4d72-9065-8eb0b2bf85d9",
                },
                new User
                {
                    Id = managerId,
                    BloodTypeId = bloodTypeId2,
                    Name = "Trương Tiểu Phàm",
                    UserName = "manager",
                    DonationCount = 0,
                    DateOfBirth = new DateTime(2003, 9, 18),
                    Address = "Thanh Vân Môn",
                    Point = 0,
                    Job = "Manager",
                    Gender = Core.Enum.Gender.Male,
                    PhoneNumber = "0912332199",
                    NormalizedUserName = "MANAGER",
                    Email = "<EMAIL>",
                    NormalizedEmail = "<EMAIL>",
                    EmailConfirmed = true,
                    PasswordHash = managerPasswordHash,
                    SecurityStamp = "SeedData",
                    CreatedBy = "SeedData",
                    CreatedTime = DateTimeOffset.UtcNow,
                    UserImage = "https://firebasestorage.googleapis.com/v0/b/bloodplus-e8d34.firebasestorage.app/o/images%2Ftruongtieupham.jpg?alt=media&token=be7cf69a-9c25-4833-9691-8c17b3771389",
                });

            //user role
            modelBuilder.Entity<UserRole>().HasData(
                new UserRole
                {
                    UserId = adminId,
                    RoleId = roleIdAdmin,
                    CreatedBy = "SeedData",
                    CreatedTime = DateTimeOffset.UtcNow,
                },
                new UserRole
                {
                    UserId = managerId,
                    RoleId = roleIdManager,
                    CreatedBy = "SeedData",
                    CreatedTime = DateTimeOffset.UtcNow,
                });

            // Organization
            

            // Event

            // Appointment

            // Blog
            var blogId1 = Guid.NewGuid().ToString();
            var blogId2 = Guid.NewGuid().ToString();
            modelBuilder.Entity<Blog>().HasData(
                new Blog
                {
                    Id = blogId1,
                    UserId = managerId,
                    Title = "Khởi động Tháng Nhân đạo năm 2025: Hành trình nhân đạo - Lan tỏa yêu thương",
                    Content = "Ngày 8-5, tại TPHCM, Trung ương Hội Chữ thập đỏ Việt Nam và UBND TPHCM phối hợp tổ chức lễ phát động Tháng Nhân đạo cấp quốc gia năm 2025 với chủ đề “Hành trình nhân đạo - Lan tỏa yêu thương”.",
                    Author = "Bo truong bo y te",
                    Description = "Tham dự có các đồng chí: Trương Tấn Sang, nguyên Ủy viên Bộ Chính trị, nguyên Chủ tịch nước, nguyên Chủ tịch danh dự Hội Chữ thập đỏ Việt Nam; Đỗ Văn Chiến, Ủy viên Bộ Chính trị, Bí thư Trung ương Đảng, Chủ tịch Ủy ban Trung ương MTTQ Việt Nam; Nguyễn Phước Lộc, Phó Bí thư Thành ủy, Chủ tịch Ủy ban MTTQ Việt Nam TPHCM; Vũ Chiến Thắng, Thứ trưởng Bộ Nội vụ; Nguyễn Phạm Duy Trang, Bí thư Trung ương Đoàn, Chủ tịch Hội Đồng đội Trung ương; Nguyễn Mạnh Cường, Ủy viên Ban Thường vụ Thành ủy, Trưởng Ba",
                    Image1 = "https://giotmauvang.org.vn/assets/images/271b5fe5f864d480023593de2e8aaf3a.png",
                    Image2 = "https://giotmauvang.org.vn/assets/images/b9c617aa727d51010018f897eef31504.png",
                    Image3 = "https://giotmauvang.org.vn/assets/images/34708fa4b1dbe203ff87e16eeb077bed.png",
                    Image4 = "https://giotmauvang.org.vn/assets/images/79c1c457007a40d40997edf42ce709dd.png",
                    ViewNumber = 10,
                    CreatedBy = "SeedData",
                    CreatedTime = DateTimeOffset.UtcNow,
                });

            //regis form


            // Voucher
        }
    }
}