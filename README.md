# bloodplusmobile - T<PERSON><PERSON> liệu chi tiết

## 1. Tổng quan dự án

**bloodplusmobile** là ứng dụng di động phát triển bằng Flutter, hỗ trợ cộng đồng hiến máu. Ứng dụng cung cấp các tính năng:
- Đ<PERSON><PERSON> ký/đăng nhập tài khoản người dùng
- Quản lý lịch sử, sự kiện hiến máu
- C<PERSON><PERSON> nhật tin tức, blog về hiến máu
- Xem bảng xếp hạng người hiến máu
- Nhận voucher/quà tặng cho người hiến máu
- Tích hợp chatbot hỗ trợ tư vấn

**Đối tượng sử dụng:** Người dân có nhu cầu hiến máu, tổ chứ<PERSON>, bệ<PERSON> viện, cộng đồng quan tâm.

---

## 2. C<PERSON>u trúc thư mục

```
bloodplusmobile/
│
├── android/                # Mã nguồn Android native
├── ios/                    # Mã nguồn iOS native
├── assets/                 # Tài nguyên tĩnh: ảnh, icon, đa ngôn ngữ
│   ├── icons/
│   ├── images/
│   └── locales/            # File json đa ngôn ngữ
├── lib/                    # Mã nguồn chính Flutter
│   ├── core/               # Cấu hình, helper, widget, route
│   ├── data/               # Quản lý dữ liệu: models, services, repositories, manager
│   ├── presentation/       # Giao diện chia theo tính năng
│   └── main.dart           # Điểm khởi động ứng dụng
├── pubspec.yaml            # Khai báo dependencies, assets
└── README.md               # Tài liệu hướng dẫn
```

### Giải thích chi tiết:
- **lib/core/**: Cấu hình, helper, widget dùng chung, định nghĩa route
- **lib/data/**: Quản lý trạng thái, models, repositories, services
- **lib/presentation/features/**: UI chia theo tính năng (auth, blog, home, notification, onboarding, schedule, user, voucher)
- **assets/locales/**: Đa ngôn ngữ (vi, en, ja, ko, th, zh, es)

---

## 3. Luồng hoạt động chính

### a. Đăng nhập/Đăng ký
- Đăng ký tài khoản mới hoặc đăng nhập
- Hỗ trợ quên mật khẩu, xác thực OTP

### b. Trang chủ (Home)
- Hiển thị tổng quan, sự kiện, bảng xếp hạng, tin tức

### c. Sự kiện hiến máu (Schedule)
- Xem danh sách sự kiện, đăng ký tham gia, xem lịch sử

### d. Tin tức/Blog
- Cập nhật bài viết, tin tức liên quan

### e. Quản lý tài khoản
- Xem, cập nhật thông tin cá nhân, đổi email, mật khẩu

### f. Voucher/Quà tặng
- Xem, nhận, sử dụng voucher/quà tặng

### g. Chatbot tư vấn
- Tích hợp chatbot AI hỗ trợ tư vấn

---

## 4. Hướng dẫn cài đặt & chạy project

### Yêu cầu:
- Flutter SDK (khuyến nghị bản mới nhất)
- Android Studio/Xcode (nếu build native)
- Thiết bị/emulator Android hoặc iOS

### Các bước cài đặt:

```bash
# 1. Clone project
git clone <repo-url>
cd bloodplusmobile

# 2. Cài đặt dependencies
flutter pub get

# 3. Chạy ứng dụng trên thiết bị/emulator
flutter run
```

**Lưu ý:**
- Đảm bảo đã cấu hình Firebase (file `google-services.json` cho Android, `GoogleService-Info.plist` cho iOS nếu có)
- Nếu gặp lỗi về đa ngôn ngữ, kiểm tra lại các file trong `assets/locales/`

---

## 5. Công nghệ sử dụng

- **Flutter**: Framework phát triển ứng dụng đa nền tảng
- **Dart**: Ngôn ngữ lập trình chính
- **Firebase**: Xác thực, thông báo đẩy, lưu trữ dữ liệu (nếu có)
- **REST API**: Giao tiếp với backend
- **Provider/State Management**: Quản lý trạng thái ứng dụng
- **Localization**: Đa ngôn ngữ (vi, en, ja, ko, th, zh, es)

---

## 6. Ghi chú mở rộng

- **Tùy biến giao diện:** Sửa trong `lib/core/constants/app_colors.dart` và `app_theme.dart`
- **Quản lý route:** Tập trung tại `lib/core/routes/`
- **Tài nguyên ảnh/icon:** Trong `assets/icons/` và `assets/images/`
- **Đa ngôn ngữ:** Thêm/sửa tại `assets/locales/`
- **Quản lý trạng thái:** Sử dụng các manager trong `lib/data/manager/`
- **Tích hợp API:** Qua các service trong `lib/data/services/`

---

## 7. Liên hệ & đóng góp

Nếu muốn đóng góp hoặc báo lỗi, hãy tạo issue hoặc pull request trên repository này.

---

**Tác giả:** Đội ngũ phát triển bloodplusmobile

**Mọi thắc mắc xin liên hệ:**
- Email: [<EMAIL>]
- Github: [link-repo] 