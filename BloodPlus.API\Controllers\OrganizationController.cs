﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.OrganizationModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OrganizationController : ControllerBase
    {
        private readonly IOrganizationService _organizationService;

        public OrganizationController(IOrganizationService organizationService)
        {
            _organizationService = organizationService;
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPost]
        public async Task<ActionResult<string>> CreateOrganization(CreateOrganizationModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _organizationService.CreateOrganizationAsync(model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("{id}")]
        public async Task<ActionResult<string>> DeleteOrganization(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _organizationService.DeleteOrganizationAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<OrganizationModelView>> GetOrganizationById(string id)
        {
            var result = await _organizationService.GetOrganizationByIdAsync(id);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("userId")]
        public async Task<ActionResult<List<OrganizationModelView>>> GetOrganizationByUserId()
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _organizationService.GetOrganizationByUserIdAsync(userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<ActionResult<BasePaginatedList<OrganizationModelView>>> GetAllOrganization(string? name,
                                                                                                     string? address,
                                                                                                     int pageNumber = 1, int pageSize = 5)
        {
            var result = await _organizationService.GetAllOrganizationAsync(pageNumber, pageSize, name, address);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("{id}")]
        public async Task<ActionResult<string>> UpdateOrganization(string id, CreateOrganizationModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _organizationService.UpdateOrganizationAsync(id, model, userId);

            return Ok(new { Message = result });
        }
    }
}
