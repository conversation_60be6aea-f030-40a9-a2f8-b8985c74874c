﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.ChatMessageModelView;
using BloodPlus.ModelViews.Gemini;
using BloodPlus.Services.Hubs;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class ChatMessageService : IChatMessageService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IGenericRepository<ChatMessage> _chatRepo;
        private readonly IAIChatService _aiChatService;
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly IUserService _userService;
        public ChatMessageService(IUnitOfWork unitOfWork, IHubContext<NotificationHub> hubContext, IMapper mapper, IAIChatService aiChatService, IUserService userService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _hubContext = hubContext;
            _chatRepo = _unitOfWork.GetRepository<ChatMessage>();
            _aiChatService = aiChatService;
            _userService = userService;
        }

        public async Task<List<ChatMessageModelView>> AddMessageAsync(CreateChatMessageModelView input)
        {
            string userId = input.UserId.ToString();
            var user = await _userService.GetUserByIdAsync(userId);

            // 1. Tạo message user
            var userMessage = new ChatMessage
            {
                UserId = input.UserId,
                Role = RoleChat.user,
                Message = input.Message,
                ConversationId = input.ConversationId,
                Timestamp = DateTime.UtcNow
            };

            await _chatRepo.InsertAsync(userMessage);

            bool isNewConversation = !(await _chatRepo.Entities.AnyAsync(m => m.ConversationId == input.ConversationId));

            string aiResponse;
            // 2. Build history và gọi GPT
            if (isNewConversation)
            {
                // Nếu là cuộc hội thoại mới, chỉ truyền message đầu tiên
                aiResponse = await _aiChatService.GetCompletionAsync(userMessage.Message, user.Address, user.Name, user.BloodType);
            }
            else
            {
                var history = await BuildChatHistoryAsync(userMessage.ConversationId!, userMessage.UserId);
                aiResponse = await _aiChatService.GetCompletionWithHistoryAsync(userMessage.Message, history, user.Address, user.Name, user.BloodType);
            }

            // 3. Tạo message AI
            var aiMessage = new ChatMessage
            {
                UserId = input.UserId,
                Role = RoleChat.ai,
                Message = aiResponse,
                ConversationId = userMessage.ConversationId,
                Timestamp = DateTime.UtcNow
            };

            await _chatRepo.InsertAsync(aiMessage);
            await _unitOfWork.SaveAsync();

            var aiMessageView = _mapper.Map<ChatMessageModelView>(aiMessage);
            await _hubContext.Clients.User(input.UserId.ToString())
                .SendAsync("ReceiveAIMessage", aiMessageView);

            // 4. Trả về cả 2 messages
            return _mapper.Map<List<ChatMessageModelView>>(new List<ChatMessage> { userMessage, aiMessage });
        }

        private async Task<List<ChatAIMessage>> BuildChatHistoryAsync(string conversationId, Guid userId, int maxTokens = 3000)
        {
            var allMessages = await _chatRepo.Entities
                .Where(m => m.ConversationId == conversationId && m.UserId == userId)
                .OrderByDescending(m => m.Timestamp) // Mới nhất -> cũ nhất
                .ToListAsync();

            var selected = new List<(ChatAIMessage message, int token)>();
            int totalToken = 0;

            foreach (var msg in allMessages)
            {
                var item = new ChatAIMessage
                {
                    Role = msg.Role.ToString(),
                    Content = msg.Message
                };

                int token = item.Content.Length / 4;

                if (totalToken + token > maxTokens)
                    break;

                selected.Add((item, token));
                totalToken += token;
            }

            // Đảo ngược lại để đảm bảo từ cũ -> mới
            var ordered = selected
                .Select(pair => pair.message)
                .Reverse()
                .ToList();

            return ordered;
        }


        public async Task<bool> DeleteConversationAsync(string conversationId, Guid userId)
        {
            var messages = await _chatRepo.Entities
                .Where(x => x.ConversationId == conversationId && x.UserId == userId)
                .ToListAsync();

            if (!messages.Any()) return false;

            foreach (var msg in messages)
                await _chatRepo.DeleteAsync(msg);

            await _unitOfWork.SaveAsync();
            return true;
        }

        public async Task<List<ChatMessageModelView>> GetMessagesByConversationAsync(string conversationId, Guid userId, int limit = 50)
        {
            var messages = await _chatRepo.Entities
                .Where(x => x.UserId == userId && x.ConversationId == conversationId)
                .OrderByDescending(x => x.Timestamp)
                .Take(limit)
                .ToListAsync();

            return _mapper.Map<List<ChatMessageModelView>>(messages.OrderBy(m => m.Timestamp).ToList());
        }

        public async Task<Dictionary<string, string>> GetUserConversationFirstMessagesAsync(Guid userId)
        {
            var messages = await _chatRepo.Entities
                                            .Where(c => c.UserId == userId && c.ConversationId != null)
                                            .ToListAsync(); 

            var result = messages
                .GroupBy(c => c.ConversationId)
                .Select(g => g.OrderBy(m => m.Timestamp).FirstOrDefault())
                .Where(m => m != null)
                .ToDictionary(
                    m => m!.ConversationId!.ToString(),
                    m => m.Message ?? ""
                );

            return result;
        }
    }
}
