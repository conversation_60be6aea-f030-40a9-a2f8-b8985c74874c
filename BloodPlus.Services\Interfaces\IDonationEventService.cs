﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.DonationEventModelView;

namespace BloodPlus.Services.Interfaces
{
    public interface IDonationEventService
    {
        Task<string> CreateDonationEventAsync(CreateDEModelView model, string userId);
        Task<string> UpdateDonationEventAsync(string id, UpdateDEModelView model, string userId);
        Task<BasePaginatedList<ListDEModelView>> GetAllDonationEventAsync(int pageNumber, int pageSize, string? location,
                                                                          DateOnly? startDate, DateOnly? endDate, string? organization);
        Task<DonationEventModelView> GetDonationEventByIdAsync(string id);
        Task<string> DeleteDonationEventAsync(string id, string userId);
    }
}
