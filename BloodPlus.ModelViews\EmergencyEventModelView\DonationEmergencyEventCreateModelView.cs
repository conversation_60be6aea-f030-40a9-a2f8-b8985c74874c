﻿
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace BloodPlus.ModelViews.EmergencyEventModelView
{
    public class DonationEmergencyEventCreateModelView
    {
        [Required]
        public string OrganizationId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; }

        public string? Description { get; set; }

        [Required]
        public string Location { get; set; }

        [Required]
        public DateTime EventDate { get; set; }

        [Required]
        public DateTime EndTime { get; set; }

        [Range(1, int.MaxValue)]
        public int RequiredDonors { get; set; }

        public string? Image { get; set; }

        public bool IsEmergency { get; set; } = false;

        // <PERSON>h<PERSON>n cấp cần thêm:
        public string? RequiredBloodType { get; set; }  // ví dụ: "A+", "O-", "B+"...
        public  IFormFile? imageFile{get;set;}
    }
}
