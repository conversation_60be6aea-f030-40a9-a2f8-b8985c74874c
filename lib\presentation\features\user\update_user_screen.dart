import 'dart:io';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/widgets/userprofile/custom_text_field.dart';
import 'package:bloodplusmobile/core/widgets/userprofile/profile_avatar.dart';
import 'package:bloodplusmobile/core/widgets/dialog/image_picker_dialog.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class UpdateUserScreen extends StatefulWidget {
  const UpdateUserScreen({Key? key}) : super(key: key);

  @override
  State<UpdateUserScreen> createState() => _UpdateUserScreenState();
}

class _UpdateUserScreenState extends State<UpdateUserScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _jobController;
  late TextEditingController _passportController;
  late TextEditingController _bloodTypeController;
  late TextEditingController _dateOfBirthController;

  int? _gender;
  bool _loading = false;
  File? _userImage;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeControllers();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  void _initializeControllers() {
    final user = Provider.of<AppStateNotifier>(context, listen: false).user;
    _nameController = TextEditingController(text: user?.name ?? '');
    _emailController = TextEditingController(text: user?.email ?? '');
    _phoneController = TextEditingController(text: user?.phoneNumber ?? '');
    _addressController = TextEditingController(text: user?.address ?? '');
    _jobController = TextEditingController(text: user?.job ?? '');
    _passportController = TextEditingController(text: user?.passportNumber ?? '');
    _bloodTypeController = TextEditingController(text: user?.bloodType ?? '');
    _dateOfBirthController = TextEditingController(
      text: user?.dateOfBirth != null ? _formatDate(user!.dateOfBirth!) : '',
    );
    _gender = user?.gender;
    _userImage = null;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _jobController.dispose();
    _passportController.dispose();
    _bloodTypeController.dispose();
    _dateOfBirthController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    await showDialog(
      context: context,
      builder: (context) => ImagePickerDialog(
        onImageSelected: (File? image) {
          if (image != null) {
            setState(() {
              _userImage = image;
            });
          }
        },
      ),
    );
  }

  Future<void> _updateUser() async {
    if (!_formKey.currentState!.validate()) return;

    final localizations = AppLocalizations.of(context);
    final confirmed = await DialogHelper.showConfirmationDialog(
      context: context,
      title: localizations.translate('confirm_update'),
      message: localizations.translate('confirm_update_message'),
      cancelButtonText: localizations.translate('cancel'),
      confirmButtonText: localizations.translate('confirm'),
      onConfirm: () {},
      icon: Icons.save,
      iconColor: AppTheme.primaryRed,
    );

    if (!confirmed) return;

    setState(() => _loading = true);

    try {
      final appState = Provider.of<AppStateNotifier>(context, listen: false);
      final userManager = UserManager();
      final token = await userManager.getUserToken();

      if (token == null) throw Exception('Token không hợp lệ, hãy thử đăng nhập lại');

      await appState.updateUserProfile(
        name: _nameController.text,
        dateOfBirth: _dateOfBirthController.text,
        address: _addressController.text,
        job: _jobController.text,
        gender: _gender,
        passportNumber: _passportController.text,
        bloodType: _bloodTypeController.text,
        userImagePath: _userImage?.path,
      );

      await appState.fetchUserProfile(forceRefresh: true);

      if (mounted) {
        DialogHelper.showAnimatedSuccessDialog(
          context: context,
          title: localizations.translate('success'),
          message: localizations.translate('update_success_message'),
          buttonText: localizations.translate('ok'),
          onPressed: () => Navigator.of(context).pop(),
          icon: Icons.check_circle_outline_rounded,
          iconColor: AppTheme.primaryRed,
        );
      }
    } catch (e) {
      if (mounted) {
        DialogHelper.showAnimatedErrorDialog(
          context: context,
          title: localizations.translate('error'),
          message: '${localizations.translate('update_failed')}.',
          buttonText: localizations.translate('close'),
          icon: Icons.error_outline_rounded,
          iconColor: AppTheme.primaryRed,
        );
      }
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<AppStateNotifier>(context).user;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          // Background gradient
          Container(
            height: 200,
            decoration: const BoxDecoration(
              gradient: AppTheme.primaryGradient,
            ),
          ),

          // Main content
          SafeArea(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: _loading
                      ? _buildLoadingWidget()
                      : _buildContent(user),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            AppLocalizations.of(context).translate('update_info'),
            style: AppTheme.headingLarge.copyWith(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: AppTheme.cardDecoration,
        margin: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).translate('updating_info'),
              style: AppTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(UserModel? user) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                _buildProfileSection(user),
                const SizedBox(height: 24),
                _buildPersonalInfoSection(),
                const SizedBox(height: 24),
                _buildContactInfoSection(),
                const SizedBox(height: 24),
                _buildMedicalInfoSection(),
                const SizedBox(height: 32),
                _buildSaveButton(),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection(UserModel? user) {
    return Container(
      decoration: AppTheme.cardDecoration,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context).translate('avatar'),
            style: AppTheme.headingSmall,
          ),
          const SizedBox(height: 16),
          ProfileAvatarWidget(
            userImagePath: _userImage?.path,
            userImage: user?.userImage,
            onTap: _pickImage,
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).translate('tap_to_change_avatar'),
            style: AppTheme.captionText,
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Container(
      decoration: AppTheme.cardDecoration,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.person, color: AppTheme.primaryRed, size: 20),
              ),
              const SizedBox(width: 12),
              Text(AppLocalizations.of(context).translate('personal_info'), style: AppTheme.headingSmall),
            ],
          ),
          const SizedBox(height: 20),
          CustomTextField(
            controller: _nameController,
            label: AppLocalizations.of(context).translate('full_name'),
            icon: Icons.person_outline,
            validator: (value) => (value == null || value.isEmpty)
                ? AppLocalizations.of(context).translate('please_enter_full_name') : null,
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () async {
              FocusScope.of(context).unfocus();
              DateTime? picked = await showDatePicker(
                context: context,
                initialDate: _dateOfBirthController.text.isNotEmpty
                    ? DateTime.tryParse(_dateOfBirthController.text) ?? DateTime(2000, 1, 1)
                    : DateTime(2000, 1, 1),
                firstDate: DateTime(1900),
                lastDate: DateTime.now(),
              );
              if (picked != null) {
                _dateOfBirthController.text = _formatDate(picked);
                setState(() {});
              }
            },
            child: AbsorbPointer(
              child: CustomTextField(
                controller: _dateOfBirthController,
                label: AppLocalizations.of(context).translate('date_of_birth'),
                icon: Icons.calendar_today_outlined,
                validator: (value) => (value == null || value.isEmpty)
                    ? AppLocalizations.of(context).translate('please_choose_dob') : null,
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildGenderDropdown(),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _jobController,
            label: AppLocalizations.of(context).translate('job'),
            icon: Icons.work_outline,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _passportController,
            label: AppLocalizations.of(context).translate('passport_number'),
            icon: Icons.badge_outlined,
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return Container(
      decoration: AppTheme.cardDecoration,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.contact_mail, color: AppTheme.primaryRed, size: 20),
              ),
              const SizedBox(width: 12),
              Text(AppLocalizations.of(context).translate('contact_info'), style: AppTheme.headingSmall),
            ],
          ),
          const SizedBox(height: 20),
          CustomTextField(
            controller: _emailController,
            label: AppLocalizations.of(context).translate('email'),
            icon: Icons.email_outlined,
            enabled: false,
            suffixIcon: Icon(Icons.lock_outline, color: AppTheme.textSecondary, size: 20),
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _phoneController,
            label: AppLocalizations.of(context).translate('phone_number'),
            icon: Icons.phone_outlined,
            enabled: false,
            suffixIcon: Icon(Icons.lock_outline, color: AppTheme.textSecondary, size: 20),
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _addressController,
            label: AppLocalizations.of(context).translate('address'),
            icon: Icons.location_on_outlined,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildMedicalInfoSection() {
    return Container(
      decoration: AppTheme.cardDecoration,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.local_hospital, color: AppTheme.primaryRed, size: 20),
              ),
              const SizedBox(width: 12),
              Text(AppLocalizations.of(context).translate('medical_info'), style: AppTheme.headingSmall),
            ],
          ),
          const SizedBox(height: 20),
          DropdownButtonFormField<String>(
            value: _bloodTypeController.text.isNotEmpty ? _bloodTypeController.text : null,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).translate('blood_type'),
              labelStyle: AppTheme.bodyMedium,
              prefixIcon: Icon(Icons.water_drop_outlined, color: AppTheme.textSecondary),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
            items: [
              'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
            ].map((type) => DropdownMenuItem(
              value: type,
              child: Text(type),
            )).toList(),
            onChanged: (value) {
              _bloodTypeController.text = value ?? '';
              setState(() {});
            },
            validator: (value) => (value == null || value.isEmpty)
                ? AppLocalizations.of(context).translate('please_choose_blood_type') : null,
          ),
        ],
      ),
    );
  }

  Widget _buildGenderDropdown() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.borderColor),
        borderRadius: BorderRadius.circular(12),
      ),
      child: DropdownButtonFormField<int>(
        value: ([0, 1, 2].contains(_gender)) ? _gender : null,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).translate('gender'),
          labelStyle: AppTheme.bodyMedium,
          prefixIcon: Icon(Icons.wc_outlined, color: AppTheme.textSecondary),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        dropdownColor: Colors.white,
        style: AppTheme.bodyLarge,
        items: [
          DropdownMenuItem(
            value: 0,
            child: Text(AppLocalizations.of(context).translate('male')),
          ),
          DropdownMenuItem(
            value: 1,
            child: Text(AppLocalizations.of(context).translate('female')),
          ),
          DropdownMenuItem(
            value: 2,
            child: Text(AppLocalizations.of(context).translate('other')),
          ),
        ],
        onChanged: (v) => setState(() => _gender = v),
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryRed.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _loading ? null : _updateUser,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.save, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context).translate('save_changes'),
              style: AppTheme.buttonText.copyWith(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return "${date.year.toString().padLeft(4, '0')}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }
}