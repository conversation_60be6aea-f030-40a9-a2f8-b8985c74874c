﻿using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.VoucherModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VoucherController : ControllerBase
    {
        private readonly IVoucherService _voucherService;

        public VoucherController(IVoucherService voucherService)
        {
            _voucherService = voucherService;
        }


        [Authorize(Roles = "Admin,Manager")]
        [HttpPost]
        public async Task<ActionResult<string>> CreateVoucher(CreateVoucherModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _voucherService.CreateVoucherAsync(model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("{id}")]
        public async Task<ActionResult<string>> DeleteVoucher(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _voucherService.DeleteVoucherAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<VoucherModelView>> GetVoucherById(string id)
        {
            var result = await _voucherService.GetVoucherByIdAsync(id);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("for-admin")]
        public async Task<IActionResult> GetAllVoucherForAdmin(VoucherType? type, int pageNumber = 1, int pageSize = 5)
        {
            var result = await _voucherService.GetAllVoucherForAdminAsync(pageNumber, pageSize, type);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("of-user")]
        public async Task<IActionResult> GetAllVoucherOfUser(VoucherType? type, int pageNumber = 1, int pageSize = 5)
        {
            var userId = User.FindFirst("userId")?.Value;
            var result = await _voucherService.GetAllVoucherOfUserAsync(pageNumber, pageSize, type, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("{id}")]
        public async Task<ActionResult<string>> AddVoucherForUser(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _voucherService.AddVoucherForUserAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("for-user")]
        public async Task<IActionResult> GetAllVoucherForUser(VoucherType? type, int pageNumber = 1, int pageSize = 5)
        {
            var result = await _voucherService.GetAllVoucherForUserAsync(pageNumber, pageSize, type);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("{id}")]
        public async Task<ActionResult<string>> UpdateVoucher(string id, UpdateVoucherModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _voucherService.UpdateVoucherAsync(id, model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("detail/{id}")]
        public async Task<ActionResult<DetailVoucherModel>> GetDetailVoucherById(string id)
        {
            var result = await _voucherService.GetDetailVoucherByIdAsync(id);

            return Ok(new { Message = result });
        }
    }
}
