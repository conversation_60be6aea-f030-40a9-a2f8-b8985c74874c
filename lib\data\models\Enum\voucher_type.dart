import 'package:bloodplusmobile/core/language_helper/localization.dart';

enum VoucherType {
  Shopping,
  Health,
  Beautify,
  Food,
  Travel,
  Education,
  Entertainment,
  Fitness,
  Transportation,
  Electronics,
  Fashion,
  HomeLiving,
  GiftCard
}

String getVoucherTypeName(VoucherType type, [AppLocalizations? localizations]) {
  switch (type) {
    case VoucherType.Shopping:
      return localizations?.translate('voucher_type_shopping') ?? 'Shopping';
    case VoucherType.Health:
      return localizations?.translate('voucher_type_health') ?? 'Health';
    case VoucherType.Beautify:
      return localizations?.translate('voucher_type_beautify') ?? 'Beautify';
    case VoucherType.Food:
      return localizations?.translate('voucher_type_food') ?? 'Food';
    case VoucherType.Travel:
      return localizations?.translate('voucher_type_travel') ?? 'Travel';
    case VoucherType.Education:
      return localizations?.translate('voucher_type_education') ?? 'Education';
    case VoucherType.Entertainment:
      return localizations?.translate('voucher_type_entertainment') ?? 'Entertainment';
    case VoucherType.Fitness:
      return localizations?.translate('voucher_type_fitness') ?? 'Fitness';
    case VoucherType.Transportation:
      return localizations?.translate('voucher_type_transportation') ?? 'Transportation';
    case VoucherType.Electronics:
      return localizations?.translate('voucher_type_electronics') ?? 'Electronics';
    case VoucherType.Fashion:
      return localizations?.translate('voucher_type_fashion') ?? 'Fashion';
    case VoucherType.HomeLiving:
      return localizations?.translate('voucher_type_home_living') ?? 'Home & Living';
    case VoucherType.GiftCard:
      return localizations?.translate('voucher_type_gift_card') ?? 'Gift Card';
    default:
      return '';
  }
} 