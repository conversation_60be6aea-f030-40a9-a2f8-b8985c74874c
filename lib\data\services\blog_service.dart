import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/blog_model.dart';
import '../repositories/blog_response.dart';

class BlogService {
  final UserManager _userManager = UserManager();

  Future<BlogResponse> getBlogs({
    String? title,
    String? content,
    int pageNumber = 1,
    int pageSize = 5,
  }) async {
    final queryParameters = {
      if (title != null) 'title': title,
      if (content != null) 'content': content,
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
    };

    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.blog,
        token: token,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        return BlogResponse.fromJson(jsonDecode(response.body));
      } else {
        throw Exception('<PERSON><PERSON><PERSON> danh sách blog thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('BlogService error: $e');
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<BlogModel> getBlogById(String id) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedGet(
        '${ApiConfig.blog}/$id',
        token: token,
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final message = jsonResponse['Message'] as Map<String, dynamic>? ??
                       jsonResponse['message'] as Map<String, dynamic>? ?? {};
        return BlogModel.fromJson(message);
      } else {
        throw Exception('Lấy thông tin blog thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối khi lấy blog theo ID: $e');
    }
  }
}