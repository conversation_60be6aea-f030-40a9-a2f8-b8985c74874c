import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/system/enhanced_donation_card.dart';
import 'package:bloodplusmobile/core/widgets/user/progress_circle.dart';
import 'package:bloodplusmobile/core/widgets/dialog/badge_detail_dialog.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/notification_manager.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../user/heart_progress_bar.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/helpers/blood_component_helper.dart';

class HeaderSection extends StatefulWidget {
  @override
  _HeaderSectionState createState() => _HeaderSectionState();
}

class _HeaderSectionState extends State<HeaderSection> with TickerProviderStateMixin {
  final UserManager _userManager = UserManager();
  late AnimationController _shimmerController;
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<double> _shimmerAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 900),
      vsync: this,
    )..repeat();
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 1.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 480),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _slideController.forward();
    _fadeController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserInfo();
    });
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadUserInfo() async {
    final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
    final userId = await _userManager.getUserId();
    final token = await _userManager.getUserToken();

    if (userId != null && token != null) {
      await appStateNotifier.fetchUserProfile(forceRefresh: true);
      await appStateNotifier.fetchDaysWaiting(userId, token);
    }
  }

  String _truncateAddress(String? address, int maxLength) {
    if (address == null) return AppLocalizations.of(context).translate('no_address') ?? 'No address';
    if (address.length <= maxLength) return address;
    return '${address.substring(0, maxLength - 3)}...';
  }

  Widget _buildEnhancedShimmer() {
    final screenWidth = MediaQuery.of(context).size.width;
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: screenWidth * 0.2,
          height: screenWidth * 0.2,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.4),
                Colors.white.withOpacity(0.1),
              ],
              stops: [
                0.0,
                _shimmerAnimation.value.clamp(0.0, 1.0),
                1.0,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.2),
                blurRadius: 15,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Container(
            margin: EdgeInsets.all(screenWidth * 0.03),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.1),
            ),
          ),
        );
      },
    );
  }

  MapEntry<String, int>? _findEarliestDaysWaiting(Map<String, int>? daysWaiting) {
    if (daysWaiting == null) return null;
    final nonZeroEntries = daysWaiting.entries.where((entry) => entry.value > 0).toList();
    if (nonZeroEntries.isEmpty) return null;
    return nonZeroEntries.reduce((a, b) => a.value < b.value ? a : b);
  }

  List<Widget> _buildDetailedDaysWaitingWidgets(Map<String, int>? daysWaiting) {
    if (daysWaiting == null) {
      return [
        Container(
          padding: const EdgeInsets.all(20),
          child: const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        )
      ];
    }

    final List<Widget> widgets = [];
    final Map<String, String> donationTypeLabels = {
      'wholeBloodDaysLeft': 'whole_blood',
      'redBloodCellsDaysLeft': 'red_blood_cells',
      'plasmaDaysLeft': 'plasma',
      'plateletsDaysLeft': 'platelets',
      'whiteBloodCellsDaysLeft': 'white_blood_cells',
    };

    daysWaiting.forEach((key, value) {
      if (value > 0) {
        final donationType = donationTypeLabels[key] ?? key;
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: ProgressCircle(
              daysLeft: value,
              label: 'days_left_to_donate',
              donationType: donationType,
            ),
          ),
        );
      }
    });

    return widgets.isNotEmpty
        ? widgets
        : [
      _buildCanDonateToday(context)
    ];
  }

  Widget _buildCanDonateToday(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Container(
      padding: EdgeInsets.all(screenWidth * 0.05),
      child: Column(
        children: [
          const HeartProgressBar(),
          SizedBox(height: screenWidth * 0.03),
          Text(
            AppLocalizations.of(context).translate('can_donate_today'),
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: screenWidth * 0.045,
              fontWeight: FontWeight.w600,
              shadows: [
                Shadow(
                  blurRadius: 10.0,
                  color: Colors.black.withOpacity(0.3),
                  offset: Offset(2.0, 2.0),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getDonationLevel(int donationCount) {
    final localizations = AppLocalizations.of(context);
    if (donationCount >= 1 && donationCount <= 2) {
      return localizations.translate('rookie');
    } else if (donationCount >= 3 && donationCount <= 8) {
      return localizations.translate('red_warrior');
    } else if (donationCount >= 9 && donationCount <= 15) {
      return localizations.translate('active_volunteer');
    } else if (donationCount >= 16 && donationCount <= 25) {
      return localizations.translate('blood_hero');
    } else if (donationCount >= 26 && donationCount <= 50) {
      return localizations.translate('legendary_donor');
    } else if (donationCount >= 51) {
      return localizations.translate('immortal_savior');
    } else {
      return localizations.translate('new_donor');
    }
  }

  IconData _getDonationLevelIcon(int donationCount) {
    if (donationCount >= 1 && donationCount <= 2) {
      return Icons.favorite_border;
    } else if (donationCount >= 3 && donationCount <= 8) {
      return Icons.favorite;
    } else if (donationCount >= 9 && donationCount <= 15) {
      return Icons.volunteer_activism;
    } else if (donationCount >= 16 && donationCount <= 25) {
      return Icons.workspace_premium;
    } else if (donationCount >= 26 && donationCount <= 50) {
      return Icons.auto_awesome;
    } else if (donationCount >= 51) {
      return Icons.star;
    } else {
      return Icons.person_add;
    }
  }

  List<Color> _getDonationLevelColors(int donationCount) {
    if (donationCount >= 1 && donationCount <= 2) {
      return [const Color(0xFFEC4899), const Color(0xFFF472B6)];
    } else if (donationCount >= 3 && donationCount <= 8) {
      return [const Color(0xFFDC2626), const Color(0xFFEF4444)];
    } else if (donationCount >= 9 && donationCount <= 15) {
      return [const Color(0xFFF59E0B), const Color(0xFFFBBF24)];
    } else if (donationCount >= 16 && donationCount <= 25) {
      return [const Color(0xFFEAB308), const Color(0xFFFDE047)];
    } else if (donationCount >= 26 && donationCount <= 50) {
      return [const Color(0xFF8B5CF6), const Color(0xFFA78BFA)];
    } else if (donationCount >= 51) {
      return [const Color(0xFF10B981), const Color(0xFF34D399)];
    } else {
      return [const Color(0xFF6B7280), const Color(0xFF9CA3AF)];
    }
  }

  Color _getDonationLevelColor(int donationCount) {
    if (donationCount >= 1 && donationCount <= 2) {
      return const Color(0xFFEC4899);
    } else if (donationCount >= 3 && donationCount <= 8) {
      return const Color(0xFFDC2626);
    } else if (donationCount >= 9 && donationCount <= 15) {
      return const Color(0xFFF59E0B);
    } else if (donationCount >= 16 && donationCount <= 25) {
      return const Color(0xFFEAB308);
    } else if (donationCount >= 26 && donationCount <= 50) {
      return const Color(0xFF8B5CF6);
    } else if (donationCount >= 51) {
      return const Color(0xFF10B981);
    } else {
      return const Color(0xFF6B7280);
    }
  }

  String _getDonationLevelDescription(int donationCount) {
    final localizations = AppLocalizations.of(context);
    if (donationCount >= 1 && donationCount <= 2) {
      return localizations.translate('rookie_description') ?? 'Completed 1-2 blood donations. You\'re on your way to becoming a hero!';
    } else if (donationCount >= 3 && donationCount <= 8) {
      return localizations.translate('red_war_description') ?? 'Completed 3-8 blood donations. You are a true warrior of life!';
    } else if (donationCount >= 9 && donationCount <= 15) {
      return localizations.translate('active_volunteer_description') ?? 'Completed 9-15 blood donations. Your dedication is saving many lives!';
    } else if (donationCount >= 16 && donationCount <= 25) {
      return localizations.translate('blood_hero_description') ?? 'Completed 16+ blood donations. You are a true hero of the community!';
    } else if (donationCount >= 26 && donationCount <= 50) {
      return localizations.translate('legendary_donor_description') ?? 'Completed 26+ blood donations. You are a living legend of blood donation!';
    } else if (donationCount >= 51) {
      return localizations.translate('immortal_savior_description') ?? 'Completed 51+ blood donations. You are a true savior of humanity!';
    } else {
      return localizations.translate('new_donor_description') ?? 'Start your blood donation journey to save lives';
    }
  }

  int? _getNextBadgeDonations(int donationCount) {
    if (donationCount < 1) return 1;
    if (donationCount < 3) return 3;
    if (donationCount < 9) return 9;
    if (donationCount < 16) return 16;
    if (donationCount < 26) return 26;
    if (donationCount < 51) return 51;
    return null;
  }

  String? _getNextBadgeName(int donationCount) {
    if (donationCount < 1) return 'rookie';
    if (donationCount < 3) return 'red_warrior';
    if (donationCount < 9) return 'active_volunteer';
    if (donationCount < 16) return 'blood_hero';
    if (donationCount < 26) return 'legendary_donor';
    if (donationCount < 51) return 'immortal_savior';
    return null;
  }

  IconData? _getNextBadgeIcon(int donationCount) {
    if (donationCount < 1) return Icons.favorite_border;
    if (donationCount < 3) return Icons.favorite;
    if (donationCount < 9) return Icons.volunteer_activism;
    if (donationCount < 16) return Icons.workspace_premium;
    if (donationCount < 26) return Icons.auto_awesome;
    if (donationCount < 51) return Icons.star;
    return null;
  }

  Color? _getNextBadgeColor(int donationCount) {
    if (donationCount < 1) return const Color(0xFFEC4899);
    if (donationCount < 3) return const Color(0xFFDC2626);
    if (donationCount < 9) return const Color(0xFFF59E0B);
    if (donationCount < 16) return const Color(0xFFEAB308);
    if (donationCount < 26) return const Color(0xFF8B5CF6);
    if (donationCount < 51) return const Color(0xFF10B981);
    return null;
  }

  void _showBadgeDetailDialog(BuildContext context, int donationCount) {
    final badgeName = _getDonationLevel(donationCount);
    final badgeIcon = _getDonationLevelIcon(donationCount);
    final badgeGradient = _getDonationLevelColors(donationCount);
    final badgeColor = _getDonationLevelColor(donationCount);
    final badgeDescription = _getDonationLevelDescription(donationCount);
    final nextBadgeDonations = _getNextBadgeDonations(donationCount);
    final nextBadgeName = _getNextBadgeName(donationCount);
    final nextBadgeIcon = _getNextBadgeIcon(donationCount);
    final nextBadgeColor = _getNextBadgeColor(donationCount);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => BadgeDetailDialog(
        badgeName: badgeName,
        badgeDescription: badgeDescription,
        badgeIcon: badgeIcon,
        badgeGradient: badgeGradient,
        badgeColor: badgeColor,
        donationCount: donationCount,
        nextBadgeDonations: nextBadgeDonations,
        nextBadgeName: nextBadgeName,
        nextBadgeIcon: nextBadgeIcon,
        nextBadgeColor: nextBadgeColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateNotifier>(context);
    final user = appState.user;
    final daysWaiting = appState.daysWaiting;
    final donationCount = appState.donationCount;
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = screenWidth * 0.04;

    // Lấy dữ liệu mới
    int daysRemaining = daysWaiting != null && daysWaiting['daysRemaining'] != null ? (daysWaiting['daysRemaining'] as num).toInt() : 0;
    String bloodComponent = daysWaiting != null && daysWaiting['bloodComponent'] != null ? daysWaiting['bloodComponent'].toString() : '';

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFFD32F2F),
            Color(0xFFE53935),
            Color(0xFFF44336),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(28),
        ),
      ),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              padding,
              screenWidth * 0.12,
              padding,
              screenWidth * 0.08,
            ),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    user == null
                        ? _buildEnhancedShimmer()
                        : CircleAvatar(
                      radius: screenWidth * 0.09,
                      backgroundImage: user.userImage != null && user.userImage!.startsWith('http')
                          ? NetworkImage(user.userImage!)
                          : const AssetImage('assets/images/profile.jpg') as ImageProvider,
                      onBackgroundImageError: (exception, stackTrace) {
                        print('Error loading image: $exception');
                      },
                    ),
                    SizedBox(width: screenWidth * 0.03),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user?.name ?? localizations.translate('loading'),
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: screenWidth * 0.07,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          SizedBox(height: screenWidth * 0.005),
                          if (user != null)
                            Row(
                              children: [
                                const Icon(
                                  Icons.location_on,
                                  color: Colors.white70,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    _truncateAddress(user.address, 32),
                                    style: GoogleFonts.poppins(
                                      color: Colors.white70,
                                      fontSize: screenWidth * 0.035,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                    Consumer<NotificationManager>(
                      builder: (context, notificationManager, child) {
                        return Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                shape: BoxShape.circle,
                              ),
                              child: IconButton(
                                icon: const Icon(Icons.notifications_none_outlined),
                                color: Colors.white,
                                iconSize: screenWidth * 0.06,
                                onPressed: () {
                                  NavigationService.navigateTo(AppRoutes.notification);
                                },
                              ),
                            ),
                            if (notificationManager.unreadCount > 0)
                              Positioned(
                                right: 0,
                                top: 0,
                                child: Container(
                                  padding: EdgeInsets.all(screenWidth * 0.01),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                    border: Border.all(color: Colors.white, width: 2),
                                  ),
                                  child: Text(
                                    notificationManager.unreadCount > 99 
                                        ? '99+' 
                                        : notificationManager.unreadCount.toString(),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: screenWidth * 0.025,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
                SizedBox(height: screenWidth * 0.05),
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: EnhancedDonationCard(
                          onTap: () => _showBadgeDetailDialog(context, donationCount),
                          gradientColors: _getDonationLevelColors(donationCount),
                          shadowColor: _getDonationLevelColor(donationCount),
                          child: _buildLevelCardContent(context, donationCount),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.03),
                      Expanded(
                        child: EnhancedDonationCard(
                          onTap: daysRemaining == 0
                              ? () {
                                  NavigationService.navigateTo(AppRoutes.donationEvent);
                                }
                              : null,
                          gradientColors: [const Color(0xFF6B7280), const Color(0xFF9CA3AF)],
                          shadowColor: const Color(0xFF6B7280),
                          child: daysWaiting != null
                              ? _buildDaysWaitingCardContentV2(context, daysRemaining, bloodComponent)
                              : _buildCanDonateTodayCardContent(context),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLevelCardContent(BuildContext context, int donationCount) {
    final localizations = AppLocalizations.of(context);
    final badgeName = _getDonationLevel(donationCount);
    final badgeIcon = _getDonationLevelIcon(donationCount);
    final screenWidth = MediaQuery.of(context).size.width;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(screenWidth * 0.015),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                badgeIcon,
                color: Colors.white,
                size: screenWidth * 0.05,
              ),
            ),
            SizedBox(width: screenWidth * 0.02),
            Expanded(
              child: Text(
                localizations.translate('donation_count'),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: screenWidth * 0.035,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: screenWidth * 0.015),
        Flexible(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                donationCount.toString(),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: screenWidth * 0.09,
                  fontWeight: FontWeight.w800,
                  height: 1,
                ),
              ),
              SizedBox(width: screenWidth * 0.01),
              Padding(
                padding: EdgeInsets.only(bottom: screenWidth * 0.005),
                child: Text(
                  localizations.translate('times') ?? 'times',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: screenWidth * 0.035,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: screenWidth * 0.015),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.025,
            vertical: screenWidth * 0.01,
          ),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(screenWidth * 0.04),
          ),
          child: Text(
            localizations.translate(badgeName),
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: screenWidth * 0.03,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildDaysWaitingCardContentV2(BuildContext context, int daysRemaining, String bloodComponent) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    if (daysRemaining == 0) {
      return _buildCanDonateTodayCardContent(context);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(screenWidth * 0.015),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.hourglass_bottom_rounded,
                color: Colors.white,
                size: screenWidth * 0.05,
              ),
            ),
            SizedBox(width: screenWidth * 0.02),
            Expanded(
              child: Text(
                localizations.translate('recovery_time'),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: screenWidth * 0.035,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: screenWidth * 0.015),
        Flexible(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                daysRemaining.toString(),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: screenWidth * 0.09,
                  fontWeight: FontWeight.w800,
                  height: 1,
                ),
              ),
              SizedBox(width: screenWidth * 0.01),
              Padding(
                padding: EdgeInsets.only(bottom: screenWidth * 0.005),
                child: Text(
                  localizations.translate('days') ?? 'days',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: screenWidth * 0.035,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: screenWidth * 0.015),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.025,
            vertical: screenWidth * 0.01,
          ),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(screenWidth * 0.04),
          ),
          child: Text(
            getTranslatedBloodComponentFromString(localizations, bloodComponent),
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: screenWidth * 0.03,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildCanDonateTodayCardContent(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(screenWidth * 0.015),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.favorite,
                color: Colors.pinkAccent,
                size: screenWidth * 0.05,
              ),
            ),
            SizedBox(width: screenWidth * 0.02),
            Expanded(
              child: Text(
                localizations.translate('ready_to_donate'),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: screenWidth * 0.035,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: screenWidth * 0.015),
        Flexible(
          child: Text(
            localizations.translate('can_donate_today_short'),
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: screenWidth * 0.065,
              fontWeight: FontWeight.w600,
              height: 1.1,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(height: screenWidth * 0.015),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.025,
            vertical: screenWidth * 0.01,
          ),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(screenWidth * 0.04),
          ),
          child: Text(
            localizations.translate('check_events'),
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: screenWidth * 0.03,
            ),
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}