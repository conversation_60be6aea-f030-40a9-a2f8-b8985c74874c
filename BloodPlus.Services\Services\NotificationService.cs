﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.ModelViews.NotificationModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class NotificationService : INotificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly OneSignalService _oneSignalService;

        public NotificationService(IUnitOfWork unitOfWork, OneSignalService oneSignalService)
        {
            _unitOfWork = unitOfWork;
            _oneSignalService = oneSignalService;
        }

        public async Task<BasePaginatedList<ListNotificationModel>> GetAllNotificationAsync(int pageNumber, int pageSize, string userId)
        {
            var user = Guid.Parse(userId);
            var notificationQuery = _unitOfWork.GetRepository<Notification>()
                .Entities
                .Where(v => !v.DeletedTime.HasValue && (v.UserId == user || v.UserId == null));

            int totalCount = await notificationQuery.CountAsync();

            var result = await notificationQuery
                .OrderByDescending(v => v.CreatedTime)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .Select(v => new ListNotificationModel
                {
                    Id = v.Id,
                    Title = v.Title,
                    Content = v.Content,
                    IsRead = v.IsRead,
                    SentDate = v.SentDate,
                })
                .ToListAsync();

            return new BasePaginatedList<ListNotificationModel>(result, totalCount, pageNumber, pageSize);
        }


        public async Task<NotificationModelView> GetNotificationByIdAsync(string id)
        {
            try
            {
                var notification = await _unitOfWork.GetRepository<Notification>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == id && !u.DeletedTime.HasValue);

                if (notification == null)
                {
                    throw new Exception("can not find or Notification is deleted");
                }

                if (notification.IsRead == false)
                {
                    notification.IsRead = true;
                    await _unitOfWork.GetRepository<Notification>().UpdateAsync(notification);
                    await _unitOfWork.SaveAsync();
                }

                return new NotificationModelView
                {
                    Title = notification.Title,
                    Content = notification.Content,
                    IsRead = notification.IsRead,
                    SentDate = notification.SentDate,
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<bool> SendNotificationToAllUsersAsync(NotificationForUser model)
        {
            if (string.IsNullOrWhiteSpace(model.Title) || string.IsNullOrWhiteSpace(model.Content))
                throw new ArgumentException("Tiêu đề và nội dung thông báo không được để trống.");

            var result = await _oneSignalService.SendNotificationAsync(model.Title, model.Content);

            if (result)
            {
                var notification = new Notification
                {
                    Title = model.Title,
                    Content = model.Content,
                    IsRead = false,
                    SentDate = DateTime.UtcNow,
                    CreatedTime = DateTime.UtcNow,
                    LastUpdatedTime = DateTime.UtcNow,
                    UserId = null, 
                };

                await _unitOfWork.GetRepository<Notification>().InsertAsync(notification);
                await _unitOfWork.SaveAsync();
            }

            return result;
        }

    }
}
