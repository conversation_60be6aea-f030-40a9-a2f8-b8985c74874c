﻿using BloodPlus.ModelViews.EmergencyEventModelView;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BloodTypeController : ControllerBase
    {
        private readonly IBloodTypeService _bloodTypeService;

        public BloodTypeController(IBloodTypeService bloodTypeService)
        {
            _bloodTypeService = bloodTypeService;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<IActionResult>  GetAll()
        {
            var result = await _bloodTypeService.GetAllBloodType();
            return Ok(result);
        }
    }
}
