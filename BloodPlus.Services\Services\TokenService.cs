﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Utils;
using BloodPlus.ModelViews.AuthModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace BloodPlus.Services.Services
{
    public class TokenService : ITokenService
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly UserManager<User> _userManager;

        public TokenService(IConfiguration configuration, IUnitOfWork unitOfWork, UserManager<User> userManager)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _userManager = userManager;
        }

        public async Task<TokenModelView> GenerateJwtTokenAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            var roles = await _userManager.GetRolesAsync(user ?? throw new Exception("User not found"));

            // Các claims của token
            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim("userId", userId),
                new Claim("email", user.Email),
            };

            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Tạo token
            var token = CreateToken(claims);

            var tokenString = new JwtSecurityTokenHandler().WriteToken(token);


            await AddTokenToDatabaseAsync(userId);

            return new TokenModelView
            {
                AccessToken = tokenString,
            };
        }

        private async Task AddTokenToDatabaseAsync(string userId)
        {
            if (!Guid.TryParse(userId, out Guid userIdGuid))
            {
                throw new ArgumentException("Invalid user ID format.");
            }
            // Find the user by their userId using the _userManager
            var user = await _unitOfWork.GetRepository<User>().GetByIdAsync(userIdGuid);
            if (user != null)
            {
                // Get the token repository from the unit of work
                var tokenRepository = _unitOfWork.GetRepository<UserToken>();

                try
                {
                    // Begin a database transaction to ensure atomicity
                    _unitOfWork.BeginTransaction();

                    // Check if there's already an existing token for the user with the specific login provider
                    var existingToken = tokenRepository.Entities
                        .FirstOrDefault(t => t.UserId == Guid.Parse(userId) && t.LoginProvider == "CustomLoginProvider");

                    if (existingToken != null)
                    {
                        existingToken.LastUpdatedBy = user.UserName;
                        existingToken.LastUpdatedTime = CoreHelper.SystemTimeNow;

                        // Update the existing token in the database
                        await tokenRepository.UpdateAsync(existingToken);
                    }
                    else
                    {
                        // If no existing token is found, create a new token record for the user
                        var userToken = new UserToken
                        {
                            UserId = Guid.Parse(userId),
                            LoginProvider = "CustomLoginProvider",
                            Name = "JWT",
                            CreatedBy = user.UserName,
                            CreatedTime = CoreHelper.SystemTimeNow,
                            LastUpdatedBy = user.UserName,
                            LastUpdatedTime = CoreHelper.SystemTimeNow
                        };

                        // Insert the new token into the repository
                        await tokenRepository.InsertAsync(userToken);
                    }

                    // Commit the transaction to save changes to the database
                    await _unitOfWork.SaveAsync();
                    _unitOfWork.CommitTransaction();
                }
                catch (Exception)
                {
                    _unitOfWork.RollBack();
                    throw;
                }
            }
        }

        private JwtSecurityToken CreateToken(List<Claim> authClaims)
        {
            // Retrieve the signing key for JWT from the configuration and convert it to a byte array
            var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));

            // Try to parse the token validity duration in minutes from the configuration
            _ = int.TryParse(_configuration["JWT:TokenValidityInMinutes"], out int tokenValidityInMinutes);

            // Create a new JWT token with the specified claims and other properties
            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                expires: DateTime.Now.AddMinutes(tokenValidityInMinutes),
                claims: authClaims,
                signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
            );

            // Return the created JWT token
            return token;
        }
    }
}
