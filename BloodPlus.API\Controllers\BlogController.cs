﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.BlogModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogController : ControllerBase
    {
        private readonly IBlogService _blogService;

        public BlogController(IBlogService blogService)
        {
            _blogService = blogService;
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPost]
        public async Task<ActionResult<string>> CreateBlog(CreateBlogModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _blogService.CreateBlogAsync(model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("{id}")]
        public async Task<ActionResult<string>> DeleteBlog(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _blogService.DeleteBlogAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<BlogModelView>> GetById(string id)
        {
            var result = await _blogService.GetBlogByIdAsync(id);

            return Ok(new { Message = result });
        }


        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<IActionResult> GetAllBlog(string? title, int pageNumber = 1, int pageSize = 5)
        {
            var result = await _blogService.GetAllBlogAsync(pageNumber, pageSize, title);

            return Ok(new { Message = result });
        }


        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("{id}")]
        public async Task<ActionResult<string>> UpdateBlog(string id, CreateBlogModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _blogService.UpdateBlogAsync(id, model, userId);

            return Ok(new { Message = result });
        }
    }
}
