﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.ModelViews.AuthModelViews;
using BloodPlus.ModelViews.UserModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface IAuthService
    {
        Task<User> AuthenticateAsync(LoginModelView model);
        Task<User> ValidateGoogleTokenAsync(string idToken, string deviceToken);
        Task<string> CreateAccountOtpAsync(CreateAccountModelView model);
        Task<string> VerifyOtpAsync(VerifyOtpModel model);

        Task<string> ForgetPasswordAsync(ForgetPasswordModel model);
        Task<string> VerifyOtpForgetPasswordAsync(VerifyOtpModel model);
        Task<string> ResetPasswordAsync(ResetPasswordModel model);
    }
}
