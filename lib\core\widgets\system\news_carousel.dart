import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/models/blog_model.dart';
import 'package:flutter/material.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class NewsCarousel extends StatefulWidget {
  const NewsCarousel({Key? key}) : super(key: key);

  @override
  State<NewsCarousel> createState() => _NewsCarouselState();
}

class _NewsCarouselState extends State<NewsCarousel> with TickerProviderStateMixin {
  int _currentIndex = 0;
  final CarouselController _controller = CarouselController();
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = screenWidth * 0.04; // 4% of screen width for padding
    final isSmallScreen = screenWidth < 600;

    return Consumer<AppStateNotifier>(
      builder: (context, appState, child) {
        final blogs = appState.blogs;
        final isLoading = appState.isLoading;

        return Container(
          margin: EdgeInsets.symmetric(vertical: screenWidth * 0.02), // 8px equivalent
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: padding),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('latest_blogs'),
                      style: GoogleFonts.poppins(
                        fontSize: isSmallScreen ? 18 : 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.black87,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        NavigationService.navigateTo(AppRoutes.blog);
                      },
                      icon: Icon(
                        Icons.arrow_forward_ios,
                        size: isSmallScreen ? 12 : 14,
                        color: const Color(0xFF6B7280),
                      ),
                      label: Text(
                        localizations.translate('view_all'),
                        style: GoogleFonts.poppins(
                          fontSize: isSmallScreen ? 12 : 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: screenWidth * 0.04), // 16px equivalent
              SizedBox(
                height: screenWidth * 0.7, // 280px equivalent on 400px screen
                child: isLoading
                    ? _buildLoadingIndicator()
                    : blogs.isEmpty
                    ? _buildEmptyWidget(localizations)
                    : FadeTransition(
                  opacity: _fadeAnimation,
                  child: CarouselSlider(
                    options: CarouselOptions(
                      height: screenWidth * 0.7,
                      autoPlay: true,
                      autoPlayInterval: const Duration(seconds: 4),
                      autoPlayAnimationDuration: const Duration(milliseconds: 800),
                      autoPlayCurve: Curves.fastOutSlowIn,
                      enlargeCenterPage: true,
                      enlargeFactor: 0.25,
                      viewportFraction: isSmallScreen ? 0.9 : 0.85,
                      enableInfiniteScroll: true,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _currentIndex = index;
                        });
                      },
                    ),
                    items: blogs.map((blog) {
                      return Builder(
                        builder: (context) => _buildBlogCard(blog, screenWidth, isSmallScreen),
                      );
                    }).toList(),
                  ),
                ),
              ),
              if (blogs.isNotEmpty) ...[
                SizedBox(height: screenWidth * 0.04),
                _buildPageIndicator(blogs, screenWidth),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
            strokeWidth: 3,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading blogs...',
            style: GoogleFonts.poppins(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(AppLocalizations localizations) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.06), // 24px equivalent
      padding: EdgeInsets.all(screenWidth * 0.06),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            color: Colors.grey,
            size: screenWidth * 0.12, // 48px equivalent
          ),
          SizedBox(height: screenWidth * 0.04),
          Text(
            localizations.translate('no_blogs'),
            style: GoogleFonts.poppins(
              color: Colors.grey[600],
              fontSize: screenWidth * 0.04, // 16px equivalent
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBlogCard(BlogModel blog, double screenWidth, bool isSmallScreen) {
    return GestureDetector(
      onTap: () {
        NavigationService.navigateToBlogDetail(
          blogId: blog.id,
          blogTitle: blog.title,
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.02), // 8px equivalent
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(screenWidth * 0.05), // 20px equivalent
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              child: Stack(
                children: [
                  blog.image1 != null
                      ? Image.network(
                    blog.image1!,
                    height: screenWidth * 0.35, // 140px equivalent
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => _buildDefaultImage(screenWidth),
                  )
                      : _buildDefaultImage(screenWidth),
                  Positioned(
                    top: screenWidth * 0.03, // 12px equivalent
                    right: screenWidth * 0.03,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.02,
                        vertical: screenWidth * 0.01,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(screenWidth * 0.03),
                      ),
                      child: Text(
                        'NEW',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: isSmallScreen ? 8 : 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(screenWidth * 0.04), // 16px equivalent
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: AutoSizeText(
                        blog.title,
                        style: GoogleFonts.poppins(
                          fontSize: isSmallScreen ? 14 : 16,
                          fontWeight: FontWeight.w600,
                          height: 1.3,
                          color: Colors.black87,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        minFontSize: 12,
                      ),
                    ),
                    SizedBox(height: screenWidth * 0.03), // 12px equivalent
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: isSmallScreen ? 12 : 14,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: screenWidth * 0.01), // 4px equivalent
                        Text(
                          blog.createdTime != null
                              ? _formatDate(blog.createdTime!)
                              : 'Unknown date',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: isSmallScreen ? 10 : 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: EdgeInsets.all(screenWidth * 0.015), // 6px equivalent
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            size: isSmallScreen ? 10 : 12,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultImage(double screenWidth) {
    return Container(
      height: screenWidth * 0.35, // 140px equivalent
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red.withOpacity(0.7),
            Colors.red.withOpacity(0.5),
          ],
        ),
      ),
      child: Icon(
        Icons.article,
        color: Colors.white,
        size: screenWidth * 0.12, // 48px equivalent
      ),
    );
  }

  Widget _buildPageIndicator(List<BlogModel> blogs, double screenWidth) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: blogs.asMap().entries.map((entry) {
        int index = entry.key;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _currentIndex == index ? 24.0 : 8.0,
          height: 8.0,
          margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.01), // 4px equivalent
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.0),
            color: _currentIndex == index ? Colors.red : Colors.grey.withOpacity(0.4),
          ),
        );
      }).toList(),
    );
  }
}