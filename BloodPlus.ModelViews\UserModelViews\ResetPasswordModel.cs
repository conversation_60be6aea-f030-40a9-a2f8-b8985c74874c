﻿using System.ComponentModel.DataAnnotations;

namespace BloodPlus.ModelViews.UserModelViews
{
    public class ResetPasswordModel
    {
        public string Email { get; set; }
        [Required]
        [DataType(DataType.Password)]
        [StringLength(100, MinimumLength = 6)]
        public string NewPassword { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [Compare("NewPassword", ErrorMessage = "Mật khẩu xác nhận không khớp.")]
        public string VerifyPassword { get; set; }
    }
}
