import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/language_manager.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/core/widgets/dialog/success_dialog.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/data/services/user_service.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';
import 'package:app_settings/app_settings.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with TickerProviderStateMixin {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  String _selectedLanguageCode = 'vi';
  final List<String> _languageCodes = ['vi', 'en', 'zh', 'ko', 'th', 'es', 'ja'];
  bool _isLoading = true;
  bool _isChangeEmailLoading = false;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );
    _loadSavedSettings();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _handleChangeEmailTap() async {
    if (_isChangeEmailLoading) return; // Ngăn chặn nhiều lần nhấn

    setState(() {
      _isChangeEmailLoading = true;
    });

    final result = await NavigationService.navigateToChangeEmail();

    if (mounted) {
      setState(() {
        _isChangeEmailLoading = false;
      });

      if (result == false) { // Nếu kết quả là false, hiển thị dialog lỗi
        DialogHelper.showAnimatedErrorDialog(
          context: context,
          title: AppLocalizations.of(context).translate('error'),
          message: AppLocalizations.of(context).translate('network_connection_error'),
          buttonText: AppLocalizations.of(context).translate('ok'),
          onPressed: () {
            // Có thể thêm hành động khi nhấn nút OK nếu cần
          },
          icon: Icons.error_outline_rounded,
          iconColor: AppColors.primaryRed,
        );
      }
    }
  }

  Future<void> _loadSavedSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguageCode = prefs.getString('languageCode') ?? 'vi';
      final notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      final darkModeEnabled = prefs.getBool('dark_mode_enabled') ?? false;
      setState(() {
        _selectedLanguageCode = savedLanguageCode;
        _notificationsEnabled = notificationsEnabled;
        _darkModeEnabled = darkModeEnabled;
        _isLoading = false;
      });
      _fadeController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings(LanguageManager languageManager) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('languageCode', _selectedLanguageCode);
      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('dark_mode_enabled', _darkModeEnabled);

      await languageManager.changeLanguage(_selectedLanguageCode);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).translate('saved_settings'),
              style: AppTheme.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: AppTheme.primaryRed,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).translate('error')}: $e',
              style: AppTheme.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing
    final horizontalPadding = isSmallScreen ? 16.0 : (isLargeScreen ? 32.0 : 20.0);
    final verticalPadding = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final bodyFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final spacingSmall = isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);
    final spacingLarge = isSmallScreen ? 24.0 : (isLargeScreen ? 40.0 : 32.0);
    final cardBorderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final buttonPadding = EdgeInsets.symmetric(
      horizontal: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
      vertical: isSmallScreen ? 12.0 : (isLargeScreen ? 18.0 : 16.0),
    );

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
        title: Text(
          localizations.translate('settings'),
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: titleFontSize,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
            size: iconSize,
          ),
          onPressed: _isChangeEmailLoading ? null : () => NavigationService.goBack(),
        ),
      ),
      body: AbsorbPointer(
        absorbing: _isChangeEmailLoading,
        child: Stack(
          children: [
            _isLoading
                ? _buildLoadingIndicator(context, isSmallScreen, isLargeScreen)
                : FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: horizontalPadding,
                  vertical: verticalPadding,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.translate('general'),
                      style: AppTheme.headingMedium.copyWith(
                        color: AppTheme.textPrimary,
                        fontSize: titleFontSize,
                      ),
                    ),
                    SizedBox(height: spacingSmall),
                    _buildSwitchTile(
                      title: localizations.translate('enable_notifications'),
                      value: _notificationsEnabled,
                      onChanged: (value) {
                        setState(() {
                          _notificationsEnabled = value;
                        });
                      },
                      isSmallScreen: isSmallScreen,
                      isLargeScreen: isLargeScreen,
                    ),
                    _buildDivider(isSmallScreen, isLargeScreen),
                    Text(
                      localizations.translate('language'),
                      style: AppTheme.headingMedium.copyWith(
                        color: AppTheme.textPrimary,
                        fontSize: titleFontSize,
                      ),
                    ),
                    SizedBox(height: spacingSmall),
                    _buildLanguageDropdown(context, isSmallScreen, isLargeScreen),
                    _buildDivider(isSmallScreen, isLargeScreen),
                    Text(
                      localizations.translate('about'),
                      style: AppTheme.headingMedium.copyWith(
                        color: AppTheme.textPrimary,
                        fontSize: titleFontSize,
                      ),
                    ),
                    SizedBox(height: spacingSmall),
                    _buildMenuItem(
                      context: context,
                      title: localizations.translate('privacy_policy'),
                      onTap: () {
                        DialogHelper.showAnimatedSuccessDialog(
                          context: context,
                          title: localizations.translate('privacy_policy_title'),
                          message: localizations.translate('privacy_policy_message'),
                          buttonText: localizations.translate('close'),
                          icon: Icons.shield,
                          iconColor: AppTheme.primaryRed,
                          onPressed: () => NavigationService.goBack(),
                        );
                      },
                      isSmallScreen: isSmallScreen,
                      isLargeScreen: isLargeScreen,
                      isLoading: false,
                    ),
                    _buildMenuItem(
                      context: context,
                      title: localizations.translate('change_password'),
                      onTap: () => _showChangePasswordDialog(context),
                      isSmallScreen: isSmallScreen,
                      isLargeScreen: isLargeScreen,
                      isLoading: false,
                    ),
                    _buildMenuItem(
                      context: context,
                      title: localizations.translate('change_email'),
                      onTap: _isChangeEmailLoading ? null : _handleChangeEmailTap,
                      isSmallScreen: isSmallScreen,
                      isLargeScreen: isLargeScreen,
                      isLoading: _isChangeEmailLoading,
                    ),
                    SizedBox(height: spacingLarge),
                    Center(
                      child: CustomButton(
                        text: localizations.translate('save_settings'),
                        color: AppTheme.primaryRed,
                        textColor: Colors.white,
                        onPressed: () => _saveSettings(languageManager),
                        padding: buttonPadding,
                        borderRadius: cardBorderRadius,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (_isChangeEmailLoading)
              Container(
                color: Colors.black.withOpacity(0.5),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
                    strokeWidth: isSmallScreen ? 3.0 : 4.0,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator(BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final iconSize = isSmallScreen ? 30.0 : (isLargeScreen ? 50.0 : 40.0);
    final fontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final spacing = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
            strokeWidth: isSmallScreen ? 3.0 : 4.0,
          ),
          SizedBox(height: spacing),
          Text(
            AppLocalizations.of(context).translate('loading'),
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.textPrimary,
              fontSize: fontSize,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
    required bool isSmallScreen,
    required bool isLargeScreen,
  }) {
    final fontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final padding = EdgeInsets.symmetric(
      horizontal: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
      vertical: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
    );
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
      color: AppTheme.cardColor,
      child: SwitchListTile(
        title: Text(
          title,
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontSize: fontSize,
          ),
        ),
        value: value,
        activeColor: AppTheme.primaryRed,
        inactiveTrackColor: AppTheme.borderColor,
        onChanged: onChanged,
        contentPadding: padding,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
      ),
    );
  }

  Widget _buildLanguageDropdown(BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final localizations = AppLocalizations.of(context);
    final fontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final padding = EdgeInsets.symmetric(
      horizontal: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
      vertical: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
    );
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);

    final languageDisplayNames = {
      'vi': localizations.translate('language_vietnamese'),
      'en': localizations.translate('language_english'),
      'zh': localizations.translate('language_chinese'),
      'ko': localizations.translate('language_korean'),
      'th': localizations.translate('language_thai'),
      'es': localizations.translate('language_spanish'),
      'ja': localizations.translate('language_japanese'),
    };

    final languageFlagAssets = {
      'vi': 'assets/icons/vietnam.png',
      'en': 'assets/icons/united-kingdom.png',
      'zh': 'assets/icons/china.png',
      'ko': 'assets/icons/south-korea.png',
      'th': 'assets/icons/thailand.png',
      'es': 'assets/icons/spain.png',
      'ja': 'assets/icons/japan.png',
    };

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
      color: AppTheme.cardColor,
      child: Padding(
        padding: padding,
        child: DropdownButtonFormField<String>(
          value: _selectedLanguageCode,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).translate('language'),
            labelStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary, fontSize: fontSize),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius - 2),
              borderSide: BorderSide(color: AppTheme.borderColor, width: 1.5),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius - 2),
              borderSide: BorderSide(color: AppTheme.borderColor, width: 1.5),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius - 2),
              borderSide: BorderSide(color: AppTheme.primaryRed, width: 2),
            ),
            filled: true,
            fillColor: AppTheme.cardColor,
            contentPadding: EdgeInsets.symmetric(vertical: padding.vertical / 2, horizontal: padding.horizontal),
          ),
          style: AppTheme.bodyLarge.copyWith(color: AppTheme.textPrimary, fontSize: fontSize),
          icon: Icon(Icons.arrow_drop_down, color: AppTheme.primaryRed, size: iconSize),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedLanguageCode = newValue;
              });
            }
          },
          items: _languageCodes.map<DropdownMenuItem<String>>((String language) {
            return DropdownMenuItem<String>(
              value: language,
              child: Row(
                children: [
                  Image.asset(
                    languageFlagAssets[language] ?? 'assets/icons/vietnam.png',
                    width: 24,
                    height: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(languageDisplayNames[language] ?? language, style: TextStyle(fontSize: fontSize)),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required BuildContext context,
    required String title,
    required VoidCallback? onTap,
    required bool isSmallScreen,
    required bool isLargeScreen,
    required bool isLoading,
  }) {
    final fontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final padding = EdgeInsets.symmetric(
      horizontal: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
      vertical: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
    );
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
      color: AppTheme.cardColor,
      child: ListTile(
        title: Text(
          title,
          style: AppTheme.bodyLarge.copyWith(
            color: isLoading || onTap == null ? AppTheme.textSecondary : AppTheme.textPrimary,
            fontSize: fontSize,
          ),
        ),
        trailing: isLoading
            ? SizedBox(
          width: iconSize,
          height: iconSize,
          child: CircularProgressIndicator(
            strokeWidth: isSmallScreen ? 2.0 : 3.0,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
          ),
        )
            : Icon(
          Icons.chevron_right,
          color: onTap == null ? AppTheme.textSecondary.withOpacity(0.5) : AppTheme.textSecondary,
          size: iconSize,
        ),
        onTap: onTap,
        contentPadding: padding,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
        enabled: !isLoading && onTap != null,
      ),
    );
  }

  Widget _buildDivider(bool isSmallScreen, bool isLargeScreen) {
    final indent = isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
      child: Divider(
        color: AppTheme.borderColor,
        thickness: 1,
        indent: indent,
        endIndent: indent,
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ChangePasswordDialog(),
    );
  }
}

class ChangePasswordDialog extends StatefulWidget {
  @override
  _ChangePasswordDialogState createState() => _ChangePasswordDialogState();
}

class _ChangePasswordDialogState extends State<ChangePasswordDialog> with SingleTickerProviderStateMixin {
  final TextEditingController _currentPasswordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _verifyPasswordController = TextEditingController();
  bool _obscureCurrent = true;
  bool _obscureNew = true;
  bool _obscureVerify = true;
  bool _isLoading = false;
  String? _errorText;

  // Password strength
  late AnimationController _strengthController;
  late Animation<double> _strengthAnimation;
  bool _hasMinLength = false;
  bool _hasUpperCase = false;
  bool _hasLowerCase = false;
  bool _hasNumber = false;
  bool _hasSpecialChar = false;

  // Color scheme
  static const Color primaryRed = Color(0xFFE53E3E);
  static const Color lightRed = Color(0xFFFED7D7);
  static const Color darkRed = Color(0xFFC53030);
  static const Color backgroundWhite = Color(0xFFFFFFFF);
  static const Color lightGray = Color(0xFFF7FAFC);
  static const Color borderGray = Color(0xFFE2E8F0);
  static const Color textGray = Color(0xFF4A5568);
  static const Color successGreen = Color(0xFF38A169);
  static const Color warningOrange = Color(0xFFDD6B20);

  @override
  void initState() {
    super.initState();
    _strengthController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _strengthAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _strengthController, curve: Curves.easeOut),
    );
    _newPasswordController.addListener(_checkPasswordStrength);
  }

  @override
  void dispose() {
    _strengthController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _verifyPasswordController.dispose();
    super.dispose();
  }

  void _checkPasswordStrength() {
    final password = _newPasswordController.text;
    setState(() {
      _hasMinLength = password.length >= 8;
      _hasUpperCase = password.contains(RegExp(r'[A-Z]'));
      _hasLowerCase = password.contains(RegExp(r'[a-z]'));
      _hasNumber = password.contains(RegExp(r'[0-9]'));
      _hasSpecialChar = password.contains(RegExp(r'[!@#\$%^&*(),.?":{}|<>]'));
    });
    final strength = _getPasswordStrength();
    _strengthController.animateTo(strength / 5);
  }

  int _getPasswordStrength() {
    int strength = 0;
    if (_hasMinLength) strength++;
    if (_hasUpperCase) strength++;
    if (_hasLowerCase) strength++;
    if (_hasNumber) strength++;
    if (_hasSpecialChar) strength++;
    return strength;
  }

  Color _getStrengthColor() {
    final strength = _getPasswordStrength();
    if (strength <= 2) return primaryRed;
    if (strength <= 3) return warningOrange;
    if (strength <= 4) return Colors.amber[600]!;
    return successGreen;
  }

  String _getStrengthText(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final strength = _getPasswordStrength();
    if (strength <= 2) return localizations.translate('password_weak');
    if (strength <= 3) return localizations.translate('password_medium');
    if (strength <= 4) return localizations.translate('password_strong');
    return localizations.translate('password_very_strong');
  }

  Widget _buildPasswordStrengthIndicator(BuildContext context) {
    return AnimatedBuilder(
      animation: _strengthAnimation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.only(top: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: lightGray,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: borderGray, width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.security,
                    size: 16,
                    color: _getStrengthColor(),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context).translate('password_strength'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: textGray,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _getStrengthText(context),
                    style: TextStyle(
                      fontSize: 14,
                      color: _getStrengthColor(),
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(3),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _strengthAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      color: _getStrengthColor(),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildRequirementChip(AppLocalizations.of(context).translate('min_8_characters'), _hasMinLength),
                  _buildRequirementChip(AppLocalizations.of(context).translate('uppercase_letter'), _hasUpperCase),
                  _buildRequirementChip(AppLocalizations.of(context).translate('lowercase_letter'), _hasLowerCase),
                  _buildRequirementChip(AppLocalizations.of(context).translate('number'), _hasNumber),
                  _buildRequirementChip(AppLocalizations.of(context).translate('special_character'), _hasSpecialChar),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRequirementChip(String text, bool isMet) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isMet ? successGreen.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isMet ? successGreen : Colors.grey[400]!,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 14,
            color: isMet ? successGreen : Colors.grey[400],
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              color: isMet ? successGreen : Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    String? helperText,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: borderGray, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        style: TextStyle(
          fontSize: 16,
          color: textGray,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: textGray,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          helperText: helperText,
          helperStyle: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          suffixIcon: IconButton(
            icon: Icon(
              obscureText ? Icons.visibility_outlined : Icons.visibility_off_outlined,
              color: primaryRed,
              size: 20,
            ),
            onPressed: onToggleVisibility,
          ),
        ),
      ),
    );
  }

  Future<void> _handleChangePassword() async {
    setState(() => _errorText = null);
    final currentPassword = _currentPasswordController.text;
    final newPassword = _newPasswordController.text;
    final verifyPassword = _verifyPasswordController.text;
    final localizations = AppLocalizations.of(context);

    if (newPassword != verifyPassword) {
      setState(() => _errorText = localizations.translate('password_not_match'));
      return;
    }
    if (_getPasswordStrength() < 3) {
      setState(() => _errorText = localizations.translate('password_too_weak'));
      return;
    }

    setState(() => _isLoading = true);
    String? token;
    try {
      token = await UserManager().getUserToken();
    } catch (e) {
      token = null;
    }

    if (token == null) {
      setState(() {
        _isLoading = false;
        _errorText = localizations.translate('not_logged_in');
      });
      return;
    }

    final userService = UserService();
    final errorMsg = await userService.changePassword(
      token: token,
      currentPassword: currentPassword,
      newPassword: newPassword,
      verifyPassword: verifyPassword,
    );

    setState(() => _isLoading = false);

    if (errorMsg == null || errorMsg.isEmpty) {
      Navigator.of(context).pop();
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => SuccessDialog(
          icon: Icons.check_circle_rounded,
          iconColor: successGreen,
          title: localizations.translate('change_password_success'),
          message: localizations.translate('change_password_success'),
          buttonText: localizations.translate('ok'),
          onButtonPressed: () {
            Navigator.of(context).pop(); // đóng dialog
            // Nếu muốn quay về Home hoặc một màn khác, thêm navigator.pushReplacement ở đây
          },
        ),
      );
    } else {
      String friendlyError = errorMsg;
      final lowerMsg = errorMsg.toLowerCase();
      if (lowerMsg.contains('mật khẩu hiện tại không đúng') || lowerMsg.contains('current password is incorrect')) {
        friendlyError = localizations.translate('current_password_incorrect');
      } else if (lowerMsg.contains('not match') || lowerMsg.contains('không khớp')) {
        friendlyError = localizations.translate('password_not_match');
      } else if (lowerMsg.contains('weak') || lowerMsg.contains('quá yếu')) {
        friendlyError = localizations.translate('password_too_weak');
      } else if (lowerMsg.contains('required') || lowerMsg.contains('điền')) {
        friendlyError = localizations.translate('please_fill_all_fields');
      } else if (lowerMsg.contains('không tồn tại') || lowerMsg.contains('not exist')) {
        friendlyError = localizations.translate('user_not_found');
      } else if (errorMsg.length > 100) {
        friendlyError = localizations.translate('system_error');
      }
      setState(() => _errorText = friendlyError);
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 8,
      backgroundColor: Colors.white,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [primaryRed, darkRed],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.lock_outline,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.translate('change_password'),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Cập nhật mật khẩu để bảo mật tài khoản',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Error message
                  if (_errorText != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: lightRed,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: primaryRed.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: primaryRed, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorText!,
                              style: TextStyle(
                                color: primaryRed,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Current password field
                  _buildPasswordField(
                    controller: _currentPasswordController,
                    label: localizations.translate('current_password'),
                    obscureText: _obscureCurrent,
                    onToggleVisibility: () => setState(() => _obscureCurrent = !_obscureCurrent),
                  ),

                  const SizedBox(height: 16),

                  // New password field
                  _buildPasswordField(
                    controller: _newPasswordController,
                    label: localizations.translate('new_password'),
                    obscureText: _obscureNew,
                    onToggleVisibility: () => setState(() => _obscureNew = !_obscureNew),
                  ),

                  // Password strength indicator
                  if (_newPasswordController.text.isNotEmpty)
                    _buildPasswordStrengthIndicator(context),

                  const SizedBox(height: 16),

                  // Verify password field
                  _buildPasswordField(
                    controller: _verifyPasswordController,
                    label: localizations.translate('retype_password'),
                    obscureText: _obscureVerify,
                    onToggleVisibility: () => setState(() => _obscureVerify = !_obscureVerify),
                  ),

                  const SizedBox(height: 32),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: primaryRed, width: 1.5),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            localizations.translate('cancel'),
                            style: TextStyle(
                              color: primaryRed,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _handleChangePassword,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryRed,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: _isLoading
                              ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                              : Text(
                            localizations.translate('confirm'),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}