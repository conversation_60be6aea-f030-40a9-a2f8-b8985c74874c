﻿using BloodPlus.ModelViews.ChatGPT;
using BloodPlus.ModelViews.Gemini;
using BloodPlus.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace BloodPlus.Services.Services
{
    public class ChatGPTService : IChatGPTService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;

        public ChatGPTService(IHttpClientFactory httpFactory, IConfiguration config)
        {
            _httpClient = httpFactory.CreateClient();
            _apiKey = config["OpenAI:ApiKey"] ?? throw new Exception("Missing OpenAI:ApiKey");

            _httpClient.BaseAddress = new Uri("https://api.openai.com/v1/");
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
        }

        // Chat mới — có system prompt + thông tin location, name, bloodType
        public async Task<string> GetCompletionAsync(string userPrompt, string location, string userName, string bloodType)
        {
            var messages = new List<ChatAIMessage>
            {
                new ChatAIMessage
                {
                    Role = "user",
                    Content = $"User location: {location}\n" +
                              $"User name: {userName}\n" +
                              $"User blood type: {bloodType}\n\n" +
                              $"{userPrompt}"
                }
            };

            messages.Insert(0, new ChatAIMessage
            {
                Role = "system",
                Content = "You are a healthcare technology expert specializing in blood donation apps. " +
                          "Please advise on implementing an AI feature that answers user queries about health, blood types, " +
                          "and nearby donation locations with distance, while dynamically responding in the user's preferred language."
            });

            return await GetCompletionWithHistoryAsync(userPrompt, messages);
        }

        // Chat có lịch sử — danh sách message giữ nguyên, chỉ gọi API
        public async Task<string> GetCompletionWithHistoryAsync(string userPrompt, List<ChatAIMessage> messages)
        {
            var request = new ChatGPTRequest
            {
                Model = "gpt-4o",
                Messages = messages,
                Temperature = 0.7f,
                MaxTokens = 1024
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("chat/completions", content);

            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception($"OpenAI error: {error}");
            }

            var responseBody = await response.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<ChatGPTResponse>(responseBody);

            return parsed?.Choices.FirstOrDefault()?.Message.Content?.Trim()
                   ?? "(Không có phản hồi từ GPT)";
        }
    }
}
