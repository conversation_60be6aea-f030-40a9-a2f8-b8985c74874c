import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

enum Environment {
  development,
  staging,
  production,
}

enum DeviceType {
  androidEmulator,
  androidReal,
  ios,
  web,
}

class ApiConfig {
  static const String _localWifiIp = '************';
  // static const String _localWifiIp = '*************';
  static const String _apiPort = '7026';
  static const String _frontendPort = '3000';
  static const Environment _currentEnvironment = Environment.production;

  static const String _androidEmulatorBaseUrl = 'https://********:$_apiPort';
  static const String _realDeviceBaseUrl = 'https://$_localWifiIp:$_apiPort';
  static const String _webBaseUrl = 'https://localhost:$_apiPort';
  static const String _productionBaseUrl = 'https://bloodplus.duckdns.org';

  static String get baseUrl {
    switch (_currentEnvironment) {
      case Environment.production:
        return _productionBaseUrl;
      case Environment.staging:
        return _realDeviceBaseUrl;
      case Environment.development:
      default:
        return _getDevBaseUrl();
    }
  }

  //static String get apiUrl => '$baseUrl/api';
  static String get apiUrl => '$_androidEmulatorBaseUrl/api';

  static String get frontendUrl => _currentEnvironment == Environment.production
      ? 'https://bloodplus.duckdns.org'
      : 'http://$_localWifiIp:$_frontendPort';

  static DeviceType get currentDeviceType {
    if (kIsWeb) return DeviceType.web;
    if (Platform.isAndroid) {
      return _isAndroidEmulator() ? DeviceType.androidEmulator : DeviceType.androidReal;
    }
    if (Platform.isIOS) return DeviceType.ios;
    throw Exception('Unsupported platform');
  }

  static String _getDevBaseUrl() {
    switch (currentDeviceType) {
      case DeviceType.androidEmulator:
        return _androidEmulatorBaseUrl;
      case DeviceType.androidReal:
        return _realDeviceBaseUrl;
      case DeviceType.web:
        return _webBaseUrl;
      case DeviceType.ios:
        return _realDeviceBaseUrl;
    }
  }

  static bool _isAndroidEmulator() {
    return Platform.isAndroid &&
        (Platform.environment['ANDROID_EMULATOR'] == 'true' ||
            Platform.environment.containsKey('ANDROID_AVD_HOME'));
  }

  static const String authLogin = '/auth/auth-account';
  static const String authGoogleLogin = '/auth/login-google';
  static const String authRegister = '/auth/create-account';
  static const String authVerifyPhone = '/auth/verify-phonenumber';
  static const String authCreateAccountOtp = '/auth/create-account';
  static const String userProfile = '/user/profile';
  static const String userUpdateProfile = '/user/update-profile';
  static const String bloodDonations = '/blood-donations';
  static const String bloodRequests = '/blood-requests';
  static const String appointment = '/appointment';
  static const String blog = '/blog';
  static const String donationEvent = '/donationevent';
  static const String voucher = '/voucher';
  static const String voucherForUser = '/voucher/for-user';
  static const String voucherOfUser = '/voucher/of-user';
  static const String notification = '/notification';
  static const String authRegisterAccount = '/auth/register-account';
  static const String authVerifyOtp = '/auth/verify-otp';
  static const String authForgotPassword = '/auth/forget-password';
  static const String authVerifyOtpPassword = '/auth/verify-otp-password';
  static const String authResetPassword = '/auth/reset-password';
  static const String userChangePassword = '/user/change-password';
  static const String userDaysWaiting = '/user/day-watting';
  static const String userRanking = '/user/ranking';
  static const String userChangeEmail = '/user/request-update-email';
  static const String userConfirmOTP = '/user/verify-otp-email';
  static const String userUpdateEmail = '/user/update-email';
  static const String emergencyDonation = '/emergencydonation';
  static const String bloodType = '/bloodtype';
  static const String organizationByUserId = '/organization/userid';


  static final HttpClient _httpClient = HttpClient();
  static final IOClient _client = IOClient(_httpClient);
  static IOClient get httpClient => _client;

  static Future<http.Response> get(
      String endpoint, {
        Map<String, String>? headers,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.get(
      url,
      headers: _buildHeaders(headers),
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> post(
      String endpoint, {
        Map<String, String>? headers,
        Map<String, dynamic>? body,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.post(
      url,
      headers: _buildHeaders(headers),
      body: body != null ? jsonEncode(body) : null,
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> put(
      String endpoint, {
        Map<String, String>? headers,
        Map<String, dynamic>? body,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.put(
      url,
      headers: _buildHeaders(headers),
      body: body != null ? jsonEncode(body) : null,
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> delete(
      String endpoint, {
        Map<String, String>? headers,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.delete(
      url,
      headers: _buildHeaders(headers),
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> patch(
      String endpoint, {
        Map<String, String>? headers,
        Map<String, dynamic>? body,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.patch(
      url,
      headers: _buildHeaders(headers),
      body: body != null ? jsonEncode(body) : null,
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> multipartPost(
      String endpoint, {
        required String token,
        Map<String, String>? fields,
        Map<String, http.MultipartFile>? files,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    final request = http.MultipartRequest('POST', url);

    request.headers.addAll(_buildAuthHeaders(token, null));
    if (fields != null) request.fields.addAll(fields);
    if (files != null) request.files.addAll(files.values);

    final streamedResponse = await _client.send(request);
    return await http.Response.fromStream(streamedResponse)
        .timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> multipartPatch(
      String endpoint, {
        required String token,
        Map<String, String>? fields,
        Map<String, http.MultipartFile>? files,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    final request = http.MultipartRequest('PATCH', url);

    request.headers.addAll(_buildAuthHeaders(token, null));
    if (fields != null) request.fields.addAll(fields);
    if (files != null) request.files.addAll(files.values);

    final streamedResponse = await _client.send(request);
    return await http.Response.fromStream(streamedResponse)
        .timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Uri _buildUrl(String endpoint, Map<String, dynamic>? queryParameters) {
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/$endpoint';
    final uri = Uri.parse('$apiUrl$cleanEndpoint');

    if (queryParameters != null) {
      final queryMap = queryParameters.map((key, value) => MapEntry(key, value.toString()));
      return uri.replace(queryParameters: queryMap);
    }

    return uri;
  }

  static Map<String, String> _buildHeaders(Map<String, String>? customHeaders) {
    final defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (customHeaders != null) {
      defaultHeaders.addAll(customHeaders);
    }

    return defaultHeaders;
  }

  static Map<String, String> _buildAuthHeaders(String token, Map<String, String>? customHeaders) {
    final headers = _buildHeaders(customHeaders);
    headers['Authorization'] = 'Bearer $token';
    return headers;
  }

  static Future<http.Response> authenticatedGet(
      String endpoint, {
        required String token,
        Map<String, String>? headers,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    print('Token: $token');
    return await _client.get(
      url,
      headers: _buildAuthHeaders(token, headers),
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> authenticatedPost(
      String endpoint, {
        required String token,
        Map<String, String>? headers,
        Map<String, dynamic>? body,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.post(
      url,
      headers: _buildAuthHeaders(token, headers),
      body: body != null ? jsonEncode(body) : null,
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> authenticatedPut(
      String endpoint, {
        required String token,
        Map<String, String>? headers,
        Map<String, dynamic>? body,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.put(
      url,
      headers: _buildAuthHeaders(token, headers),
      body: body != null ? jsonEncode(body) : null,
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> authenticatedDelete(
      String endpoint, {
        required String token,
        Map<String, String>? headers,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.delete(
      url,
      headers: _buildAuthHeaders(token, headers),
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static Future<http.Response> authenticatedPatch(
      String endpoint, {
        required String token,
        Map<String, String>? headers,
        Map<String, dynamic>? body,
        Map<String, dynamic>? queryParameters,
      }) async {
    final url = _buildUrl(endpoint, queryParameters);
    return await _client.patch(
      url,
      headers: _buildAuthHeaders(token, headers),
      body: body != null ? jsonEncode(body) : null,
    ).timeout(const Duration(seconds: 10), onTimeout: () {
      throw Exception('Request timed out');
    });
  }

  static String getFullUrl(String endpoint) {
    if (endpoint.isEmpty || endpoint == '/') return apiUrl;
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/$endpoint';
    return '$apiUrl$cleanEndpoint';
  }

  static Future<bool> checkServerConnection() async {
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse(baseUrl));
      final response = await request.close();
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  static String userDetailEndpoint(String userId) => '/user/$userId';

  static String userRankingDetailEndpoint(String id) => '/user/ranking/$id';
}

extension ApiConfigExtension on String {
  String get fullApiUrl => ApiConfig.getFullUrl(this);
}