import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/donation_event_model.dart';
import 'package:bloodplusmobile/data/repositories/donation_event_response.dart';

class DonationEventService {
  final UserManager _userManager = UserManager();

  Future<DonationEventResponse> getDonationEvents({
    String? location,
    String? startDate,
    String? endDate,
    String? organization,
    int pageNumber = 1,
    int pageSize = 5,
  }) async {
    final token = (await _userManager.getUserToken()) ?? '';
    final queryParameters = {
      'location': location,
      'startDate': startDate,
      'endDate': endDate,
      'organization': organization,
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
    };

    queryParameters.removeWhere((key, value) => value == null);

    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.donationEvent,
        token: token,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return DonationEventResponse.fromJson(data);
      } else {
        throw Exception('Failed to fetch event list: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<DonationEvent> getDonationEventById(String id) async {
    final token = (await _userManager.getUserToken()) ?? '';

    try {
      final response = await ApiConfig.authenticatedGet(
        '${ApiConfig.donationEvent}/$id',
        token: token,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final message = data['Message'] ?? data['message'];
        return DonationEvent.fromJson(message);
      } else {
        throw Exception('Failed to fetch event detail: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }
}