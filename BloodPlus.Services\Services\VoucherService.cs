﻿using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.VoucherModelViews;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class VoucherService : IVoucherService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly FirebaseService _firebaseService;
        private readonly IMapper _mapper;

        public VoucherService(IUnitOfWork unitOfWork, FirebaseService firebaseService, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _firebaseService = firebaseService;
            _mapper = mapper;
        }

        public async Task<string> CreateVoucherAsync(CreateVoucherModelView model, string userId)
        {
            try
            {
                string? sponsorImageUrl = null;
                string? panelImage = null;

                var uploadTasks = new List<Task>
                {
                    model.SponsorImage != null ? Task.Run(async () =>
                    {
                        sponsorImageUrl = await _firebaseService.UploadImageAsync(model.SponsorImage);
                    }) : Task.CompletedTask,

                    model.Panel != null ? Task.Run(async () =>
                    {
                        panelImage = await _firebaseService.UploadImageAsync(model.Panel);
                    }) : Task.CompletedTask
                };

                await Task.WhenAll(uploadTasks);

                var voucherTasks = model.Vouchers.Select(async voucher =>
                {
                    string? voucherImageUrl = null;
                    if (voucher != null)
                    {
                        voucherImageUrl = await _firebaseService.UploadImageAsync(voucher);
                    }

                    var entity = new Voucher
                    {
                        VoucherName = model.VoucherName,
                        Type = model.Type,
                        Point = model.Point,
                        ExpiryDate = model.ExpiryDate,
                        VoucherImage = voucherImageUrl,
                        SponsorName = model.SponsorName,
                        SponsorImage = sponsorImageUrl,
                        Description = model.Description,
                        UserManual = model.UserManual,
                        Support = model.Support,
                        Panel = panelImage,
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now,
                    };

                    await _unitOfWork.GetRepository<Voucher>().InsertAsync(entity);
                });

                await Task.WhenAll(voucherTasks);

                await _unitOfWork.SaveAsync();

                return "Vouchers added successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }



        public async Task<string> DeleteVoucherAsync(string id, string userId)
        {
            var voucher = await _unitOfWork.GetRepository<Voucher>().GetByIdAsync(id);
            if (voucher == null || voucher.DeletedTime.HasValue)
            {
                throw new Exception("Voucher can not find or is deleted");
            }

            voucher.DeletedTime = DateTime.Now;
            voucher.DeletedBy = userId;

            await _unitOfWork.GetRepository<Voucher>().UpdateAsync(voucher);
            await _unitOfWork.SaveAsync();

            return "Voucher deleted successfully";
        }


        public async Task<VoucherModelView> GetVoucherByIdAsync(string id)
        {
            try
            {
                var voucher = await _unitOfWork.GetRepository<Voucher>()
                    .Entities
                    .FirstOrDefaultAsync(u => u.Id == id && !u.DeletedTime.HasValue);

                if (voucher == null)
                {
                    throw new Exception("can not find or organization is deleted");
                }

                var voucherModelView = _mapper.Map<VoucherModelView>(voucher);

                return voucherModelView;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }

        public async Task<DetailVoucherModel> GetDetailVoucherByIdAsync(string id)
        {
            try
            {
                var voucher = await _unitOfWork.GetRepository<Voucher>()
                    .Entities
                    .Where(u => u.Id == id && !u.DeletedTime.HasValue)
                    .Select(u => new DetailVoucherModel
                    {
                        VoucherName = u.VoucherName,
                        Type = u.Type,
                        SponsorName = u.SponsorName,
                        ExpiryDate = u.ExpiryDate,
                        SponsorImage = u.SponsorImage,
                        Description = u.Description,
                        UserManual = u.UserManual,
                        Support = u.Support,
                        Point = u.Point,
                        Panel = u.Panel
                    })
                    .FirstOrDefaultAsync();

                if (voucher == null)
                {
                    throw new Exception("Can not find or organization is deleted");
                }

                return voucher;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


        public async Task<BasePaginatedList<ListVoucherForAdmin>> GetAllVoucherForAdminAsync(int pageNumber, int pageSize, VoucherType? type)
        {
            var voucherQuery = _unitOfWork.GetRepository<Voucher>()
                .Entities
                .Where(v => !v.DeletedTime.HasValue);

            if (type.HasValue)
            {
                voucherQuery = voucherQuery.Where(v => v.Type == type.Value);
            }

            int totalCount = await voucherQuery.CountAsync();

            var result = await voucherQuery
                .OrderByDescending(v => v.CreatedTime)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .Select(v => new ListVoucherForAdmin
                {
                    Id = v.Id,
                    VoucherName = v.VoucherName,
                    Type = v.Type,
                    SponsorName = v.SponsorName,
                    SponsorImage = v.SponsorImage,
                    ExpiryDate = v.ExpiryDate,
                    Point = v.Point,
                    Description = v.Description,
                    Panel = v.Panel,
                    UsedBy = v.User != null ? v.User.UserName : null
                })
                .ToListAsync();

            return new BasePaginatedList<ListVoucherForAdmin>(result, totalCount, pageNumber, pageSize);
        }

        public async Task<BasePaginatedList<ListVoucherForUser>> GetAllVoucherForUserAsync(int pageNumber, int pageSize, VoucherType? type)
        {
            var voucherQuery = _unitOfWork.GetRepository<Voucher>()
                .Entities
                .Where(v => !v.DeletedTime.HasValue && !v.UserId.HasValue);

            if (type.HasValue)
            {
                voucherQuery = voucherQuery.Where(v => v.Type == type.Value);
            }

            int totalCount = await voucherQuery.CountAsync();

            var result = await voucherQuery
                .OrderByDescending(v => v.CreatedTime)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .Select(v => new ListVoucherForUser
                {
                    Id = v.Id,
                    VoucherName = v.VoucherName,
                    Type = v.Type,
                    SponsorName = v.SponsorName,
                    SponsorImage = v.SponsorImage,
                    ExpiryDate = v.ExpiryDate,
                    Point = v.Point,
                    Description = v.Description,
                    Panel = v.Panel,
                })
                .ToListAsync();

            return new BasePaginatedList<ListVoucherForUser>(result, totalCount, pageNumber, pageSize);
        }


        public async Task<BasePaginatedList<ListVoucherForUser>> GetAllVoucherOfUserAsync(int pageNumber, int pageSize, VoucherType? type, string userId)
        {
            var user = Guid.Parse(userId);
            var voucherQuery = _unitOfWork.GetRepository<Voucher>()
                .Entities
                .Where(v => !v.DeletedTime.HasValue && v.UserId == user);

            if (type.HasValue)
            {
                voucherQuery = voucherQuery.Where(v => v.Type == type.Value);
            }

            int totalCount = await voucherQuery.CountAsync();

            var result = await voucherQuery
                .OrderByDescending(v => v.CreatedTime)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .Select(v => new ListVoucherForUser
                {
                    Id = v.Id,
                    VoucherName = v.VoucherName,
                    Type = v.Type,
                    SponsorName = v.SponsorName,
                    SponsorImage = v.SponsorImage,
                    ExpiryDate = v.ExpiryDate,
                    Point = v.Point,
                    Description = v.Description
                })
                .ToListAsync();

            return new BasePaginatedList<ListVoucherForUser>(result, totalCount, pageNumber, pageSize);
        }



        public async Task<string> AddVoucherForUserAsync(string id, string userId)
        {
            Guid uId = Guid.Parse(userId);
            var voucher = _unitOfWork.GetRepository<Voucher>().Entities
                .FirstOrDefault(v => v.Id == id && !v.DeletedTime.HasValue && v.UserId == null);
            if (voucher == null)
            {
                throw new Exception("Voucher can not find or deleted");
            }

            var user = _unitOfWork.GetRepository<User>()
                .Entities
                .FirstOrDefault(v => v.Id == uId && !v.DeletedTime.HasValue);
            if (user.Point < voucher.Point)
            {
                throw new Exception("Don't have enough Point to get Voucher");
            }
            user.Point -= voucher.Point;

            voucher.UserId = Guid.Parse(userId);
            voucher.LastUpdatedBy = userId;
            voucher.LastUpdatedTime = DateTime.Now;

            await _unitOfWork.GetRepository<Voucher>().UpdateAsync(voucher);
            await _unitOfWork.GetRepository<User>().UpdateAsync(user);
            await _unitOfWork.SaveAsync();

            return "Add voucher for user successfully";
        }

        public async Task<string> UpdateVoucherAsync(string id, UpdateVoucherModelView model, string userId)
        {
            try
            {
                var voucherRepo = _unitOfWork.GetRepository<Voucher>();
                var userRepo = _unitOfWork.GetRepository<User>();

                var voucher = await voucherRepo.GetByIdAsync(id);
                if (voucher == null || voucher.DeletedTime.HasValue)
                {
                    throw new Exception("Cannot find or voucher is deleted");
                }

                // Nếu có thay đổi UserName thì tìm lại UserId tương ứng
                if (!string.IsNullOrWhiteSpace(model.UserName))
                {
                    var user = await userRepo.Entities
                        .Where(u => u.UserName == model.UserName && !u.DeletedTime.HasValue)
                        .FirstOrDefaultAsync();

                    if (user == null)
                    {
                        throw new Exception("User with provided UserName does not exist or was deleted.");
                    }

                    voucher.UserId = user.Id;
                }

                // Cập nhật các thông tin nếu có thay đổi
                if (!string.IsNullOrWhiteSpace(model.VoucherName))
                    voucher.VoucherName = model.VoucherName;

                if (model.Type.HasValue)
                    voucher.Type = model.Type.Value;

                if (!string.IsNullOrWhiteSpace(model.SponsorName))
                    voucher.SponsorName = model.SponsorName;

                if (model.ExpiryDate.HasValue)
                    voucher.ExpiryDate = model.ExpiryDate.Value;

                if (!string.IsNullOrWhiteSpace(model.Description))
                    voucher.Description = model.Description;

                if (!string.IsNullOrWhiteSpace(model.UserManual))
                    voucher.UserManual = model.UserManual;

                if (!string.IsNullOrWhiteSpace(model.Support))
                    voucher.Support = model.Support;

                if (model.Point != voucher.Point)
                    voucher.Point = (int)model.Point;

                voucher.LastUpdatedBy = userId;
                voucher.LastUpdatedTime = DateTimeOffset.UtcNow;

                await voucherRepo.UpdateAsync(voucher);
                await _unitOfWork.SaveAsync();

                return "Voucher updated successfully";
            }
            catch (Exception ex)
            {
                throw new Exception($"Error: {ex.Message}");
            }
        }


    }
}
