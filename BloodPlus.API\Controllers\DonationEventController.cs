﻿using BloodPlus.Core.Base;
using BloodPlus.ModelViews.DonationEventModelView;
using BloodPlus.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BloodPlus.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DonationEventController : ControllerBase
    {
        private readonly IDonationEventService _eventService;

        public DonationEventController(IDonationEventService donationEventService)
        {
            _eventService = donationEventService;
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPost]
        public async Task<ActionResult<string>> CreateDonationEvent(CreateDEModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _eventService.CreateDonationEventAsync(model, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("{id}")]
        public async Task<ActionResult<string>> DeleteDonationEvent(string id)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _eventService.DeleteDonationEventAsync(id, userId);

            return Ok(new { Message = result });
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<DonationEventModelView>> GetDonationEventById(string id)
        {
            var result = await _eventService.GetDonationEventByIdAsync(id);

            return Ok(new { Message = result });
        }


        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<ActionResult<BasePaginatedList<ListDEModelView>>> GetAllDonationEvent(string? location, DateOnly? startDate, 
                                                                                                DateOnly? endDate, string? organization, 
                                                                                                int pageNumber = 1, int pageSize = 5)
        {
            var result = await _eventService.GetAllDonationEventAsync(pageNumber, pageSize, location, startDate, endDate, organization);

            return Ok(new { Message = result });
        }


        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("{id}")]
        public async Task<ActionResult<string>> UpdateDonationEvent(string id, UpdateDEModelView model)
        {
            var userId = User.FindFirst("userId")?.Value;
            string result = await _eventService.UpdateDonationEventAsync(id, model, userId);

            return Ok(new { Message = result });
        }
    }
}
