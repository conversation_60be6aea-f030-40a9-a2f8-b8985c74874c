import 'dart:math';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:flutter/material.dart';

class CustomConfirmationDialog extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String message;
  final String cancelButtonText;
  final String confirmButtonText;
  final VoidCallback onCancel;
  final VoidCallback onConfirm;

  const CustomConfirmationDialog({
    super.key,
    this.icon = Icons.logout,
    this.iconColor = AppColors.primaryRed,
    required this.title,
    required this.message,
    required this.cancelButtonText,
    required this.confirmButtonText,
    required this.onCancel,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing
    final dialogWidth = min(screenSize.width * 0.85, 400.0);
    final padding = isSmallScreen ? 16.0 : (isLargeScreen ? 32.0 : 24.0);
    final iconSize = isSmallScreen ? 40.0 : (isLargeScreen ? 56.0 : 48.0);
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final messageFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final buttonHeight = isSmallScreen ? 48.0 : (isLargeScreen ? 60.0 : 54.0);
    final spacing = isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16)),
      elevation: 8,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 16.0 : 24.0,
        vertical: isSmallScreen ? 16.0 : 24.0,
      ),
      child: Container(
        width: dialogWidth,
        padding: EdgeInsets.all(padding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    iconColor.withOpacity(0.2),
                    iconColor.withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(color: iconColor.withOpacity(0.3), width: isSmallScreen ? 1 : 1.5),
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: iconColor,
                semanticLabel: 'Confirmation icon',
              ),
            ),
            SizedBox(height: spacing),
            Text(
              title,
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                fontFamily: 'Roboto',
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: spacing),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: messageFontSize,
                color: Colors.grey.shade600,
                fontFamily: 'Roboto',
                height: 1.5,
              ),
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: isSmallScreen ? 16 : 24),
            LayoutBuilder(
              builder: (context, constraints) {
                final availableWidth = constraints.maxWidth;
                final buttonWidth = (availableWidth - spacing) / 2;
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: CustomButton(
                        text: cancelButtonText,
                        color: Colors.grey.shade400,
                        textColor: Colors.black87,
                        onPressed: onCancel,
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 16 : 24,
                          vertical: isSmallScreen ? 10 : 14,
                        ),
                        borderRadius: isSmallScreen ? 8 : 10,
                        height: buttonHeight,
                        minWidth: buttonWidth,
                        textStyle: TextStyle(
                          fontSize: isSmallScreen ? 14 : (isLargeScreen ? 18 : 16),
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        maxLines: 2,
                      ),
                    ),
                    SizedBox(width: spacing),
                    Flexible(
                      child: CustomButton(
                        text: confirmButtonText,
                        color: iconColor,
                        onPressed: onConfirm,
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 16 : 24,
                          vertical: isSmallScreen ? 10 : 14,
                        ),
                        borderRadius: isSmallScreen ? 8 : 10,
                        height: buttonHeight,
                        minWidth: buttonWidth,
                        textStyle: TextStyle(
                          fontSize: isSmallScreen ? 14 : (isLargeScreen ? 18 : 16),
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}