class RegisterRequestModel {
  final String userName;
  final String password;
  final String verifyPassword;
  final String email;
  final String? phoneNumber;
  final String? bloodType;
  final String name;
  final String dateOfBirth;
  final String? address;
  final String? job;
  final int gender;
  final String? passportNumber;

  RegisterRequestModel({
    required this.userName,
    required this.password,
    required this.verifyPassword,
    required this.email,
    this.phoneNumber,
    this.bloodType,
    required this.name,
    required this.dateOfBirth,
    this.address,
    this.job,
    required this.gender,
    this.passportNumber,
  });

  Map<String, String> toMap() {
    return {
      'UserName': userName,
      'Password': password,
      'VerifyPassword': verifyPassword,
      'Email': email,
      'PhoneNumber': phoneNumber ?? '',
      if (bloodType != null) 'BloodType': bloodType!,
      'Name': name,
      'DateOfBirth': dateOfBirth,
      if (address != null) 'Address': address!,
      if (job != null) 'Job': job!,
      'Gender': gender.toString(),
      if (passportNumber != null) 'PassportNumber': passportNumber!,
    };
  }
}