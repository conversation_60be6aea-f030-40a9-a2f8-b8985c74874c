﻿using BloodPlus.Core.Enum;
namespace BloodPlus.ModelViews.EmergencyEventModelView
{
    public class EmergencyEventParticipantModelView
    {
        public string? UserName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? BloodType { get; set; }
        public BloodComponent BloodComponent { get; set; }
        public AppointmentStatus Status { get; set; }
    }
}