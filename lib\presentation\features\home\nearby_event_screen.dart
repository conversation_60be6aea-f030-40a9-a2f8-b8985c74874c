import 'dart:async';
import 'dart:convert';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:developer' as developer;
import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../data/models/donation_event_model.dart';
import 'package:http/http.dart' as http;

class NearbyEventScreen extends StatefulWidget {
  final List<DonationEvent> events;
  const NearbyEventScreen({required this.events, Key? key}) : super(key: key);

  @override
  State<NearbyEventScreen> createState() => _NearbyEventScreenState();
}

class _NearbyEventScreenState extends State<NearbyEventScreen> with TickerProviderStateMixin {
  LatLng? userLocation;
  List<Map<String, dynamic>> eventMarkers = [];
  bool _isLoading = true;
  final MapController _mapController = MapController();
  static const String _openCageApiKey = 'f0139ae6881b475d8bfacd801ec46d9b'; // Cage API
  static const double _maxDistanceKm = 50.0;
  static const int _cacheDurationMs = 24 * 60 * 60 * 1000;
  StreamSubscription<Position>? _positionStreamSubscription;
  LatLng? _lastKnownLocation;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    )..repeat(reverse: true);
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _init();
    _startLocationUpdates();
  }

  @override
  void dispose() {
    _positionStreamSubscription?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _init() async {
    try {
      userLocation = await _getUserLocation();
      _lastKnownLocation = userLocation;
      developer.log('User location retrieved: $userLocation');
      if (userLocation == null) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).translate('cannot_get_user_location')),
            ),
          );
        }
        return;
      }

      await _updateEventMarkers();
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).translate('data_load_error').replaceAll('{error}', e.toString()),
            ),
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateEventMarkers() async {
    eventMarkers.clear();
    for (var event in widget.events) {
      LatLng? latLng = await _geocodeAddress(event.location);
      if (latLng == null) {
        latLng = await _geocodeAddress('${event.location}, Việt Nam');
      }
      if (latLng == null) {
        continue;
      }

      final distance = _calculateDistance(userLocation!, latLng);
      if (distance > _maxDistanceKm) {
        continue;
      }

      eventMarkers.add({
        'event': event,
        'latLng': latLng,
        'distance': distance,
      });
    }

    eventMarkers.sort((a, b) => a['distance'].compareTo(b['distance']));
    setState(() {});
  }

  void _startLocationUpdates() {
    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 100, // Chỉ cập nhật khi di chuyển 100m
    );

    _positionStreamSubscription = Geolocator.getPositionStream(locationSettings: locationSettings).listen(
          (Position position) {
        final newLocation = LatLng(position.latitude, position.longitude);
        if (_lastKnownLocation != null) {
          final distanceMoved = _calculateDistance(_lastKnownLocation!, newLocation);
          if (distanceMoved < 1.0) return; // Bỏ qua nếu di chuyển dưới 1km
        }

        setState(() {
          userLocation = newLocation;
          _lastKnownLocation = newLocation;
          _isLoading = true;
        });

        developer.log('User location updated: $userLocation');
        _updateEventMarkers().then((_) {
          setState(() {
            _isLoading = false;
          });
          _centerOnUserLocation();
        });
      },
      onError: (e) {
        developer.log('Error in position stream: $e');
      },
    );
  }

  Future<LatLng?> _getUserLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        await Geolocator.openLocationSettings();
        return null;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return null;
        }
      }
      if (permission == LocationPermission.deniedForever) {
        return null;
      }
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      return LatLng(position.latitude, position.longitude);
    } catch (e) {
      return null;
    }
  }

  Future<LatLng?> _geocodeAddress(String address) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'geocode_$address';
      final timestampKey = 'geocode_timestamp_$address';
      final now = DateTime.now().millisecondsSinceEpoch;

      final cachedData = prefs.getString(cacheKey);
      final cachedTimestamp = prefs.getInt(timestampKey);

      if (cachedData != null && cachedTimestamp != null) {
        if (now - cachedTimestamp < _cacheDurationMs) {
          final data = jsonDecode(cachedData);
          final latLng = LatLng(data['lat'], data['lng']);
          developer.log('Using cached geocode for $address: $latLng');
          return latLng;
        } else {
          developer.log('Cache expired for $address');
        }
      }

      developer.log('Geocoding address with OpenCage: $address');
      final url = Uri.parse(
        'https://api.opencagedata.com/geocode/v1/json?q=${Uri.encodeComponent(address)}&key=$_openCageApiKey&limit=1&countrycode=vn',
      );
      final response = await http.get(url);
      developer.log('OpenCage response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['results'].isNotEmpty) {
          final lat = data['results'][0]['geometry']['lat'];
          final lng = data['results'][0]['geometry']['lng'];
          final latLng = LatLng(lat, lng);
          developer.log('Geocoded to Lat: $lat, Lng: $lng');

          await prefs.setString(
            cacheKey,
            jsonEncode({'lat': lat, 'lng': lng}),
          );
          await prefs.setInt(timestampKey, now);
          developer.log('Cached geocode for $address: $latLng');
          return latLng;
        } else {
          developer.log('No results found for $address');
          return null;
        }
      } else {
        developer.log('OpenCage API error: ${response.body}');
        return null;
      }
    } catch (e) {
      developer.log('Error in _geocodeAddress($address): $e');
      return null;
    }
  }

  double _calculateDistance(LatLng a, LatLng b) {
    const Distance distance = Distance();
    return distance.as(LengthUnit.Kilometer, a, b);
  }

  void _centerOnUserLocation() {
    if (userLocation != null) {
      _mapController.move(userLocation!, 12.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localizations.translate('nearby_event_title'),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: isSmallScreen ? 18 : 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryRed,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primaryRed, AppColors.primaryRed.withOpacity(0.8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          _isLoading || userLocation == null
              ? Center(
            child: CircularProgressIndicator(
              color: AppColors.primaryRed,
              strokeWidth: isSmallScreen ? 3 : 4,
            ),
          )
              : FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: userLocation!,
              initialZoom: 12.0,
              maxZoom: 18.0,
              minZoom: 5.0,
              interactiveFlags: InteractiveFlag.all & ~InteractiveFlag.rotate,
            ),
            children: [
              TileLayer(
                urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                subdomains: ['a', 'b', 'c'],
                userAgentPackageName: 'bloodplusmobile.production', // Thêm dòng này cho production
              ),
              CircleLayer(
                circles: [
                  CircleMarker(
                    point: userLocation!,
                    radius: _maxDistanceKm * 1000,
                    color: AppColors.primaryRed.withOpacity(0.15),
                    borderStrokeWidth: isSmallScreen ? 1 : 2,
                    borderColor: AppColors.primaryRed.withOpacity(0.6),
                    useRadiusInMeter: true,
                  ),
                ],
              ),
              MarkerLayer(
                markers: [
                  Marker(
                    point: userLocation!,
                    width: isSmallScreen ? 40 : 50,
                    height: isSmallScreen ? 40 : 50,
                    child: const Icon(
                      Icons.person_pin_circle,
                      color: Colors.blue,
                      size: 40,
                    ),
                  ),
                  ...eventMarkers.map((e) {
                    final event = e['event'] as DonationEvent;
                    return Marker(
                      point: e['latLng'],
                      width: isSmallScreen ? 36 : 44,
                      height: isSmallScreen ? 36 : 44,
                      child: GestureDetector(
                        onTap: () {
                          _showEventDetailDialog(context, event, e['latLng'], e['distance']);
                        },
                        child: AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _pulseAnimation.value,
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: event.isEmergency ? Colors.red.shade900 : Colors.red.shade700,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.red.withOpacity(0.5),
                                      blurRadius: isSmallScreen ? 8 : 12 * _pulseAnimation.value,
                                      spreadRadius: isSmallScreen ? 1 : 2 * _pulseAnimation.value,
                                    ),
                                  ],
                                  border: Border.all(color: Colors.white, width: isSmallScreen ? 1 : 2),
                                ),
                                child: const Center(
                                  child: Icon(Icons.bloodtype, color: Colors.white, size: 24),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    );
                  }).toList(),
                ],
              ),
            ],
          ),
          Positioned(
            bottom: isSmallScreen ? 20 : 45,
            right: isSmallScreen ? 15 : 25,
            child: FloatingActionButton(
              onPressed: _centerOnUserLocation,
              backgroundColor: AppColors.primaryRed,
              elevation: isSmallScreen ? 4 : 6,
              child: Icon(Icons.my_location, color: Colors.white, size: isSmallScreen ? 20 : 24),
            ),
          ),
        ],
      ),
    );
  }

  void _showEventDetailDialog(BuildContext context, DonationEvent event, LatLng latLng, double distance) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, anim1, anim2) => const SizedBox.shrink(),
      transitionBuilder: (context, anim1, anim2, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(
              parent: anim1,
              curve: Curves.easeOutCubic,
            ),
          ),
          child: Dialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20)),
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
              constraints: BoxConstraints(
                maxWidth: isSmallScreen ? screenWidth * 0.9 : 400,
                minWidth: isSmallScreen ? screenWidth * 0.8 : 300,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
                gradient: LinearGradient(
                  colors: [Colors.white, Colors.grey.shade50],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: isSmallScreen ? 10 : 20,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            event.title,
                            style: TextStyle(
                              fontSize: isSmallScreen ? 16 : 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (event.isEmergency)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 6 : 10,
                              vertical: isSmallScreen ? 4 : 6,
                            ),
                            margin: EdgeInsets.only(left: isSmallScreen ? 4 : 8),
                            decoration: BoxDecoration(
                              color: Colors.red.shade100,
                              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                            ),
                            child: Text(
                              localizations.translate('event_emergency'),
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                                fontSize: isSmallScreen ? 10 : 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 20),
                    _buildInfoRow(
                      Icons.location_on,
                      localizations.translate('event_location'),
                      event.location,
                      isSmallScreen,
                    ),
                    _buildInfoRow(
                      Icons.business,
                      localizations.translate('event_organization'),
                      event.organizationName,
                      isSmallScreen,
                    ),
                    _buildInfoRow(
                      Icons.calendar_today,
                      localizations.translate('event_day'),
                      event.getFormattedDate(),
                      isSmallScreen,
                    ),
                    _buildInfoRow(
                      Icons.access_alarm,
                      localizations.translate('event_time'),
                      event.getFormattedTime(),
                      isSmallScreen,
                    ),
                    _buildInfoRow(
                      Icons.people,
                      localizations.translate('event_required_donors'),
                      event.requiredDonors.toString(),
                      isSmallScreen,
                    ),
                    _buildInfoRow(
                      Icons.person_add,
                      localizations.translate('event_current_donors'),
                      event.currentDonors.toString(),
                      isSmallScreen,
                    ),
                    _buildInfoRow(
                      Icons.directions,
                      localizations.translate('event_distance'),
                      '${distance.toStringAsFixed(2)} km',
                      isSmallScreen,
                    ),
                    if (event.description.isNotEmpty) ...[
                      SizedBox(height: isSmallScreen ? 10 : 16),
                      Text(
                        localizations.translate('event_description'),
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: isSmallScreen ? 14 : 16,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 6 : 8),
                      Text(
                        event.description,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 12 : 14,
                          color: Colors.grey.shade700,
                          height: 1.5,
                        ),
                        maxLines: 5,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    SizedBox(height: isSmallScreen ? 16 : 24),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: CustomButton(
                            text: localizations.translate('close'),
                            color: Colors.grey.shade300,
                            textColor: Colors.black87,
                            onPressed: () => Navigator.of(context).pop(),
                            borderRadius: isSmallScreen ? 8 : 12,
                            height: isSmallScreen ? 56 : 60,
                            maxLines: 2,
                          ),
                        ),
                        SizedBox(width: isSmallScreen ? 8 : 12),
                        Expanded(
                          child: CustomButton(
                            text: localizations.translate('details'),
                            color: AppColors.primaryRed,
                            textColor: Colors.white,
                            onPressed: () {
                              Navigator.of(context).pop();
                              NavigationService.navigateToDonationEventDetail(
                                eventId: event.id,
                                eventName: event.title,
                              );
                            },
                            borderRadius: isSmallScreen ? 8 : 12,
                            height: isSmallScreen ? 56 : 60,
                            maxLines: 2,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, bool isSmallScreen) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 6 : 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: isSmallScreen ? 18 : 24, color: AppColors.primaryRed),
          SizedBox(width: isSmallScreen ? 8 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: isSmallScreen ? 2 : 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 14 : 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}