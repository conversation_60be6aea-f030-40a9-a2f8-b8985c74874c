import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// IdleTimer: <PERSON>u<PERSON>n lý thời gian không hoạt động của người dùng.
class IdleTimer {
  static final IdleTimer _instance = IdleTimer._internal();
  factory IdleTimer() => _instance;
  IdleTimer._internal();

  Timer? _timer;
  Duration timeout = const Duration(minutes: 15);
  VoidCallback? onTimeout;

  void start({Duration? customTimeout, VoidCallback? onTimeout}) {
    this.onTimeout = onTimeout ?? this.onTimeout;
    timeout = customTimeout ?? timeout;
    _reset();
  }

  void _reset() {
    _timer?.cancel();
    _timer = Timer(timeout, _handleTimeout);
  }

  void userActivityDetected() async {
    _reset();
    // Lưu thời gian hoạt động cuối cùng
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('last_active_time', DateTime.now().millisecondsSinceEpoch);
  }

  void _handleTimeout() {
    onTimeout?.call();
  }

  void stop() {
    _timer?.cancel();
    _timer = null;
  }
} 