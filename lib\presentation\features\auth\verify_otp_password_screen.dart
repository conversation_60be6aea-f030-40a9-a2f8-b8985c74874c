import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/data/services/auth_service.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class VerifyOtpPasswordScreen extends StatefulWidget {
  final String email;
  const VerifyOtpPasswordScreen({Key? key, required this.email}) : super(key: key);

  @override
  State<VerifyOtpPasswordScreen> createState() => _VerifyOtpPasswordScreenState();
}

class _VerifyOtpPasswordScreenState extends State<VerifyOtpPasswordScreen>
    with TickerProviderStateMixin {
  final List<TextEditingController> _otpControllers =
  List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes =
  List.generate(6, (index) => FocusNode());
  final AuthService _authService = AuthService();

  bool _isLoading = false;
  bool _canResend = false;
  int _countdown = 60;
  Timer? _timer;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _shakeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startCountdown();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _shakeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );

    _fadeController.forward();
    _slideController.forward();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() => _countdown--);
      } else {
        setState(() => _canResend = true);
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _fadeController.dispose();
    _slideController.dispose();
    _shakeController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty) {
      if (index < 5) {
        _focusNodes[index + 1].requestFocus();
      } else {
        _focusNodes[index].unfocus();
        _submit();
      }
    }
  }

  void _onBackspace(int index) {
    if (_otpControllers[index].text.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  String _getOtpCode() {
    return _otpControllers.map((controller) => controller.text).join();
  }

  void _clearOtp() {
    for (var controller in _otpControllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
  }

  void _submit() async {
    final otp = _getOtpCode();
    if (otp.length != 6) {
      _showError(AppLocalizations.of(context).translate('please_enter_6_digit_otp'));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await _authService.verifyOtpPassword(widget.email, otp);
      setState(() => _isLoading = false);

      if (success) {
        _showSuccessDialog();
        Future.delayed(const Duration(seconds: 1), () {
          NavigationService.navigateTo(AppRoutes.createNewPassword,
              arguments: {'email': widget.email});
        });
      } else {
        // Hiển thị thông báo lỗi OTP không chính xác hoặc hết hạn
        _showError(AppLocalizations.of(context).translate('otp_invalid_or_expired'));
        _shakeOtpFields();
        _clearOtp();
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showError(AppLocalizations.of(context).translate('error_occurred_try_again'));
      _shakeOtpFields();
    }
  }

  void _shakeOtpFields() {
    _shakeController.reset();
    _shakeController.forward();
  }

  void _resendOtp() async {
    if (!_canResend) return;

    setState(() {
      _canResend = false;
      _countdown = 60;
    });

    try {
      await _authService.forgotPassword(widget.email);
      _showSnackBar(AppLocalizations.of(context).translate('otp_resent'), isError: false);
      _startCountdown();
      _clearOtp();
    } catch (e) {
      _showSnackBar(AppLocalizations.of(context).translate('cannot_resend_otp'), isError: true);
      setState(() => _canResend = true);
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: AppTheme.cardDecoration,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).translate('otp_verify_success'),
                style: AppTheme.headingMedium,
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context).translate('redirect_to_create_new_password'),
                style: AppTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showError(String message) {
    _showSnackBar(message, isError: true);
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  String _formatEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email;

    final username = parts[0];
    final domain = parts[1];

    if (username.length <= 2) return email;

    final maskedUsername = username[0] +
        '*' * (username.length - 2) +
        username[username.length - 1];

    return '$maskedUsername@$domain';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: AppTheme.textPrimary,
              size: 16,
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),

                    // Header Section
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.primaryRed.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.verified_user,
                        color: Colors.white,
                        size: 36,
                      ),
                    ),

                    const SizedBox(height: 32),

                    Text(
                      AppLocalizations.of(context).translate('verify_otp'),
                      style: AppTheme.headingLarge,
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: AppLocalizations.of(context).translate('otp_sent_to') + '\n',
                            style: AppTheme.bodyMedium.copyWith(height: 1.5),
                          ),
                          TextSpan(
                            text: _formatEmail(widget.email),
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.primaryRed,
                              fontWeight: FontWeight.w600,
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 48),

                    // OTP Input Section
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: AppTheme.cardDecoration,
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('enter_otp'),
                            style: AppTheme.headingSmall,
                          ),

                          const SizedBox(height: 24),

                          // OTP Input Fields
                          AnimatedBuilder(
                            animation: _shakeAnimation,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(
                                  _shakeAnimation.value * 10 *
                                      (1 - _shakeAnimation.value),
                                  0,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: List.generate(6, (index) {
                                    return Container(
                                      width: 45,
                                      height: 55,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[50],
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: _focusNodes[index].hasFocus
                                              ? AppTheme.primaryRed
                                              : AppTheme.borderColor,
                                          width: _focusNodes[index].hasFocus ? 2 : 1.5,
                                        ),
                                        boxShadow: _focusNodes[index].hasFocus
                                            ? [
                                          BoxShadow(
                                            color: AppTheme.primaryRed.withOpacity(0.2),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ]
                                            : null,
                                      ),
                                      child: TextFormField(
                                        controller: _otpControllers[index],
                                        focusNode: _focusNodes[index],
                                        textAlign: TextAlign.center,
                                        keyboardType: TextInputType.number,
                                        maxLength: 1,
                                        style: AppTheme.headingMedium.copyWith(
                                          color: AppTheme.primaryRed,
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.digitsOnly,
                                        ],
                                        decoration: const InputDecoration(
                                          border: InputBorder.none,
                                          counterText: '',
                                          contentPadding: EdgeInsets.zero,
                                        ),
                                        onChanged: (value) => _onOtpChanged(value, index),
                                        onTap: () {
                                          if (_otpControllers[index].text.isNotEmpty) {
                                            _otpControllers[index].selection =
                                                TextSelection.fromPosition(
                                                  TextPosition(
                                                    offset: _otpControllers[index].text.length,
                                                  ),
                                                );
                                          }
                                        },
                                        onEditingComplete: () {
                                          if (index < 5) {
                                            _focusNodes[index + 1].requestFocus();
                                          }
                                        },
                                      ),
                                    );
                                  }),
                                ),
                              );
                            },
                          ),

                          const SizedBox(height: 32),

                          // Submit Button
                          SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _submit,
                              style: AppTheme.primaryButtonStyle.copyWith(
                                padding: MaterialStateProperty.all(
                                  const EdgeInsets.symmetric(vertical: 16),
                                ),
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                                  : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.verified, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    AppLocalizations.of(context).translate('confirm'),
                                    style: AppTheme.buttonText,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Resend Section
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: AppTheme.borderColor),
                      ),
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('didnt_receive_otp'),
                            style: AppTheme.bodyMedium,
                          ),
                          const SizedBox(height: 8),
                          if (_canResend)
                            TextButton(
                              onPressed: _resendOtp,
                              child: Text(
                                AppLocalizations.of(context).translate('resend_otp'),
                                style: AppTheme.bodyMedium.copyWith(
                                  color: AppTheme.primaryRed,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            )
                          else
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: AppLocalizations.of(context).translate('resend_after') + ' ',
                                    style: AppTheme.bodySmall,
                                  ),
                                  TextSpan(
                                    text: '${_countdown}s',
                                    style: AppTheme.bodySmall.copyWith(
                                      color: AppTheme.primaryRed,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}