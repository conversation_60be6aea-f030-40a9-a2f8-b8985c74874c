import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/chat_message.dart';
import 'package:bloodplusmobile/data/models/chat_session.dart';
import 'package:bloodplusmobile/data/services/chat_api_service.dart';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class ChatAIScreen extends StatefulWidget {
  const ChatAIScreen({super.key});

  @override
  State<ChatAIScreen> createState() => _ChatAIScreenState();
}

class _ChatAIScreenState extends State<ChatAIScreen> with TickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isSending = false;
  late AnimationController _sendButtonController;
  bool _showSidebar = false;
  bool _showSidebarInitialized = false;
  bool _isLoading = true;
  List<ChatSession> sessions = [];
  ChatSession? _currentSession;
  List<ChatMessage> currentMessages = [];
  final UserManager _userManager = UserManager();
  String? _token;

  final ChatApiService _chatApiService = ChatApiService();

  @override
  void initState() {
    super.initState();
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
    appStateNotifier.setLoggedIn(false);
    _initializeApp();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    if (sessions.isEmpty && !_isLoading) {
      _createNewSession();
    }

    if (screenWidth < 600 && !_showSidebarInitialized) {
      setState(() {
        _showSidebar = false;
        _showSidebarInitialized = true;
      });
    }
  }

  String formatVietnamTime(DateTime utcTime) {
    final vnTime = utcTime.add(const Duration(hours: 7));
    return DateFormat('HH:mm, dd/MM/yyyy').format(vnTime);
  }

  @override
  void dispose() {
    _sendButtonController.dispose();
    _scrollController.dispose();
    _controller.dispose();
    super.dispose();
  }

  Future<void> _initializeApp() async {
    try {
      setState(() => _isLoading = true);

      _token = await _userManager.getUserToken();
      if (_token == null) {
        _createNewSession();
        return;
      }

      await _loadSessions();

      if (sessions.isEmpty) {
        _createNewSession();
      } else {
        _currentSession = sessions.first;
        await _loadMessagesForSession(_currentSession!.conversationId);
      }
    } catch (e) {
      debugPrint('Initialization error: $e');
      _createNewSession();
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadSessions() async {
    if (_token == null) return;

    try {
      final response = await _chatApiService.fetchChatSessions(token: _token!);
      if (mounted) {
        setState(() {
          sessions = response;
        });
      }
    } catch (e) {
      debugPrint('Load sessions error: $e');
    }
  }

  Future<void> _loadMessagesForSession(String conversationId) async {
    if (_token == null) return;

    try {
      final messages = await _chatApiService.fetchMessagesForSession(
        token: _token!,
        conversationId: conversationId,
      );
      if (mounted) {
        setState(() {
          currentMessages = messages;
        });
        _scrollToBottom();
      }
    } catch (e) {
      debugPrint('Load messages error: $e');
      setState(() {
        currentMessages = [];
      });
    }
  }

  void _createNewSession() {
    final newConversationId = '${DateTime.now().millisecondsSinceEpoch}-${(1000 + (DateTime.now().microsecond % 1000)).toString()}';
    final newSession = ChatSession(
      conversationId: newConversationId,
      title: AppLocalizations.of(context).translate('new_chat'),
    );

    if (mounted) {
      setState(() {
        sessions.add(newSession);
        _currentSession = newSession;
        currentMessages = [];
        if (MediaQuery.of(context).size.width < 600) {
          _showSidebar = false;
        }
      });
    }
  }

  Future<void> _switchSession(ChatSession session) async {
    if (_currentSession?.conversationId == session.conversationId) return;

    setState(() {
      _currentSession = session;
      currentMessages = [];
      _showSidebar = false;
    });

    await _loadMessagesForSession(session.conversationId);
  }

  Future<void> _deleteSession(ChatSession session) async {
    if (sessions.length <= 1) return;

    try {
      _token ??= await _userManager.getUserToken();
      if (_token == null) throw Exception(AppLocalizations.of(context).translate('sending_message_error'));

      await _chatApiService.deleteSession(
        token: _token!,
        conversationId: session.conversationId,
      );

      setState(() {
        sessions.remove(session);
        if (_currentSession?.conversationId == session.conversationId) {
          _currentSession = sessions.isNotEmpty ? sessions.first : null;
          currentMessages = [];
          if (_currentSession != null) {
            _loadMessagesForSession(_currentSession!.conversationId);
          } else {
            _createNewSession();
          }
        }
      });
      DialogHelper.showAnimatedSuccessDialog(
        context: context,
        title: AppLocalizations.of(context).translate('success'),
        message: AppLocalizations.of(context).translate('session_deleted').replaceAll('{sessionTitle}', session.title),
        buttonText: 'OK',
      );
    } catch (e) {
      debugPrint('Delete session error: $e');
      DialogHelper.showAnimatedErrorDialog(
        context: context,
        title: AppLocalizations.of(context).translate('error'),
        message: AppLocalizations.of(context).translate('delete_session_error').replaceAll('{error}', e.toString()),
        buttonText: 'OK',
      );
    }
  }

  void _confirmDeleteSession(ChatSession session) {
    if (sessions.length <= 1) return;

    DialogHelper.showConfirmationDialog(
      context: context,
      title: AppLocalizations.of(context).translate('delete_session'),
      message: AppLocalizations.of(context).translate('confirm_delete_message').replaceAll('{sessionTitle}', session.title),
      cancelButtonText: AppLocalizations.of(context).translate('cancel'),
      confirmButtonText: AppLocalizations.of(context).translate('delete'),
      onConfirm: () async {
        await _deleteSession(session);
      },
      icon: Icons.delete_rounded,
      iconColor: AppColors.primaryRed,
    );
  }

  void _showSessionOptions(ChatSession session) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              session.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            if (sessions.length > 1)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: Text(
                  AppLocalizations.of(context).translate('delete_session'),
                  style: const TextStyle(color: Colors.red),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _confirmDeleteSession(session);
                },
              ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  void _sendMessage() async {
    final text = _controller.text.trim();
    if (text.isEmpty || _isSending || _currentSession == null) return;

    _sendButtonController.forward();

    setState(() {
      _isSending = true;
      currentMessages.add(ChatMessage(
        role: 'user',
        message: text,
        timestamp: DateTime.now(),
        conversationId: _currentSession!.conversationId,
        id: null,
      ));

      currentMessages.add(ChatMessage(
        role: 'ai',
        message: '',
        timestamp: DateTime.now(),
        conversationId: _currentSession!.conversationId,
        id: null,
      ));
    });

    _controller.clear();
    _scrollToBottom();

    try {
      _token ??= await _userManager.getUserToken();
      if (_token == null) throw Exception(AppLocalizations.of(context).translate('sending_message_error'));

      final responses = await _chatApiService.sendMessage(
        token: _token!,
        message: text,
        conversationId: _currentSession!.conversationId,
      );

      if (currentMessages.length == 2) {
        await _loadSessions();
        setState(() {
          _currentSession = sessions.firstWhere(
                (session) => session.conversationId == _currentSession!.conversationId,
            orElse: () => _currentSession!,
          );
        });
      }

      setState(() {
        currentMessages.removeWhere((m) => m.message.isEmpty && m.role == 'ai');
        currentMessages.addAll(responses.where((e) => e.role != 'user'));
        _isSending = false;
      });
    } catch (e) {
      debugPrint('Send error: $e');
      setState(() {
        currentMessages.removeWhere((m) => m.message.isEmpty && m.role == 'ai');
        currentMessages.add(ChatMessage(
          role: 'ai',
          message: AppLocalizations.of(context).translate('sending_message_error'),
          timestamp: DateTime.now(),
          conversationId: _currentSession!.conversationId,
          id: null,
        ));
        _isSending = false;
      });
    }

    _sendButtonController.reverse();
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Widget _buildSidebar() {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.chat_bubble_outline, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context).translate('chat_sessions'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: _createNewSession,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.add, color: Colors.white, size: 20),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: sessions.length,
              itemBuilder: (context, index) {
                final session = sessions[index];
                final isActive = session.conversationId == _currentSession?.conversationId;
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () => _switchSession(session),
                      onLongPress: () => _showSessionOptions(session),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: isActive ? const Color(0xFFFFEBEE) : Colors.transparent,
                          borderRadius: BorderRadius.circular(12),
                          border: isActive ? Border.all(color: const Color(0xFFE53E3E).withOpacity(0.3)) : null,
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: isActive ? const Color(0xFFE53E3E) : Colors.grey[400],
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                session.title,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                                  color: isActive ? const Color(0xFFE53E3E) : Colors.grey[700],
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            PopupMenuButton<String>(
                              icon: Icon(
                                Icons.more_vert,
                                size: 16,
                                color: isActive ? const Color(0xFFE53E3E) : Colors.grey[500],
                              ),
                              onSelected: (value) {
                                switch (value) {
                                  case 'delete':
                                    _confirmDeleteSession(session);
                                    break;
                                }
                              },
                              itemBuilder: (context) => [
                                if (sessions.length > 1)
                                  PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        const Icon(Icons.delete, size: 16, color: Colors.red),
                                        const SizedBox(width: 8),
                                        Text(
                                          AppLocalizations.of(context).translate('delete_session'),
                                          style: const TextStyle(color: Colors.red),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.role == 'user';
    final isTyping = message.message.isEmpty && message.role == 'ai';
    final vnTime = isUser
        ? message.timestamp
        : message.timestamp.toUtc().add(const Duration(hours: 7));

    final formattedTime = DateFormat('HH:mm, dd/MM/yyyy').format(vnTime);

    return GestureDetector(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        child: Row(
          mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isUser) ...[
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Image.asset(
                  'assets/icons/robot.png',
                  width: 34,
                  height: 34,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Flexible(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isUser ? const Color(0xFFE53E3E) : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    isTyping
                        ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          AppLocalizations.of(context).translate('typing'),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 8),
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                          ),
                        ),
                      ],
                    )
                        : !isUser
                        ? MarkdownBody(
                      data: message.message,
                      styleSheet: MarkdownStyleSheet(
                        p: const TextStyle(fontSize: 14, color: Colors.black87),
                        listBullet: const TextStyle(fontSize: 14, color: Colors.black87),
                      ),
                    )
                        : Text(
                      message.message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      formattedTime,
                      style: TextStyle(
                        fontSize: 10,
                        color: isUser ? Colors.white70 : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (isUser) ...[
              const SizedBox(width: 8),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(Icons.person, color: Colors.grey, size: 16),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: Stack(
        children: [
          Row(
            children: [
              if (!isSmallScreen) _buildSidebar(),
              Expanded(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: SafeArea(
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(8),
                                    onTap: () => NavigationService.goBack(),
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      child: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 20),
                                    ),
                                  ),
                                ),
                                if (isSmallScreen)
                                  Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(8),
                                      onTap: () => setState(() => _showSidebar = !_showSidebar),
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        child: const Icon(Icons.menu, color: Colors.white, size: 20),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            Text(
                              AppLocalizations.of(context).translate('chat_with_ai'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : currentMessages.isEmpty
                          ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
                            const SizedBox(height: 16),
                            Text(
                              AppLocalizations.of(context).translate('start_conversation'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AppLocalizations.of(context).translate('enter_message_prompt'),
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                          : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        itemCount: currentMessages.length,
                        itemBuilder: (context, index) {
                          return _buildMessageBubble(currentMessages[index]);
                        },
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, -2),
                          ),
                        ],
                      ),
                      child: SafeArea(
                        child: Row(
                          children: [
                            Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF7FAFC),
                                  borderRadius: BorderRadius.circular(24),
                                  border: Border.all(color: Colors.grey[300]!),
                                ),
                                child: TextField(
                                  controller: _controller,
                                  enabled: !_isSending,
                                  maxLines: null,
                                  textCapitalization: TextCapitalization.sentences,
                                  onSubmitted: (_) => _sendMessage(),
                                  decoration: InputDecoration(
                                    hintText: AppLocalizations.of(context).translate('enter_your_message'),
                                    hintStyle: const TextStyle(color: Colors.grey),
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            AnimatedBuilder(
                              animation: _sendButtonController,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: 1.0 - (_sendButtonController.value * 0.1),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: _isSending
                                          ? null
                                          : const LinearGradient(
                                        colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      color: _isSending ? Colors.grey[300] : null,
                                      shape: BoxShape.circle,
                                      boxShadow: _isSending
                                          ? null
                                          : [
                                        BoxShadow(
                                          color: const Color(0xFFE53E3E).withOpacity(0.3),
                                          blurRadius: 8,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(24),
                                        onTap: _isSending ? null : _sendMessage,
                                        child: Container(
                                          padding: const EdgeInsets.all(12),
                                          child: _isSending
                                              ? const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                            ),
                                          )
                                              : const Icon(
                                            Icons.send_rounded,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (isSmallScreen && _showSidebar) ...[
            GestureDetector(
              onTap: () => setState(() => _showSidebar = false),
              child: Container(
                color: Colors.black.withOpacity(0.5),
              ),
            ),
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              child: _buildSidebar(),
            ),
          ],
        ],
      ),
    );
  }
}