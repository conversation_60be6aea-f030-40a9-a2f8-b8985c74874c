{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "database": "Server=.;database=BloodPlusDB;TrustServerCertificate=True;User ID=sa;Password=******;"
   // "database": "Server=**************;TrustServerCertificate=True;User ID=sa;Password=********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Initial Catalog=BloodPlusDB;Persist Security Info=False;Connection Timeout=30;"
  },
  "OpenAI": {
    "ApiKey": "********************************************************************************************************************************************************************",
    "Model": "gpt-4o"
  },
  "Gemini": {
    "ApiKey": "AIzaSyDoK9UmKmYREJmgLvukWG0D2U7IwBN535E"
  },
  "LocationIQ": {
    "ApiKey": "***********************************"
  }

}