import 'package:flutter/material.dart';
import 'package:bloodplusmobile/data/models/emergency_event_detail.dart';
import 'package:bloodplusmobile/data/models/emergency_event_participant.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';

class EmergencyEventDetailScreen extends StatelessWidget {
  final EmergencyEventDetail eventDetail;
  const EmergencyEventDetailScreen({Key? key, required this.eventDetail}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final progress = eventDetail.requiredDonors == 0 ? 0.0 : eventDetail.currentDonors / eventDetail.requiredDonors;
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text('Chi tiết sự kiện khẩn cấp'),
        backgroundColor: AppTheme.primaryRed,
        elevation: 2,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event detail section
            Container(
              margin: EdgeInsets.only(bottom: isSmallScreen ? 20 : 32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryRed.withOpacity(0.08),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                child: Column(
                  children: [
                    Container(
                      height: isSmallScreen ? 3 : 4,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.primaryRed.withOpacity(0.3),
                            AppTheme.primaryRed,
                            AppTheme.primaryRed.withOpacity(0.7),
                          ],
                          stops: [0.0, progress, 1.0],
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(isSmallScreen ? 14 : 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: isSmallScreen ? 60 : 80,
                                height: isSmallScreen ? 60 : 80,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppTheme.primaryRed.withOpacity(0.1),
                                      AppTheme.primaryRed.withOpacity(0.05),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                  border: Border.all(
                                    color: AppTheme.primaryRed.withOpacity(0.2),
                                    width: 1,
                                  ),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                  child: eventDetail.image != null && eventDetail.image!.isNotEmpty
                                      ? Image.network(eventDetail.image!, fit: BoxFit.contain, errorBuilder: (context, error, stackTrace) => Icon(Icons.warning, color: AppTheme.primaryRed, size: isSmallScreen ? 24 : 36))
                                      : Icon(Icons.warning, color: AppTheme.primaryRed, size: isSmallScreen ? 24 : 36),
                                ),
                              ),
                              SizedBox(width: isSmallScreen ? 10 : 20),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(eventDetail.title, style: AppTheme.headingSmall.copyWith(fontWeight: FontWeight.bold, fontSize: isSmallScreen ? 15 : 18, color: AppTheme.textPrimary, height: 1.3), maxLines: 2, overflow: TextOverflow.ellipsis),
                                    SizedBox(height: isSmallScreen ? 6 : 8),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                                      decoration: BoxDecoration(
                                        color: AppTheme.primaryRed.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                        border: Border.all(color: AppTheme.primaryRed.withOpacity(0.3)),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.business, size: isSmallScreen ? 10 : 12, color: AppTheme.primaryRed),
                                          SizedBox(width: isSmallScreen ? 2 : 4),
                                          Text(eventDetail.organizationName, style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 12, fontWeight: FontWeight.w500, color: AppTheme.primaryRed), maxLines: 1, overflow: TextOverflow.ellipsis),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 6 : 8),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                                      decoration: BoxDecoration(
                                        color: Colors.red.withOpacity(0.08),
                                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                        border: Border.all(color: Colors.red.withOpacity(0.2)),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.bloodtype, size: isSmallScreen ? 10 : 12, color: Colors.red),
                                          SizedBox(width: isSmallScreen ? 2 : 4),
                                          Text(eventDetail.requiredBloodType, style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 12, fontWeight: FontWeight.w500, color: Colors.red), maxLines: 1, overflow: TextOverflow.ellipsis),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: isSmallScreen ? 12 : 16),
                          Text(eventDetail.description, style: AppTheme.bodyLarge),
                          SizedBox(height: isSmallScreen ? 12 : 16),
                          Container(
                            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8FAFC),
                              borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                              border: Border.all(color: AppTheme.primaryRed.withOpacity(0.1)),
                            ),
                            child: Row(
                      children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryRed.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                                        ),
                                        child: Icon(Icons.calendar_today, size: isSmallScreen ? 14 : 16, color: AppTheme.primaryRed),
                                      ),
                                      SizedBox(width: isSmallScreen ? 6 : 8),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('Ngày', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                                            Text(_formatDate(eventDetail.eventDate), style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 13, fontWeight: FontWeight.w600, color: AppTheme.textPrimary)),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  height: isSmallScreen ? 30 : 40,
                                  width: 1,
                                  color: AppTheme.borderColor,
                                  margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12),
                                ),
                                Expanded(
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryRed.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                                        ),
                                        child: Icon(Icons.people, size: isSmallScreen ? 14 : 16, color: AppTheme.primaryRed),
                                      ),
                                      SizedBox(width: isSmallScreen ? 6 : 8),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('Người cần', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                                            Text('${eventDetail.requiredDonors}', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 13, fontWeight: FontWeight.w600, color: AppTheme.textPrimary)),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: isSmallScreen ? 12 : 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Đã có', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 12 : 13, fontWeight: FontWeight.w600, color: AppTheme.textPrimary)),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 2 : 4),
                                decoration: BoxDecoration(
                                  color: progress >= 1.0 ? Colors.green.withOpacity(0.1) : progress >= 0.8 ? Colors.orange.withOpacity(0.1) : AppTheme.primaryRed.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                  border: Border.all(color: progress >= 1.0 ? Colors.green.withOpacity(0.3) : progress >= 0.8 ? Colors.orange.withOpacity(0.3) : AppTheme.primaryRed.withOpacity(0.3)),
                                ),
                                child: Text('${eventDetail.currentDonors}/${eventDetail.requiredDonors}', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 12, fontWeight: FontWeight.w700, color: progress >= 1.0 ? Colors.green : progress >= 0.8 ? Colors.orange : AppTheme.primaryRed)),
                              ),
                            ],
                          ),
                          SizedBox(height: isSmallScreen ? 6 : 8),
                          Container(
                            height: isSmallScreen ? 6 : 8,
                            decoration: BoxDecoration(
                              color: AppTheme.borderColor.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(isSmallScreen ? 3 : 4),
                            ),
                            child: Stack(
                      children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: AppTheme.borderColor.withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 3 : 4),
                                  ),
                                ),
                                FractionallySizedBox(
                                  widthFactor: progress.clamp(0.0, 1.0),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: progress >= 1.0 ? [Colors.green, Colors.green.shade700] : progress >= 0.8 ? [Colors.orange, Colors.orange.shade700] : [AppTheme.primaryRed, AppTheme.primaryRed.withOpacity(0.7)],
                                      ),
                                      borderRadius: BorderRadius.circular(isSmallScreen ? 3 : 4),
                                      boxShadow: [
                                        BoxShadow(
                                          color: (progress >= 1.0 ? Colors.green : progress >= 0.8 ? Colors.orange : AppTheme.primaryRed).withOpacity(0.3),
                                          blurRadius: isSmallScreen ? 3 : 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: isSmallScreen ? 6 : 8),
                    Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                              Text('${(progress * 100).toInt()}% hoàn thành', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 10 : 11, color: AppTheme.textSecondary, fontWeight: FontWeight.w500)),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 6 : 8, vertical: isSmallScreen ? 1 : 2),
                                decoration: BoxDecoration(
                                  color: progress >= 1.0 ? Colors.green.withOpacity(0.1) : progress >= 0.8 ? Colors.orange.withOpacity(0.1) : AppTheme.primaryRed.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                                ),
                                child: Text(progress >= 1.0 ? 'Đủ người' : progress >= 0.8 ? 'Gần đủ' : 'Còn chỗ', style: AppTheme.bodyLarge.copyWith(fontSize: isSmallScreen ? 9 : 10, fontWeight: FontWeight.w600, color: progress >= 1.0 ? Colors.green : progress >= 0.8 ? Colors.orange : AppTheme.primaryRed)),
                              ),
                      ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Participant list section
            Text('Danh sách người tham gia', style: AppTheme.headingSmall.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            eventDetail.participants.isEmpty
                ? Text('Chưa có người tham gia.', style: AppTheme.bodyLarge)
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: eventDetail.participants.length,
                    separatorBuilder: (_, __) => SizedBox(height: isSmallScreen ? 10 : 16),
                    itemBuilder: (context, index) {
                      final p = eventDetail.participants[index];
                      return _ParticipantCard(participant: p, isSmallScreen: isSmallScreen);
                    },
                  ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

// Widget hiển thị từng user tham gia
class _ParticipantCard extends StatelessWidget {
  final EmergencyEventParticipant participant;
  final bool isSmallScreen;
  const _ParticipantCard({required this.participant, required this.isSmallScreen});

  void _callPhone(BuildContext context) async {
    final phone = participant.phoneNumber;
    if (phone != null && phone.isNotEmpty) {
      final Uri uri = Uri(scheme: 'tel', path: phone);
      try {
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          // Không mở được app gọi điện, copy số và hiện toast
          await Clipboard.setData(ClipboardData(text: phone));
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Đã copy số điện thoại')),
            );
          }
        }
      } catch (e) {
        // Nếu có lỗi, cũng copy số và hiện toast
        await Clipboard.setData(ClipboardData(text: phone));
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Đã copy số điện thoại')),
          );
        }
      }
    }
  }

  static Color _statusColor(int value) {
    switch (value) {
      case 0:
        return Colors.orange;
      case 1:
        return AppTheme.primaryRed;
      case 2:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  static String _statusName(int value) {
    switch (value) {
      case 0:
        return 'Chờ xác nhận';
      case 1:
        return 'Đã xác nhận';
      case 2:
        return 'Đã hiến máu';
      default:
        return 'Không rõ';
    }
  }

  static String _bloodComponentName(int value) {
    switch (value) {
      case 0:
        return 'Hồng cầu';
      case 1:
        return 'Huyết tương';
      case 2:
        return 'Tiểu cầu';
      case 3:
        return 'Bạch cầu';
      case 4:
        return 'Máu toàn phần';
      default:
        return 'Không rõ';
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _callPhone(context),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(isSmallScreen ? 14 : 18),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryRed.withOpacity(0.06),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.all(isSmallScreen ? 10 : 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: isSmallScreen ? 22 : 28,
              backgroundColor: _statusColor(participant.status).withOpacity(0.12),
              child: Icon(Icons.person, color: _statusColor(participant.status), size: isSmallScreen ? 22 : 28),
            ),
            SizedBox(width: isSmallScreen ? 10 : 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(participant.userName ?? 'Ẩn danh', style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.bold, fontSize: isSmallScreen ? 13 : 15)),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _statusColor(participant.status).withOpacity(0.15),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(_statusName(participant.status), style: AppTheme.bodyLarge.copyWith(fontSize: 11, color: _statusColor(participant.status), fontWeight: FontWeight.w600)),
                      ),
                      if (participant.phoneNumber != null && participant.phoneNumber!.isNotEmpty) ...[
                        SizedBox(width: 8),
                        InkWell(
                          onTap: () {
                            _callPhone(context);
                          },
                          child: Container(
                            padding: EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.12),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.call, color: Colors.green, size: 18),
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (participant.bloodType != null) ...[
                    SizedBox(height: 2),
                    Text('Nhóm máu: ${participant.bloodType}', style: AppTheme.bodyLarge.copyWith(fontSize: 12)),
                  ],
                  if (participant.phoneNumber != null && participant.phoneNumber!.isNotEmpty) ...[
                    SizedBox(height: 2),
                    Text('SĐT: ${participant.phoneNumber}', style: AppTheme.bodyLarge.copyWith(fontSize: 12, color: Colors.green)),
                  ],
                  if (participant.email != null) ...[
                    SizedBox(height: 2),
                    Text('Email: ${participant.email}', style: AppTheme.bodyLarge.copyWith(fontSize: 12)),
                  ],
                  if (participant.address != null) ...[
                    SizedBox(height: 2),
                    Text('Địa chỉ: ${participant.address}', style: AppTheme.bodyLarge.copyWith(fontSize: 12)),
                  ],
                  SizedBox(height: 2),
                  Text('Thành phần máu: ${_bloodComponentName(participant.bloodComponent)}', style: AppTheme.bodyLarge.copyWith(fontSize: 12)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 