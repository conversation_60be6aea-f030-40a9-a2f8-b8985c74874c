import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/data/models/Enum/voucher_type.dart';
import 'package:bloodplusmobile/data/models/voucher_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:intl/intl.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/data/services/voucher_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';

class UserVoucherDetailScreen extends StatefulWidget {
  final Voucher voucher;

  const UserVoucherDetailScreen({
    super.key,
    required this.voucher,
  });

  @override
  State<UserVoucherDetailScreen> createState() => _UserVoucherDetailScreenState();
}

class _UserVoucherDetailScreenState extends State<UserVoucherDetailScreen>
    with TickerProviderStateMixin {
  final VoucherService _voucherService = VoucherService();
  Voucher? _voucher;
  bool _isLoading = true;
  String? _errorMessage;
  bool _showVoucherImage = false;
  bool _isDownloading = false;

  late AnimationController _animationController;
  late AnimationController _imageToggleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _imageScaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchVoucherDetail();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _imageToggleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _imageScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _imageToggleController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _imageToggleController.dispose();
    super.dispose();
  }

  Future<void> _fetchVoucherDetail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final voucher = await _voucherService.getVoucherById(widget.voucher.id);
      if (mounted) {
        setState(() {
          _voucher = voucher;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _toggleVoucherImage() {
    setState(() {
      _showVoucherImage = !_showVoucherImage;
    });

    if (_showVoucherImage) {
      _imageToggleController.forward();
    } else {
      _imageToggleController.reverse();
    }
  }

  void _zoomVoucherImage(String? imageUrl) {
    if (imageUrl != null && imageUrl.isNotEmpty) {
      showDialog(
        context: context,
        barrierColor: Colors.black87,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: InteractiveViewer(
                    boundaryMargin: const EdgeInsets.all(20.0),
                    minScale: 0.1,
                    maxScale: 5.0,
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: 300,
                        decoration: const BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.broken_image_rounded,
                            color: Colors.white,
                            size: 64,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 8.0,
                right: 8.0,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.close_rounded, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  void _downloadVoucherImage(String? imageUrl) async {
    if (imageUrl == null || imageUrl.isEmpty) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      var status = await Permission.storage.request();
      if (status.isGranted) {
        final directories = await getExternalStorageDirectories(type: StorageDirectory.downloads);
        final directory = directories?.first;
        if (directory == null) {
          _showErrorSnackBar('Không thể truy cập thư mục tải xuống');
          return;
        }

        final fileName = 'voucher_${DateTime.now().millisecondsSinceEpoch}.png';

        await FlutterDownloader.enqueue(
          url: imageUrl,
          savedDir: directory.path,
          fileName: fileName,
          showNotification: true,
          openFileFromNotification: true,
        );

        _showSuccessSnackBar('Đang tải xuống voucher...');

        FlutterDownloader.registerCallback((id, status, progress) {
          if (status == DownloadTaskStatus.complete) {
            _showSuccessSnackBar('Tải xuống hoàn tất!');
          }
        });
      } else {
        _showErrorSnackBar('Quyền truy cập bị từ chối. Vui lòng cấp quyền trong cài đặt.');
      }
    } catch (e) {
      _showErrorSnackBar('Có lỗi xảy ra khi tải xuống');
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green[600],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.primaryRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: _buildLoadingState(localizations, isSmallScreen),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: _buildErrorState(localizations, isSmallScreen),
      );
    }

    final voucher = _voucher ?? widget.voucher;
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        bottom: true,
        top: false,
        child: CustomScrollView(
          slivers: [
            _buildSliverAppBar(voucher, localizations, isSmallScreen),
            SliverToBoxAdapter(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: Column(
                    children: [
                      _buildVoucherInfoCard(voucher, localizations, isSmallScreen),
                      _buildDetailsCard(voucher, localizations, isSmallScreen),
                      if (voucher.voucherImage != null && voucher.voucherImage!.isNotEmpty)
                        _buildVoucherImageCard(voucher, localizations, isSmallScreen),
                      SizedBox(height: isSmallScreen ? 24 : MediaQuery.of(context).padding.bottom + 32),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(AppLocalizations localizations, bool isSmallScreen) {
    return SafeArea(
      child: Column(
        children: [
          _buildSimpleAppBar(localizations, isSmallScreen),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
                    strokeWidth: isSmallScreen ? 2.5 : 3,
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 20),
                  Text(
                    'Đang tải chi tiết voucher...',
                    style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 12 : 14),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(AppLocalizations localizations, bool isSmallScreen) {
    return SafeArea(
      child: Column(
        children: [
          _buildSimpleAppBar(localizations, isSmallScreen),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline_rounded,
                    size: isSmallScreen ? 48 : 64,
                    color: Colors.red[300],
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 20),
                  Text(
                    _errorMessage!,
                    style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 12 : 14),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 20),
                  ElevatedButton(
                    onPressed: _fetchVoucherDetail,
                    style: AppTheme.primaryButtonStyle.copyWith(
                      padding: MaterialStateProperty.all(EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 16 : 24,
                        vertical: isSmallScreen ? 8 : 12,
                      )),
                    ),
                    child: Text(
                      'Thử lại',
                      style: AppTheme.buttonText.copyWith(fontSize: isSmallScreen ? 12 : 14),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAppBar(AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16, vertical: isSmallScreen ? 8 : 12),
      decoration: const BoxDecoration(
        gradient: AppTheme.primaryGradient,
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded, color: Colors.white, size: isSmallScreen ? 18 : 20),
            onPressed: () => NavigationService.navigateToHome(),
          ),
          Text(
            localizations.translate('my_voucher') ?? 'Voucher của tôi',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontSize: isSmallScreen ? 14 : 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(Voucher voucher, AppLocalizations localizations, bool isSmallScreen) {
    return SliverAppBar(
      expandedHeight: isSmallScreen ? 200 : 300,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: Container(
        margin: EdgeInsets.all(isSmallScreen ? 6 : 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
        ),
        child: IconButton(
          icon: Icon(Icons.arrow_back_ios_rounded, color: Colors.white, size: isSmallScreen ? 18 : 20),
          onPressed: () => NavigationService.navigateToHome(),
        ),
      ),
      actions: [
        Container(
          margin: EdgeInsets.all(isSmallScreen ? 6 : 8),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.9),
            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check_circle_rounded, color: Colors.white, size: isSmallScreen ? 14 : 16),
                SizedBox(width: isSmallScreen ? 2 : 4),
                Text(
                  'Đã sở hữu',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 10 : 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            if (voucher.panel != null && voucher.panel!.isNotEmpty)
              Image.network(
                voucher.panel!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  decoration: const BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.card_giftcard_rounded,
                      size: isSmallScreen ? 60 : 80,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
            else
              Container(
                decoration: const BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                ),
                child: Center(
                  child: Icon(
                    Icons.card_giftcard_rounded,
                    size: isSmallScreen ? 60 : 80,
                    color: Colors.white,
                  ),
                ),
              ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: isSmallScreen ? 12 : 20,
              right: isSmallScreen ? 12 : 20,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 8 : 12,
                  vertical: isSmallScreen ? 4 : 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                ),
                child: Text(
                  getVoucherTypeName(voucher.type, localizations),
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 10 : 12,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoucherInfoCard(Voucher voucher, AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 12 : 20),
      decoration: AppTheme.elevatedCardDecoration,
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: isSmallScreen ? 48 : 60,
                  height: isSmallScreen ? 48 : 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppTheme.borderColor, width: isSmallScreen ? 2 : 3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: isSmallScreen ? 6 : 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: Image.network(
                      voucher.sponsorImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: AppTheme.backgroundColor,
                        child: Icon(
                          Icons.business_rounded,
                          size: isSmallScreen ? 20 : 30,
                          color: AppTheme.primaryRed,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        voucher.sponsorName,
                        style: AppTheme.headingSmall.copyWith(fontSize: isSmallScreen ? 12 : 14),
                      ),
                      SizedBox(height: isSmallScreen ? 2 : 4),
                      Text(
                        localizations.translate('sponsor') ?? 'Nhà tài trợ',
                        style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 10 : 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 12 : 20),
            Text(
              voucher.voucherName,
              style: AppTheme.headingLarge.copyWith(fontSize: isSmallScreen ? 16 : 20),
            ),
            SizedBox(height: isSmallScreen ? 12 : 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                      border: Border.all(
                        color: Colors.green.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 4 : 6),
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check_circle_rounded,
                            size: isSmallScreen ? 14 : 18,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: isSmallScreen ? 8 : 12),
                        Text(
                          'Đã sở hữu',
                          style: AppTheme.bodyLarge.copyWith(
                            fontWeight: FontWeight.w700,
                            color: Colors.green[700],
                            fontSize: isSmallScreen ? 12 : 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 16),
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule_rounded,
                        size: isSmallScreen ? 14 : 18,
                        color: Colors.orange,
                      ),
                      SizedBox(width: isSmallScreen ? 4 : 8),
                      Text(
                        DateFormat('dd/MM/yyyy').format(voucher.expiryDate),
                        style: AppTheme.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.orange[700],
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard(Voucher voucher, AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 20),
      decoration: AppTheme.cardDecoration,
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailSection(
              localizations.translate('description') ?? 'Mô tả',
              voucher.description,
              Icons.description_rounded,
              isSmallScreen,
            ),
            if (voucher.userManual != null && voucher.userManual!.isNotEmpty) ...[
              SizedBox(height: isSmallScreen ? 16 : 24),
              _buildDetailSection(
                localizations.translate('usage_instructions') ?? 'Hướng dẫn sử dụng',
                voucher.userManual!,
                Icons.help_outline_rounded,
                isSmallScreen,
              ),
            ],
            SizedBox(height: isSmallScreen ? 16 : 24),
            _buildDetailSection(
              localizations.translate('support') ?? 'Hỗ trợ',
              (voucher.support != null && voucher.support!.isNotEmpty)
                  ? voucher.support!
                  : (localizations.translate('no_support_info') ?? 'Không có thông tin hỗ trợ'),
              Icons.support_agent_rounded,
              isSmallScreen,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, String content, IconData icon, bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
              decoration: BoxDecoration(
                color: AppTheme.primaryRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
              ),
              child: Icon(
                icon,
                size: isSmallScreen ? 16 : 20,
                color: AppTheme.primaryRed,
              ),
            ),
            SizedBox(width: isSmallScreen ? 8 : 12),
            Text(
              title,
              style: AppTheme.headingSmall.copyWith(fontSize: isSmallScreen ? 12 : 14),
            ),
          ],
        ),
        SizedBox(height: isSmallScreen ? 8 : 12),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: Text(
            content,
            style: AppTheme.bodyMedium.copyWith(
              height: 1.6,
              color: AppTheme.textPrimary,
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVoucherImageCard(Voucher voucher, AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 20),
      decoration: AppTheme.cardDecoration,
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryRed.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                  ),
                  child: Icon(
                    Icons.qr_code_2_rounded,
                    size: isSmallScreen ? 16 : 20,
                    color: AppTheme.primaryRed,
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Text(
                  localizations.translate('voucher_image') ?? 'Mã voucher',
                  style: AppTheme.headingSmall.copyWith(fontSize: isSmallScreen ? 12 : 14),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    _showVoucherImage ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                    color: AppTheme.primaryRed,
                    size: isSmallScreen ? 18 : 20,
                  ),
                  onPressed: _toggleVoucherImage,
                ),
              ],
            ),
            if (_showVoucherImage) ...[
              SizedBox(height: isSmallScreen ? 12 : 16),
              ScaleTransition(
                scale: _imageScaleAnimation,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                    border: Border.all(color: AppTheme.borderColor),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                    child: Image.network(
                      voucher.voucherImage!,
                      width: double.infinity,
                      height: isSmallScreen ? 180 : 250,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: isSmallScreen ? 180 : 250,
                        decoration: const BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                        ),
                        child: Center(
                          child: Icon(
                            Icons.broken_image_rounded,
                            color: Colors.white,
                            size: isSmallScreen ? 48 : 64,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: isSmallScreen ? 12 : 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue[600]!, Colors.blue[700]!],
                        ),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            blurRadius: isSmallScreen ? 6 : 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () => _zoomVoucherImage(voucher.voucherImage),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 8 : 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                          ),
                        ),
                        icon: Icon(Icons.zoom_in_rounded, color: Colors.white, size: isSmallScreen ? 16 : 20),
                        label: Text(
                          'Phóng to',
                          style: AppTheme.buttonText.copyWith(fontSize: isSmallScreen ? 12 : 14),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: isSmallScreen ? 8 : 12),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.green[600]!, Colors.green[700]!],
                        ),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.3),
                            blurRadius: isSmallScreen ? 6 : 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: _isDownloading ? null : () => _downloadVoucherImage(voucher.voucherImage),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 8 : 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                          ),
                        ),
                        icon: _isDownloading
                            ? SizedBox(
                          width: isSmallScreen ? 14 : 16,
                          height: isSmallScreen ? 14 : 16,
                          child: CircularProgressIndicator(
                            strokeWidth: isSmallScreen ? 1.5 : 2,
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                            : Icon(Icons.download_rounded, color: Colors.white, size: isSmallScreen ? 16 : 20),
                        label: Text(
                          _isDownloading ? 'Đang tải...' : 'Tải xuống',
                          style: AppTheme.buttonText.copyWith(fontSize: isSmallScreen ? 12 : 14),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}