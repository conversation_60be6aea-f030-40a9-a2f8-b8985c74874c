import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';

class FeatureGrid extends StatefulWidget {
  final List<Map<String, dynamic>> features;

  const FeatureGrid({Key? key, required this.features}) : super(key: key);

  @override
  State<FeatureGrid> createState() => _FeatureGridState();
}

class _FeatureGridState extends State<FeatureGrid> with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.features.length,
          (index) => AnimationController(
        duration: Duration(milliseconds: 600 + (index * 100)),
        vsync: this,
      ),
    );

    _slideAnimations = _controllers
        .map((controller) => Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
    ))
        .toList();

    _fadeAnimations = _controllers
        .map((controller) => Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: controller, curve: Curves.easeOut),
    ))
        .toList();

    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 120), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _navigateToFeature(String featureTitle) {
    HapticFeedback.mediumImpact();

    switch (featureTitle) {
      case 'expert_advice':
        NavigationService.navigateTo(AppRoutes.expertAdvice);
        break;
      case 'information':
        NavigationService.navigateTo(AppRoutes.otherInformation);
        break;
      case 'schedule_donation':
        NavigationService.navigateTo(AppRoutes.donationEvent);
        break;
      case 'blog_list':
        NavigationService.navigateTo(AppRoutes.blog);
        break;
      case 'nearby_event':
        final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
        NavigationService.navigateTo(AppRoutes.nearbyEvent, arguments: appStateNotifier.donationEvents);
        break;
      case 'voucher':
        NavigationService.navigateTo(AppRoutes.voucher);
        break;
      case 'leaderboard':
        NavigationService.navigateTo(AppRoutes.leaderboard);
        break;
      case 'shopping':
        _showComingSoonDialog(featureTitle);
        break;
      case 'emergency_donation':
        NavigationService.navigateTo(AppRoutes.emergencyDonation);
        break;
    }
  }

  void _showComingSoonDialog(String featureTitle) {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            margin: EdgeInsets.all(screenWidth * 0.04),
            padding: EdgeInsets.all(screenWidth * 0.07),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.06),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withOpacity(0.1),
                  blurRadius: 30,
                  offset: const Offset(0, 10),
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(screenWidth * 0.05),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFFE53E3E),
                        const Color(0xFFFC8181),
                      ],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                SizedBox(height: screenWidth * 0.06),
                Text(
                  localizations.translate('${featureTitle}_title'),
                  style: GoogleFonts.inter(
                    fontSize: screenWidth * 0.07,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF2D3748),
                    letterSpacing: -0.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenWidth * 0.03),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04, vertical: screenWidth * 0.02),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFED7D7),
                    borderRadius: BorderRadius.circular(screenWidth * 0.05),
                  ),
                  child: Text(
                    localizations.translate('coming_soon'),
                    style: GoogleFonts.inter(
                      fontSize: screenWidth * 0.03,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFFE53E3E),
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
                SizedBox(height: screenWidth * 0.04),
                Text(
                  localizations.translate('feature_in_development'),
                  style: GoogleFonts.inter(
                    fontSize: screenWidth * 0.035,
                    color: const Color(0xFF718096),
                    height: 1.5,
                    letterSpacing: 0.1,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenWidth * 0.07),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: screenWidth * 0.04),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            side: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Text(
                          localizations.translate('close'),
                          style: GoogleFonts.inter(
                            fontSize: screenWidth * 0.04,
                            color: const Color(0xFF718096),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: screenWidth * 0.03),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              const Color(0xFFE53E3E),
                              const Color(0xFFC53030),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(screenWidth * 0.03),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.red.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  localizations.translate('notify_me_message'),
                                  style: GoogleFonts.inter(fontWeight: FontWeight.w500),
                                ),
                                backgroundColor: const Color(0xFFE53E3E),
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(screenWidth * 0.03),
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            padding: EdgeInsets.symmetric(vertical: screenWidth * 0.04),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(screenWidth * 0.03),
                            ),
                          ),
                          child: Text(
                            localizations.translate('notify_me'),
                            style: GoogleFonts.inter(
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureCard(
      BuildContext context, Map<String, dynamic> feature, int index) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final cardPadding = screenWidth * 0.031;
    final iconSize = screenWidth * 0.09;

    return AnimatedBuilder(
      animation: _controllers[index],
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimations[index],
          child: Transform.translate(
            offset: Offset(0.0, _slideAnimations[index].value),
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () => _navigateToFeature(feature['title']),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.05),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFE2E8F0).withOpacity(0.5),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
            border: Border.all(
              color: const Color(0xFFEDF2F7),
              width: 1.5,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(isSmallScreen ? cardPadding : cardPadding * 1.5),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFFFFF1F2),
                      const Color(0xFFFFE4E6),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                ),
                child: Image.asset(
                  feature['icon'],
                  width: iconSize,
                  height: iconSize,
                  color: const Color(0xFFD32F2F),
                ),
              ),
              SizedBox(height: isSmallScreen ? screenWidth * 0.02 : screenWidth * 0.03),
              Flexible(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    localizations.translate(feature['title']),
                    textAlign: TextAlign.center,
                    style: GoogleFonts.inter(
                      fontSize: isSmallScreen ? screenWidth * 0.033 : screenWidth * 0.043,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = screenWidth * 0.04;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: screenWidth * 0.01,
              bottom: screenWidth * 0.05,
              top: screenWidth * 0.02,
            ),
            child: Row(
              children: [
                Container(
                  width: screenWidth * 0.01,
                  height: screenWidth * 0.06,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFFE53E3E),
                        const Color(0xFFFC8181),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(screenWidth * 0.005),
                  ),
                ),
                SizedBox(width: screenWidth * 0.03),
                Text(
                  AppLocalizations.of(context).translate('our_services'),
                  style: GoogleFonts.inter(
                    fontSize: screenWidth * 0.06,
                    fontWeight: FontWeight.w800,
                    color: const Color(0xFF2D3748),
                    letterSpacing: -0.5,
                  ),
                ),
              ],
            ),
          ),
          LayoutBuilder(
            builder: (context, constraints) {
              final crossAxisCount = 3;
              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.features.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  mainAxisSpacing: screenWidth * 0.05,
                  crossAxisSpacing: screenWidth * 0.04,
                  childAspectRatio: 0.9,
                ),
                itemBuilder: (context, index) {
                  final feature = widget.features[index];
                  return _buildFeatureCard(context, feature, index);
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

class _ModernFeatureCard extends StatefulWidget {
  final String iconPath;
  final String title;
  final Color color;
  final VoidCallback onTap;

  const _ModernFeatureCard({
    required this.iconPath,
    required this.title,
    required this.color,
    required this.onTap,
  });

  @override
  State<_ModernFeatureCard> createState() => _ModernFeatureCardState();
}

class _ModernFeatureCardState extends State<_ModernFeatureCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _pressController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _pressController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeInOut),
    );

    _elevationAnimation = Tween<double>(begin: 1.0, end: 0.7).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = screenWidth * 0.04;
    final iconSize = screenWidth * 0.07;

    return AnimatedBuilder(
      animation: _pressController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) {
              setState(() => _isPressed = true);
              _pressController.forward();
              HapticFeedback.lightImpact();
            },
            onTapUp: (_) {
              _pressController.reverse();
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  setState(() => _isPressed = false);
                  widget.onTap();
                }
              });
            },
            onTapCancel: () {
              setState(() => _isPressed = false);
              _pressController.reverse();
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(screenWidth * 0.05),
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFE53E3E)
                        .withOpacity(0.08 * _elevationAnimation.value),
                    blurRadius: 12 * _elevationAnimation.value,
                    offset: Offset(0, 6 * _elevationAnimation.value),
                  ),
                  BoxShadow(
                    color: Colors.black
                        .withOpacity(0.04 * _elevationAnimation.value),
                    blurRadius: 8 * _elevationAnimation.value,
                    offset: Offset(0, 2 * _elevationAnimation.value),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(padding),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0x4DFF9292),
                          const Color(0xFFFEF5F5).withOpacity(0.7),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(screenWidth * 0.04),
                    ),
                    child: Image.asset(
                      widget.iconPath,
                      height: iconSize,
                      width: iconSize,
                      color: const Color(0xFFFF0000),
                    ),
                  ),
                  SizedBox(height: screenWidth * 0.03),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                    child: Text(
                      widget.title,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.inter(
                        fontSize: screenWidth * 0.035,
                        fontWeight: FontWeight.w600,
                        height: 1.3,
                        color: const Color(0xFF000000),
                        letterSpacing: 0.1,
                      ),
                      maxLines: screenWidth < 600 ? 1 : 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}