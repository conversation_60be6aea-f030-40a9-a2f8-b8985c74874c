import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/data/models/notification_model.dart';
import 'package:bloodplusmobile/data/manager/notification_manager.dart';
import 'package:provider/provider.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    _initializeNotifications();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _initializeNotifications() async {
    final notificationManager = Provider.of<NotificationManager>(context, listen: false);
    await notificationManager.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FE),
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(localizations, isSmallScreen),
          SliverToBoxAdapter(
            child: _buildBody(localizations, isSmallScreen),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(AppLocalizations localizations, bool isSmallScreen) {
    return SliverAppBar(
      expandedHeight: isSmallScreen ? 200 : 240,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFE53E3E),
                const Color(0xFFD53F8C),
                const Color(0xFFB91C1C),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30),
            ),
          ),
          child: Stack(
            children: [
              // Decorative blood drops
              Positioned(
                top: 40,
                right: 20,
                child: AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 60,
                        height: 80,
                        decoration: const BoxDecoration(
                          color: Colors.white12,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                            bottomLeft: Radius.circular(30),
                            bottomRight: Radius.circular(5),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Content
              Positioned(
                bottom: 30,
                left: 20,
                right: 20,
                child: AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: const Icon(
                                  Icons.notifications_active,
                                  color: Colors.white,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      localizations.translate('notification_title'),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: isSmallScreen ? 24 : 28,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      localizations.translate('notification_subtitle'),
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: isSmallScreen ? 14 : 16,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 18),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        Consumer<NotificationManager>(
          builder: (context, notificationManager, child) {
            if (notificationManager.unreadCount > 0) {
              return IconButton(
                icon: Stack(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.mark_email_read, color: Colors.white, size: 20),
                    ),
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.yellow,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
                onPressed: () => notificationManager.markAllAsRead(),
                tooltip: localizations.translate('mark_all_as_read'),
              );
            }
            return const SizedBox.shrink();
          },
        ),
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.delete_sweep, color: Colors.white, size: 20),
          ),
          onPressed: _clearAllNotifications,
          tooltip: localizations.translate('delete_all_notifications'),
        ),
        const SizedBox(width: 12),
      ],
    );
  }

  Widget _buildBody(AppLocalizations localizations, bool isSmallScreen) {
    return Consumer<NotificationManager>(
      builder: (context, notificationManager, child) {
        if (!notificationManager.isInitialized) {
          return Container(
            height: 300,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
                      ),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 3,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    localizations.translate('loading_notifications'),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final notifications = notificationManager.notifications;

        return Column(
          children: [
            const SizedBox(height: 10),
            _buildStatsCard(notifications, notificationManager, localizations, isSmallScreen),
            if (notifications.isEmpty)
              _buildEmptyState(localizations, isSmallScreen)
            else
              RefreshIndicator(
                onRefresh: () => notificationManager.refreshNotifications(),
                color: const Color(0xFFE53E3E),
                child: notificationManager.isLoading
                    ? Container(
                  height: 200,
                  child: const Center(child: CircularProgressIndicator()),
                )
                    : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    return AnimatedBuilder(
                      animation: _slideAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _slideAnimation.value * (index + 1) * 0.1),
                          child: AnimatedOpacity(
                            duration: Duration(milliseconds: 600 + (index * 100)),
                            opacity: _fadeAnimation.value,
                            child: _buildNotificationCard(
                              notifications[index],
                              notificationManager,
                              localizations,
                              isSmallScreen,
                              index,
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      height: 400,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: isSmallScreen ? 120 : 150,
              height: isSmallScreen ? 120 : 150,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFE53E3E).withOpacity(0.1),
                    const Color(0xFFD53F8C).withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(75),
              ),
              child: Icon(
                Icons.notifications_off_outlined,
                size: isSmallScreen ? 60 : 80,
                color: const Color(0xFFE53E3E).withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              localizations.translate('no_notifications'),
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: isSmallScreen ? 20 : 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                localizations.translate('no_notifications_desc'),
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: isSmallScreen ? 14 : 16,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                final notificationManager = Provider.of<NotificationManager>(context, listen: false);
                notificationManager.refreshNotifications();
              },
              icon: const Icon(Icons.refresh, size: 20),
              label: Text(localizations.translate('refresh')),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE53E3E),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                elevation: 4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(List<NotificationModel> notifications, NotificationManager notificationManager, AppLocalizations localizations, bool isSmallScreen) {
    final unreadCount = notificationManager.unreadCount;
    final totalCount = notifications.length;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20, vertical: 8),
      padding: EdgeInsets.all(isSmallScreen ? 20 : 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              localizations.translate('total_notifications'),
              totalCount.toString(),
              Icons.notifications,
              const Color(0xFFE53E3E),
              isSmallScreen,
            ),
          ),
          Container(
            width: 2,
            height: isSmallScreen ? 50 : 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.grey.withOpacity(0.1),
                  Colors.grey.withOpacity(0.3),
                  Colors.grey.withOpacity(0.1),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Expanded(
            child: _buildStatItem(
              localizations.translate('unread_notifications'),
              unreadCount.toString(),
              Icons.mark_email_unread,
              const Color(0xFFD53F8C),
              isSmallScreen,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color, bool isSmallScreen) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: isSmallScreen ? 28 : 32,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: isSmallScreen ? 28 : 32,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: color.withOpacity(0.8),
            fontSize: isSmallScreen ? 12 : 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationCard(NotificationModel notification, NotificationManager notificationManager, AppLocalizations localizations, bool isSmallScreen, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: notification.isRead
              ? Colors.transparent
              : notification.getTypeColor().withOpacity(0.3),
          width: notification.isRead ? 0 : 2,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _onNotificationTap(notification, notificationManager),
          child: Container(
            padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildNotificationIcon(notification, isSmallScreen),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildNotificationHeader(notification, isSmallScreen),
                          const SizedBox(height: 8),
                          _buildNotificationBody(notification, isSmallScreen),
                        ],
                      ),
                    ),
                    _buildNotificationActions(notification, notificationManager, localizations, isSmallScreen),
                  ],
                ),
                const SizedBox(height: 12),
                _buildNotificationFooter(notification, isSmallScreen),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationModel notification, bool isSmallScreen) {
    return Container(
      width: isSmallScreen ? 50 : 56,
      height: isSmallScreen ? 50 : 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            notification.getTypeColor().withOpacity(0.2),
            notification.getTypeColor().withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: notification.getTypeColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Icon(
        notification.getTypeIcon(),
        color: notification.getTypeColor(),
        size: isSmallScreen ? 24 : 28,
      ),
    );
  }

  Widget _buildNotificationHeader(NotificationModel notification, bool isSmallScreen) {
    return Row(
      children: [
        Expanded(
          child: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
              fontSize: isSmallScreen ? 16 : 18,
              color: notification.isRead ? Colors.grey[600] : Colors.grey[800],
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (!notification.isRead)
          Container(
            margin: const EdgeInsets.only(left: 8),
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  notification.getTypeColor(),
                  notification.getTypeColor().withOpacity(0.7),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: notification.getTypeColor().withOpacity(0.4),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildNotificationBody(NotificationModel notification, bool isSmallScreen) {
    return Text(
      notification.content,
      style: TextStyle(
        fontSize: isSmallScreen ? 14 : 15,
        color: Colors.grey[600],
        height: 1.4,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildNotificationFooter(NotificationModel notification, bool isSmallScreen) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                notification.getTypeColor().withOpacity(0.15),
                notification.getTypeColor().withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: notification.getTypeColor().withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Text(
            notification.getTypeDisplayName(context),
            style: TextStyle(
              color: notification.getTypeColor(),
              fontSize: isSmallScreen ? 11 : 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.access_time,
                size: 12,
                color: Colors.grey[500],
              ),
              const SizedBox(width: 4),
              Text(
                notification.getFormattedDate(),
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: isSmallScreen ? 11 : 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationActions(NotificationModel notification, NotificationManager notificationManager, AppLocalizations localizations, bool isSmallScreen) {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.more_vert,
          color: Colors.grey[600],
          size: isSmallScreen ? 18 : 20,
        ),
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      onSelected: (value) => _onActionSelected(value, notification, notificationManager),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(Icons.delete, color: Colors.red, size: 16),
              ),
              const SizedBox(width: 12),
              Text(localizations.translate('delete'), style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500)),
            ],
          ),
        ),
        if (!notification.isRead)
          PopupMenuItem(
            value: 'mark_read',
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(Icons.mark_email_read, color: Colors.blue, size: 16),
                ),
                const SizedBox(width: 12),
                Text(localizations.translate('mark_as_read'), style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w500)),
              ],
            ),
          ),
      ],
    );
  }

  void _onNotificationTap(NotificationModel notification, NotificationManager notificationManager) async {
    if (!notification.isRead) {
      await notificationManager.markAsRead(notification.id);
    }
    _handleNotificationNavigation(notification);
  }

  void _handleNotificationNavigation(NotificationModel notification) {
    switch (notification.type) {
      case NotificationType.appointment:
        NavigationService.navigateTo(AppRoutes.donationHistory);
        break;
      case NotificationType.event:
        NavigationService.navigateTo(AppRoutes.donationEvent);
        break;
      case NotificationType.emergency:
        NavigationService.navigateTo(AppRoutes.emergencyDonation);
        break;
      case NotificationType.system:
        break;
      case NotificationType.unknown:
      NavigationService.navigateTo(AppRoutes.emergencyDonation);
        break;
    }
  }

  void _onActionSelected(String action, NotificationModel notification, NotificationManager notificationManager) async {
    switch (action) {
      case 'delete':
        await notificationManager.deleteNotification(notification.id);
        break;
      case 'mark_read':
        await notificationManager.markAsRead(notification.id);
        break;
    }
  }

  void _clearAllNotifications() {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.delete_sweep, color: Colors.red, size: 24),
            ),
            const SizedBox(width: 12),
            Text(localizations.translate('confirm_delete'), style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ],
        ),
        content: Text(
          localizations.translate('confirm_delete_all_notifications'),
          style: TextStyle(fontSize: 16, height: 1.4),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.translate('cancel'), style: TextStyle(color: Colors.grey[600])),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final notificationManager = Provider.of<NotificationManager>(context, listen: false);
              await notificationManager.clearAllNotifications();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: Text(localizations.translate('delete_all')),
          ),
        ],
      ),
    );
  }
}