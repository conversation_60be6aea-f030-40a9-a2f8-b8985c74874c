import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class ProfileHeader extends StatelessWidget {
  final UserModel user;

  const ProfileHeader({
    Key? key,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: AppTheme.glassmorphismDecoration,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.primaryRed.withOpacity(0.3),
                    width: 4,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryRed.withOpacity(0.2),
                      blurRadius: 16,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 60,
                  backgroundColor: AppTheme.primaryRed.withOpacity(0.1),
                  backgroundImage: _getProfileImage(),
                  child: _getProfileImage() == null
                      ? Icon(
                    Icons.person,
                    size: 60,
                    color: AppTheme.primaryRed.withOpacity(0.7),
                  )
                      : null,
                ),
              ),
              Positioned(
                bottom: 4,
                right: 4,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getDonorStatusColor(),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    _getDonorStatusIcon(),
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            user.name,
            style: AppTheme.headingMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            user.email,
            style: AppTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryRed.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.bloodtype,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  user.bloodType ?? 'Chưa xác định',
                  style: AppTheme.buttonText.copyWith(fontSize: 14),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getDonorLevelColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _getDonorLevelColor().withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Text(
              _getDonorLevelText(context),
              style: AppTheme.bodySmall.copyWith(
                color: _getDonorLevelColor(),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  ImageProvider? _getProfileImage() {
    if (user.userImage != null && user.userImage!.isNotEmpty) {
      if (user.userImage!.startsWith('http')) {
        return NetworkImage(user.userImage!);
      } else {
        return AssetImage(user.userImage!);
      }
    }
    return null;
  }

  Color _getDonorStatusColor() {
    if (user.donationCount > 0) {
      return Colors.green;
    }
    return Colors.grey;
  }

  IconData _getDonorStatusIcon() {
    if (user.donationCount > 0) {
      return Icons.verified;
    }
    return Icons.person;
  }

  Color _getDonorLevelColor() {
    if (user.donationCount >= 20) {
      return Colors.purple;
    } else if (user.donationCount >= 10) {
      return Colors.orange;
    } else if (user.donationCount >= 5) {
      return Colors.blue;
    } else if (user.donationCount > 0) {
      return Colors.green;
    }
    return Colors.grey;
  }

  String _getDonorLevelText(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final count = user.donationCount;
    if (count >= 1 && count <= 2) {
      return localizations.translate('rookie');
    } else if (count >= 3 && count <= 8) {
      return localizations.translate('red_warrior');
    } else if (count >= 9 && count <= 15) {
      return localizations.translate('active_volunteer');
    } else if (count >= 16 && count <= 25) {
      return localizations.translate('blood_hero');
    } else if (count >= 26 && count <= 50) {
      return localizations.translate('legendary_donor');
    } else if (count >= 51) {
      return localizations.translate('immortal_savior');
    } else {
      return localizations.translate('new_donor');
    }
  }
}