import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/organization_model.dart';

class OrganizationService {
  final UserManager _userManager = UserManager();

  Future<List<OrganizationModel>> getOrganizationsByUserId() async {
    final token = await _userManager.getUserToken() ?? '';

    final response = await ApiConfig.authenticatedGet(
      ApiConfig.organizationByUserId,
      token: token,
      headers: {'Accept': 'text/plain'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final List items = data['Message'];
      return items.map((e) => OrganizationModel.fromJson(e)).toList();
    } else {
      throw Exception('<PERSON><PERSON><PERSON> danh sách tổ chức theo người dùng thất bại: ${response.statusCode}');
    }
  }
}
