import 'dart:ui';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/data/models/donation_event_model.dart';
import 'package:bloodplusmobile/data/services/donation_event_service.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';

class EventDetailScreen extends StatefulWidget {
  final String eventId;

  const EventDetailScreen({super.key, required this.eventId});

  @override
  State<EventDetailScreen> createState() => _EventDetailScreenState();
}

class _EventDetailScreenState extends State<EventDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late AnimationController _progressAnimationController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _fabAnimation;

  DonationEvent? _event;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _fetchEventDetail();
  }

  void _initializeAnimations() {
    _mainAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _mainAnimationController,
          curve: const Interval(0.0, 0.6, curve: Curves.easeOut)),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.2, 0.8, curve: Curves.elasticOut)));
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
          parent: _mainAnimationController,
          curve: const Interval(0.0, 0.6, curve: Curves.elasticOut)),
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _fabAnimationController, curve: Curves.elasticOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_event != null) {
        _mainAnimationController.forward();
        _progressAnimationController.forward();
        _fabAnimationController.forward();
      }
    });
  }

  Future<void> _fetchEventDetail() async {
    try {
      final event = await DonationEventService().getDonationEventById(widget.eventId);
      setState(() {
        _event = event;
        final progress = _event!.requiredDonors > 0
            ? _event!.currentDonors / _event!.requiredDonors
            : 0.0;
        _progressAnimation = Tween<double>(begin: 0.0, end: progress.clamp(0.0, 1.0))
            .animate(CurvedAnimation(
            parent: _progressAnimationController,
            curve: Curves.easeInOutCubic));
        _mainAnimationController.forward(from: 0.0);
        _progressAnimationController.forward(from: 0.0);
        _fabAnimationController.forward(from: 0.0);
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _progressAnimationController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  Future<void> _launchGoogleMaps(String location) async {
    final String encodedLocation = Uri.encodeComponent(location);
    final String googleMapsUrl = 'geo:0,0?q=$encodedLocation';
    final String fallbackUrl = 'https://www.google.com/maps/search/?api=1&query=$encodedLocation';
    final Uri geoUrl = Uri.parse(googleMapsUrl);
    final Uri webUrl = Uri.parse(fallbackUrl);
    final localizations = AppLocalizations.of(context);

    try {
      if (await canLaunchUrl(geoUrl)) {
        await launchUrl(geoUrl, mode: LaunchMode.externalApplication);
      } else if (await canLaunchUrl(webUrl)) {
        await launchUrl(webUrl, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(localizations.translate('could_not_launch_google_maps')),
              backgroundColor: AppColors.primaryRed,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${localizations.translate('could_not_launch_google_maps')}: $e'),
            backgroundColor: AppColors.primaryRed,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    }
  }

  Future<void> _openCalendar(String date) async {
    final parsedDate = DateTime.parse(date);
    final startDateTime = '${parsedDate.toIso8601String().substring(0, 10).replaceAll('-', '')}T${parsedDate.toIso8601String().substring(11, 19).replaceAll(':', '')}Z';
    final endDateTime = '${parsedDate.add(const Duration(days: 1)).toIso8601String().substring(0, 10).replaceAll('-', '')}T000000Z';

    final calendarUrl = Uri.parse(
        'https://www.google.com/calendar/render?action=TEMPLATE&dates=$startDateTime/$endDateTime&text=${Uri.encodeComponent(_event!.title)}&location=${Uri.encodeComponent(_event!.location)}');
    final localizations = AppLocalizations.of(context);

    try {
      if (await canLaunchUrl(calendarUrl)) {
        await launchUrl(calendarUrl, mode: LaunchMode.externalApplication);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.translate('could_not_open_calendar')),
            backgroundColor: AppColors.primaryRed,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${localizations.translate('could_not_open_calendar')}: $e'),
          backgroundColor: AppColors.primaryRed,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  Widget _buildGradientCard({
    required Widget child,
    List<Color>? gradientColors,
    double? delay = 0.0,
    bool isSmallScreen = false,
  }) {
    return AnimatedBuilder(
      animation: _mainAnimationController,
      builder: (context, _) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 20),
                decoration: BoxDecoration(
                  gradient: gradientColors != null
                      ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: gradientColors,
                  )
                      : null,
                  color: gradientColors == null ? Colors.white : null,
                  borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryRed.withOpacity(0.1),
                      blurRadius: isSmallScreen ? 10 : 20,
                      offset: const Offset(0, 8),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: isSmallScreen ? 5 : 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: isSmallScreen ? 5 : 10, sigmaY: isSmallScreen ? 5 : 10),
                    child: Container(
                      padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                      ),
                      child: child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeroSection(bool isSmallScreen) {
    if (_event == null) return Container();
    final localizations = AppLocalizations.of(context);
    String translatedStatus;
    Color statusColor;
    IconData statusIcon;
    switch (_event!.status.toLowerCase()) {
      case 'pending':
        translatedStatus = localizations.translate('Pending');
        statusColor = Colors.orange;
        statusIcon = Icons.accessibility_new_sharp;
        break;
      case 'completed':
        translatedStatus = localizations.translate('Completed');
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'cancelled':
        translatedStatus = localizations.translate('Canceled');
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      default:
        translatedStatus = localizations.translate('status_unknown');
        statusColor = Colors.grey;
        statusIcon = Icons.info;
    }

    return _buildGradientCard(
      gradientColors: [
        AppColors.primaryRed.withOpacity(0.1),
        Colors.pink.withOpacity(0.05),
      ],
      isSmallScreen: isSmallScreen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
                child: Container(
                  height: isSmallScreen ? 180 : 240,
                  width: double.infinity,
                  child: _event!.image != null && _event!.image!.isNotEmpty
                      ? Image.network(
                    _event!.image!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.primaryRed.withOpacity(0.8),
                            Colors.pink.withOpacity(0.6),
                          ],
                        ),
                      ),
                      child: Image.network(
                        (_event!.image != null && _event!.image!.isNotEmpty)
                            ? _event!.image!
                            : 'assets/icons/logo_origin.png',
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                      : Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primaryRed.withOpacity(0.8),
                          Colors.pink.withOpacity(0.6),
                        ],
                      ),
                    ),
                    child: Image.asset(
                      'assets/icons/logo_white.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.3),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                top: isSmallScreen ? 12 : 16,
                left: isSmallScreen ? 12 : 16,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: isSmallScreen ? 4 : 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: isSmallScreen ? 12 : 16, color: Colors.white),
                      SizedBox(width: isSmallScreen ? 2 : 4),
                      Text(
                        translatedStatus,
                        style: GoogleFonts.poppins(
                          fontSize: isSmallScreen ? 10 : 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: isSmallScreen ? 12 : 16,
                right: isSmallScreen ? 12 : 16,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: isSmallScreen ? 4 : 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.schedule, size: isSmallScreen ? 12 : 16, color: AppColors.primaryRed),
                      SizedBox(width: isSmallScreen ? 2 : 4),
                      Text(
                        _event!.getFormattedTime(),
                        style: GoogleFonts.poppins(
                          fontSize: isSmallScreen ? 10 : 12,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primaryRed,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 12 : 20),
          AnimatedBuilder(
            animation: _mainAnimationController,
            builder: (context, _) {
              return Transform.translate(
                offset: Offset(0, isSmallScreen ? 12 : 20 * (1 - _fadeAnimation.value)),
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: Text(
                    _event!.title,
                    style: GoogleFonts.poppins(
                      fontSize: isSmallScreen ? 20 : 28,
                      fontWeight: FontWeight.w800,
                      color: AppColors.textPrimary,
                      height: 1.2,
                    ),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: isSmallScreen ? 8 : 12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: AppColors.primaryRed.withOpacity(0.1),
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.business, color: AppColors.primaryRed, size: isSmallScreen ? 14 : 18),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  _event!.organizationName,
                  style: GoogleFonts.poppins(
                    fontSize: isSmallScreen ? 12 : 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryRed,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(bool isSmallScreen) {
    if (_event == null) return Container();
    final localizations = AppLocalizations.of(context);
    return _buildGradientCard(
      isSmallScreen: isSmallScreen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.translate('event_information'),
            style: GoogleFonts.poppins(
              fontSize: isSmallScreen ? 16 : 20,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          _buildInfoItem(
            icon: Icons.description,
            title: localizations.translate('description'),
            value: _event!.description,
            iconColor: Colors.purple,
            isSmallScreen: isSmallScreen,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
    required Color iconColor,
    bool isSmallScreen = false,
  }) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        border: Border.all(
          color: iconColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
            ),
            child: Icon(icon, color: iconColor, size: isSmallScreen ? 16 : 20),
          ),
          SizedBox(width: isSmallScreen ? 8 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: isSmallScreen ? 10 : 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: isSmallScreen ? 2 : 4),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: isSmallScreen ? 14 : 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSection(bool isSmallScreen) {
    if (_event == null) return Container();
    final localizations = AppLocalizations.of(context);
    return _buildGradientCard(
      isSmallScreen: isSmallScreen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.translate('time_title'),
            style: GoogleFonts.poppins(
              fontSize: isSmallScreen ? 16 : 20,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          _buildInfoItem(
            icon: Icons.access_time,
            title: localizations.translate('time'),
            value: _event!.getFormattedTime(),
            iconColor: Colors.orange,
            isSmallScreen: isSmallScreen,
          ),
        ],
      ),
    );
  }

  Widget _buildDateSection(bool isSmallScreen) {
    if (_event == null) return Container();
    final localizations = AppLocalizations.of(context);
    return _buildGradientCard(
      isSmallScreen: isSmallScreen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calendar_today, color: AppColors.primaryRed, size: isSmallScreen ? 18 : 24),
              SizedBox(width: isSmallScreen ? 6 : 8),
              Text(
                localizations.translate('date'),
                style: GoogleFonts.poppins(
                  fontSize: isSmallScreen ? 16 : 20,
                  fontWeight: FontWeight.w700,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          GestureDetector(
            onTap: () => _openCalendar(_event!.eventDate),
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryRed.withOpacity(0.1),
                    Colors.pink.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                border: Border.all(
                  color: AppColors.primaryRed.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                    decoration: BoxDecoration(
                      color: AppColors.primaryRed.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    ),
                    child: Icon(Icons.calendar_today, color: AppColors.primaryRed, size: isSmallScreen ? 18 : 24),
                  ),
                  SizedBox(width: isSmallScreen ? 12 : 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.translate('tap_to_open_calendar'),
                          style: GoogleFonts.poppins(
                            fontSize: isSmallScreen ? 10 : 12,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        SizedBox(height: isSmallScreen ? 2 : 4),
                        Text(
                          _event!.getFormattedDate(),
                          style: GoogleFonts.poppins(
                            fontSize: isSmallScreen ? 14 : 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryRed,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.open_in_new, color: AppColors.primaryRed, size: isSmallScreen ? 16 : 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection(bool isSmallScreen) {
    if (_event == null) return Container();
    final localizations = AppLocalizations.of(context);
    return _buildGradientCard(
      isSmallScreen: isSmallScreen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: AppColors.primaryRed, size: isSmallScreen ? 18 : 24),
              SizedBox(width: isSmallScreen ? 6 : 8),
              Text(
                localizations.translate('location'),
                style: GoogleFonts.poppins(
                  fontSize: isSmallScreen ? 16 : 20,
                  fontWeight: FontWeight.w700,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          GestureDetector(
            onTap: () => _launchGoogleMaps(_event!.location),
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryRed.withOpacity(0.1),
                    Colors.pink.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                border: Border.all(
                  color: AppColors.primaryRed.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                    decoration: BoxDecoration(
                      color: AppColors.primaryRed.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    ),
                    child: Icon(Icons.map, color: AppColors.primaryRed, size: isSmallScreen ? 18 : 24),
                  ),
                  SizedBox(width: isSmallScreen ? 12 : 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.translate('tap_to_open_maps'),
                          style: GoogleFonts.poppins(
                            fontSize: isSmallScreen ? 10 : 12,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        SizedBox(height: isSmallScreen ? 2 : 4),
                        Text(
                          _event!.location,
                          style: GoogleFonts.poppins(
                            fontSize: isSmallScreen ? 14 : 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryRed,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.open_in_new, color: AppColors.primaryRed, size: isSmallScreen ? 16 : 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(bool isSmallScreen) {
    if (_event == null) return Container();
    final localizations = AppLocalizations.of(context);
    final progress = _event!.requiredDonors > 0
        ? _event!.currentDonors / _event!.requiredDonors
        : 0.0;

    return _buildGradientCard(
      isSmallScreen: isSmallScreen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, color: Colors.green, size: isSmallScreen ? 18 : 24),
              SizedBox(width: isSmallScreen ? 6 : 8),
              Text(
                localizations.translate('progress'),
                style: GoogleFonts.poppins(
                  fontSize: isSmallScreen ? 16 : 20,
                  fontWeight: FontWeight.w700,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 16 : 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                value: '${_event!.currentDonors}',
                label: localizations.translate('registered'),
                color: AppColors.primaryRed,
                icon: Icons.people,
                isSmallScreen: isSmallScreen,
              ),
              Container(
                height: isSmallScreen ? 30 : 40,
                width: 1,
                color: AppColors.borderColor.withOpacity(0.3),
              ),
              _buildStatItem(
                value: '${_event!.requiredDonors}',
                label: localizations.translate('required'),
                color: Colors.blue,
                icon: Icons.flag,
                isSmallScreen: isSmallScreen,
              ),
              Container(
                height: isSmallScreen ? 30 : 40,
                width: 1,
                color: AppColors.borderColor.withOpacity(0.3),
              ),
              _buildStatItem(
                value: '${(progress * 100).toInt()}%',
                label: localizations.translate('completed'),
                color: progress >= 1.0 ? Colors.green : Colors.orange,
                icon: Icons.check_circle,
                isSmallScreen: isSmallScreen,
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 16 : 24),
          Container(
            height: isSmallScreen ? 8 : 12,
            decoration: BoxDecoration(
              color: AppColors.borderColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(isSmallScreen ? 4 : 6),
            ),
            child: AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, _) {
                return FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _progressAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: progress >= 1.0
                            ? [Colors.green, Colors.green.shade700]
                            : [AppColors.primaryRed, Colors.pink],
                      ),
                      borderRadius: BorderRadius.circular(isSmallScreen ? 4 : 6),
                      boxShadow: [
                        BoxShadow(
                          color: (progress >= 1.0
                              ? Colors.green
                              : AppColors.primaryRed)
                              .withOpacity(0.3),
                          blurRadius: isSmallScreen ? 4 : 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String value,
    required String label,
    required Color color,
    required IconData icon,
    bool isSmallScreen = false,
  }) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
          ),
          child: Icon(icon, color: color, size: isSmallScreen ? 12 : 16),
        ),
        SizedBox(height: isSmallScreen ? 6 : 8),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: isSmallScreen ? 14 : 18,
            fontWeight: FontWeight.w800,
            color: color,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: isSmallScreen ? 10 : 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_event == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final progress = _event!.requiredDonors > 0
        ? _event!.currentDonors / _event!.requiredDonors
        : 0.0;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          localizations.translate('event_details'),
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: isSmallScreen ? 16 : 20,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Container(
            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
            ),
            child: Icon(Icons.arrow_back_ios, color: Colors.white, size: isSmallScreen ? 14 : 16),
          ),
          onPressed: () => NavigationService.goBack(),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColors.primaryRed, Colors.pink],
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primaryRed.withOpacity(0.1),
              Colors.grey.shade50,
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.only(top: isSmallScreen ? 80 : 100, left: isSmallScreen ? 12 : 20, right: isSmallScreen ? 12 : 20, bottom: isSmallScreen ? 80 : 100),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeroSection(isSmallScreen),
                _buildInfoSection(isSmallScreen),
                _buildDateSection(isSmallScreen),
                _buildTimeSection(isSmallScreen),
                _buildLocationSection(isSmallScreen),
                _buildProgressSection(isSmallScreen),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: Container(
          width: MediaQuery.of(context).size.width - (isSmallScreen ? 24 : 40),
          height: isSmallScreen ? 48 : 56,
          margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 20),
          decoration: BoxDecoration(
            gradient: progress >= 1.0
                ? LinearGradient(colors: [Colors.grey, Colors.grey.shade600])
                : LinearGradient(colors: [AppColors.primaryRed, Colors.pink]),
            borderRadius: BorderRadius.circular(isSmallScreen ? 24 : 28),
            boxShadow: [
              BoxShadow(
                color: (progress >= 1.0 ? Colors.grey : AppColors.primaryRed)
                    .withOpacity(0.3),
                blurRadius: isSmallScreen ? 10 : 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(isSmallScreen ? 24 : 28),
              onTap: progress >= 1.0
                  ? null
                  : () {
                if (_event != null) {
                  NavigationService.navigateTo(
                    AppRoutes.donationForm,
                    arguments: {'eventId': _event!.id},
                  );
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 24),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      progress >= 1.0 ? Icons.event_busy : Icons.volunteer_activism,
                      color: Colors.white,
                      size: isSmallScreen ? 18 : 24,
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    Text(
                      progress >= 1.0
                          ? localizations.translate('event_full')
                          : localizations.translate('register_now'),
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}