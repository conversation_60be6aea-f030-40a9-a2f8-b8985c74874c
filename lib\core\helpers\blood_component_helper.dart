import 'package:bloodplusmobile/core/language_helper/localization.dart';

String getTranslatedBloodComponent(AppLocalizations localizations, int bloodComponent) {
  switch (bloodComponent) {
    case 0:
      return localizations.translate('red_blood_cells');
    case 1:
      return localizations.translate('plasma');
    case 2:
      return localizations.translate('platelets');
    case 3:
      return localizations.translate('white_blood_cells');
    case 4:
      return localizations.translate('whole_blood');
    default:
      return localizations.translate('unknown');
  }
}

String getTranslatedBloodComponentFromString(AppLocalizations localizations, String bloodComponent) {
  switch (bloodComponent.trim().toLowerCase()) {
    case 'wholeblood':
      return localizations.translate('whole_blood');
    case 'redbloodcells':
      return localizations.translate('red_blood_cells');
    case 'platelets':
      return localizations.translate('platelets');
    case 'plasma':
      return localizations.translate('plasma');
    case 'whitebloodcells':
      return localizations.translate('white_blood_cells');
    default:
      return localizations.translate('unknown');
  }
} 