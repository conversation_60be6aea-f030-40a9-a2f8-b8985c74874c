import 'package:bloodplusmobile/core/widgets/userprofile/action_button.dart';
import 'package:bloodplusmobile/core/widgets/userprofile/info_card.dart';
import 'package:bloodplusmobile/core/widgets/userprofile/profile_header.dart';
import 'package:bloodplusmobile/core/widgets/userprofile/status_card.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class AccountInfoScreen extends StatelessWidget {
  const AccountInfoScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<AppStateNotifier>(context).user;

    if (user == null) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          title: Text(AppLocalizations.of(context).translate('account_info'), style: AppTheme.headingMedium),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            backgroundColor: AppTheme.primaryRed,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                AppLocalizations.of(context).translate('account_info'),
                style: AppTheme.headingMedium.copyWith(color: Colors.white),
              ),
              background: Container(
                decoration: const BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                ),
              ),
            ),
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header
                  ProfileHeader(user: user),

                  const SizedBox(height: 24),

                  // Stats Cards
                  Row(
                    children: [
                      Expanded(
                        child: StatsCard(
                          title: AppLocalizations.of(context).translate('donation_count'),
                          value: user.donationCount.toString(),
                          icon: Icons.favorite,
                          color: AppTheme.primaryRed,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: StatsCard(
                          title: AppLocalizations.of(context).translate('points'),
                          value: user.point.toString(),
                          icon: Icons.stars,
                          color: Colors.amber,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Personal Information
                  Text(
                    AppLocalizations.of(context).translate('personal_info'),
                    style: AppTheme.headingSmall,
                  ),
                  const SizedBox(height: 16),

                  InfoCard(
                    title: AppLocalizations.of(context).translate('basic_info'),
                    items: [
                      InfoItem(
                        icon: Icons.person,
                        label: AppLocalizations.of(context).translate('full_name'),
                        value: user.name,
                      ),
                      InfoItem(
                        icon: Icons.email,
                        label: AppLocalizations.of(context).translate('email'),
                        value: user.email,
                      ),
                      InfoItem(
                        icon: Icons.phone,
                        label: AppLocalizations.of(context).translate('phone_number'),
                        value: user.phoneNumber ?? AppLocalizations.of(context).translate('not_updated'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  InfoCard(
                    title: AppLocalizations.of(context).translate('detail_info'),
                    items: [
                      InfoItem(
                        icon: Icons.cake,
                        label: AppLocalizations.of(context).translate('date_of_birth'),
                        value: user.dateOfBirth != null
                            ? _formatDate(user.dateOfBirth!)
                            : AppLocalizations.of(context).translate('not_updated'),
                      ),
                      InfoItem(
                        icon: _getGenderIcon(user.gender),
                        label: AppLocalizations.of(context).translate('gender'),
                        value: _genderText(user.gender, context),
                      ),
                      InfoItem(
                        icon: Icons.bloodtype,
                        label: AppLocalizations.of(context).translate('blood_type'),
                        value: user.bloodType ?? AppLocalizations.of(context).translate('unknown'),
                        valueColor: AppTheme.primaryRed,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  InfoCard(
                    title: AppLocalizations.of(context).translate('additional_info'),
                    items: [
                      InfoItem(
                        icon: Icons.home,
                        label: AppLocalizations.of(context).translate('address'),
                        value: user.address ?? AppLocalizations.of(context).translate('not_updated'),
                      ),
                      InfoItem(
                        icon: Icons.work,
                        label: AppLocalizations.of(context).translate('job'),
                        value: user.job ?? AppLocalizations.of(context).translate('not_updated'),
                      ),
                      InfoItem(
                        icon: Icons.assignment_ind,
                        label: AppLocalizations.of(context).translate('passport_number'),
                        value: user.passportNumber ?? AppLocalizations.of(context).translate('not_updated'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Action Button
                  ActionButton(
                    text: AppLocalizations.of(context).translate('update_info'),
                    icon: Icons.edit,
                    onPressed: () {
                      NavigationService.navigateTo(AppRoutes.updateUser);
                    },
                  ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static String _formatDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
  }

  static String _genderText(int? gender, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (gender) {
      case 0:
        return localizations.translate('male');
      case 1:
        return localizations.translate('female');
      case 2:
        return localizations.translate('other');
      default:
        return localizations.translate('unknown');
    }
  }

  static IconData _getGenderIcon(int? gender) {
    switch (gender) {
      case 0:
        return Icons.male;
      case 1:
        return Icons.female;
      default:
        return Icons.person;
    }
  }
}