import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/data/models/Enum/appointment_status.dart';
import 'package:bloodplusmobile/data/models/appointment_model.dart';
import 'package:bloodplusmobile/data/services/appointment_service.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/core/widgets/dialog/image_picker_dialog.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/helpers/blood_component_helper.dart';

class HistoryDetailScreen extends StatefulWidget {
  final Appointment appointment;
  final VoidCallback onCancel;
  final VoidCallback? onComplete;

  const HistoryDetailScreen({
    super.key,
    required this.appointment,
    required this.onCancel,
    this.onComplete,
  });

  @override
  State<HistoryDetailScreen> createState() => _HistoryDetailScreenState();
}

class _HistoryDetailScreenState extends State<HistoryDetailScreen>
    with TickerProviderStateMixin {
  final AppointmentService _appointmentService = AppointmentService();
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  Appointment? _currentAppointment;

  @override
  void initState() {
    super.initState();
    _currentAppointment = widget.appointment;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    _animationController.forward();
    _refreshAppointmentData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleAppointmentAction({required bool isCancel, File? certificate}) async {
    setState(() => _isLoading = true);
    try {
      final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);

      if (isCancel) {
        await _appointmentService.cancelAppointment(widget.appointment.id);
      } else {
        if (certificate == null) throw Exception("Missing certificate image");
        await appStateNotifier.refreshAfterAppointmentCompletion(widget.appointment.id, certificationImage: certificate);
      }

      await appStateNotifier.fetchAppointments();
      await _refreshAppointmentData();
      setState(() => _isLoading = false);

      if (!context.mounted) return;

      DialogHelper.showAnimatedSuccessDialog(
        context: context,
        title: AppLocalizations.of(context).translate('success'),
        message: AppLocalizations.of(context).translate(
          isCancel ? 'appointment_canceled' : 'appointment_send',
        ),
        buttonText: AppLocalizations.of(context).translate('ok'),
        icon: Icons.check_circle_outline_rounded,
        iconColor: Colors.green,
        onPressed: () {
          isCancel ? widget.onCancel() : widget.onComplete?.call();
          NavigationService.goBack();
        },
      );
    } catch (e) {
      setState(() => _isLoading = false);
      if (!context.mounted) return;

      DialogHelper.showAnimatedErrorDialog(
        context: context,
        title: AppLocalizations.of(context).translate('error'),
        message: '${AppLocalizations.of(context).translate(isCancel ? 'error_canceling_appointment' : 'error_completing_appointment')}: $e',
        buttonText: AppLocalizations.of(context).translate('ok'),
        icon: Icons.error_outline_rounded,
        iconColor: AppColors.primaryRed,
      );
    }
  }

  Future<void> _refreshAppointmentData() async {
    try {
      final updatedAppointment = await _appointmentService.getAppointmentById(widget.appointment.id);
      setState(() {
        _currentAppointment = updatedAppointment;
      });
    } catch (e) {
      print('Error refreshing appointment data: $e');
      // Keep the original appointment if refresh fails
    }
  }

  Widget _buildAnimatedInfoCard({
    required IconData icon,
    required String label,
    required String value,
    VoidCallback? onTap,
    int delay = 0,
    Widget? customWidget,
    required bool isSmallScreen,
  }) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _fadeAnimation.value) * 20),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: isSmallScreen ? 8 : 10,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: AppColors.borderColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onTap,
                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                  child: Padding(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.primaryRed.withOpacity(0.1),
                                AppColors.primaryRed.withOpacity(0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                          ),
                          child: Icon(
                            icon,
                            color: AppColors.primaryRed,
                            size: isSmallScreen ? 18 : 24,
                          ),
                        ),
                        SizedBox(width: isSmallScreen ? 12 : 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                label,
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 10 : 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textSecondary,
                                  letterSpacing: 0.5,
                                ),
                              ),
                              const SizedBox(height: 4),
                              if (customWidget != null)
                                customWidget
                              else
                                Text(
                                  value,
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 14 : 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                            ],
                          ),
                        ),
                        if (onTap != null)
                          Container(
                            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                            decoration: BoxDecoration(
                              color: AppColors.primaryRed.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                            ),
                            child: Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: isSmallScreen ? 12 : 16,
                              color: AppColors.primaryRed,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String status, BuildContext context, bool isSmallScreen) {
    Color statusColor;
    IconData statusIcon;
    final appointment = _currentAppointment ?? widget.appointment;

    switch (appointment.status) {
      case AppointmentStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.schedule_rounded;
        break;
      case AppointmentStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle_rounded;
        break;
      case AppointmentStatus.cancelled:
        statusColor = Colors.red;
        statusIcon = Icons.cancel_rounded;
        break;
      case AppointmentStatus.needConfirm:
        statusColor = Colors.blue;
        statusIcon = Icons.help_rounded;
        break;
      default:
        statusColor = AppColors.textSecondary;
        statusIcon = Icons.info_rounded;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16, vertical: isSmallScreen ? 6 : 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: isSmallScreen ? 12 : 16,
            color: statusColor,
          ),
          SizedBox(width: isSmallScreen ? 4 : 6),
          Text(
            status,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.w600,
              fontSize: isSmallScreen ? 10 : 12,
            ),
          ),
        ],
      ),
    );
  }

  void _showFullScreenImage(String imageUrl) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            backgroundColor: AppColors.primaryRed,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios_rounded, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Center(
            child: InteractiveViewer(
              boundaryMargin: const EdgeInsets.all(20.0),
              minScale: 0.1,
              maxScale: 5.0,
              child: Image.network(imageUrl),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localization = AppLocalizations.of(context);
    final appointment = _currentAppointment ?? widget.appointment;
    final lang = Localizations.localeOf(context).languageCode;
    final isPending = appointment.status == AppointmentStatus.pending;
    final isPast = DateTime.parse(appointment.appointmentDate).isBefore(DateTime.now());
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.primaryRed.withOpacity(0.1),
                  const Color(0xFFF8F9FA),
                ],
                stops: const [0.0, 0.3],
              ),
            ),
          ),
          RefreshIndicator(
            onRefresh: _refreshAppointmentData,
            color: AppColors.primaryRed,
            child: CustomScrollView(
              slivers: [
                // Custom App Bar
                SliverAppBar(
                  expandedHeight: isSmallScreen ? 100 : 120,
                  floating: false,
                  pinned: true,
                  backgroundColor: AppColors.primaryRed,
                  elevation: 0,
                  leading: Container(
                    margin: EdgeInsets.all(isSmallScreen ? 6 : 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back_ios_rounded, color: Colors.white),
                      onPressed: () => NavigationService.goBack(),
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text(
                      localization.translate('appointment_details'),
                      style: TextStyle(
                        fontSize: isSmallScreen ? 16 : 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.primaryRed,
                            AppColors.darkRed,
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Content
                SliverToBoxAdapter(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (appointment.isEmergency == true) EmergencyNotice(isSmallScreen: isSmallScreen),
                            // Hero Card
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white,
                                    Colors.white.withOpacity(0.8),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(isSmallScreen ? 20 : 24),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primaryRed.withOpacity(0.1),
                                    blurRadius: isSmallScreen ? 15 : 20,
                                    offset: const Offset(0, 8),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: isSmallScreen ? 8 : 10,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            AppColors.primaryRed,
                                            AppColors.darkRed,
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.primaryRed.withOpacity(0.3),
                                            blurRadius: isSmallScreen ? 10 : 15,
                                            offset: const Offset(0, 5),
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        Icons.favorite_rounded,
                                        color: Colors.white,
                                        size: isSmallScreen ? 24 : 32,
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 12 : 16),
                                    Text(
                                      appointment.eventName,
                                      style: TextStyle(
                                        fontSize: isSmallScreen ? 18 : 20,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textPrimary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: isSmallScreen ? 8 : 12),
                                    _buildStatusChip(appointment.getStatusText(context), context, isSmallScreen),
                                  ],
                                ),
                              ),
                            ),

                            SizedBox(height: isSmallScreen ? 16 : 24),

                            // Information Cards
                            _buildAnimatedInfoCard(
                              icon: Icons.calendar_today_rounded,
                              label: localization.translate('date'),
                              value: appointment.getFormattedDate(),
                              onTap: () => _openCalendar(appointment.appointmentDate),
                              isSmallScreen: isSmallScreen,
                            ),
                            _buildAnimatedInfoCard(
                              icon: Icons.access_time_rounded,
                              label: localization.translate('time'),
                              value: appointment.getFormattedTime(),
                              isSmallScreen: isSmallScreen,
                            ),
                            _buildAnimatedInfoCard(
                              icon: Icons.location_on_rounded,
                              label: localization.translate('location'),
                              value: appointment.location,
                              onTap: () => _openGoogleMaps(appointment.location),
                              isSmallScreen: isSmallScreen,
                            ),
                            _buildAnimatedInfoCard(
                              icon: Icons.business_rounded,
                              label: localization.translate('organization'),
                              value: appointment.organizationName,
                              isSmallScreen: isSmallScreen,
                            ),
                            _buildAnimatedInfoCard(
                              icon: Icons.bloodtype_rounded,
                              label: localization.translate('blood_component_history'),
                              value: getTranslatedBloodComponent(localization, appointment.bloodComponent),
                              isSmallScreen: isSmallScreen,
                            ),

                            // Certification Display
                            if (appointment.certification != null && appointment.certification!.isNotEmpty)
                              _buildAnimatedInfoCard(
                                icon: Icons.verified_rounded,
                                label: localization.translate('certification'),
                                value: '',
                                customWidget: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(height: isSmallScreen ? 6 : 8),
                                    GestureDetector(
                                      onTap: () => _showFullScreenImage(appointment.certification!),
                                      child: Container(
                                        width: double.infinity,
                                        height: isSmallScreen ? 150 : 200,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                          border: Border.all(
                                            color: Colors.grey.withOpacity(0.3),
                                            width: 1,
                                          ),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                          child: Image.network(
                                            appointment.certification!,
                                            fit: BoxFit.cover,
                                            loadingBuilder: (context, child, loadingProgress) {
                                              if (loadingProgress == null) return child;
                                              return Center(
                                                child: CircularProgressIndicator(
                                                  value: loadingProgress.expectedTotalBytes != null
                                                      ? loadingProgress.cumulativeBytesLoaded /
                                                      loadingProgress.expectedTotalBytes!
                                                      : null,
                                                  color: AppColors.primaryRed,
                                                ),
                                              );
                                            },
                                            errorBuilder: (context, error, stackTrace) {
                                              return Container(
                                                color: Colors.grey.shade200,
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.error_outline_rounded,
                                                      color: Colors.grey.shade400,
                                                      size: isSmallScreen ? 36 : 48,
                                                    ),
                                                    SizedBox(height: isSmallScreen ? 6 : 8),
                                                    Text(
                                                      localization.translate('error_loading_image'),
                                                      style: TextStyle(
                                                        color: Colors.grey.shade600,
                                                        fontSize: isSmallScreen ? 10 : 12,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 6 : 8),
                                    Text(
                                      localization.translate('certification_description'),
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: isSmallScreen ? 10 : 12,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                                isSmallScreen: isSmallScreen,
                              ),

                            SizedBox(height: isSmallScreen ? 24 : 32),

                            // Action Buttons
                            if ((isPending || appointment.status == AppointmentStatus.needConfirm) && (appointment.certification == null || appointment.certification!.isEmpty))
                              AnimatedBuilder(
                                animation: _fadeAnimation,
                                builder: (context, child) {
                                  return Transform.translate(
                                    offset: Offset(0, (1 - _fadeAnimation.value) * 30),
                                    child: Opacity(
                                      opacity: _fadeAnimation.value,
                                      child: Column(
                                        children: [
                                          // Complete Button
                                          Container(
                                            width: double.infinity,
                                            height: isSmallScreen ? 48 : 56,
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: [
                                                  AppColors.primaryRed,
                                                  AppColors.darkRed,
                                                ],
                                              ),
                                              borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: AppColors.primaryRed.withOpacity(0.3),
                                                  blurRadius: isSmallScreen ? 10 : 15,
                                                  offset: const Offset(0, 5),
                                                ),
                                              ],
                                            ),
                                            child: Material(
                                              color: Colors.transparent,
                                              child: InkWell(
                                                onTap: () => DialogHelper.showConfirmationDialog(
                                                  context: context,
                                                  title: localization.translate('send_evidence_appointment'),
                                                  message: localization.translate('confirm_evidence_appointment'),
                                                  confirmButtonText: localization.translate('yes'),
                                                  cancelButtonText: localization.translate('no'),
                                                  onConfirm: () => showDialog(
                                                    context: context,
                                                    barrierDismissible: false,
                                                    builder: (context) => ImagePickerDialog(
                                                      onImageSelected: (image) {
                                                        if (image != null) {
                                                          _handleAppointmentAction(isCancel: false, certificate: image);
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                  icon: Icons.check_circle_rounded,
                                                  iconColor: Colors.green,
                                                ),
                                                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.check_circle_rounded,
                                                      color: Colors.white,
                                                      size: isSmallScreen ? 18 : 24,
                                                    ),
                                                    SizedBox(width: isSmallScreen ? 8 : 12),
                                                    Text(
                                                      localization.translate('need_confirm'),
                                                      style: TextStyle(
                                                        fontSize: isSmallScreen ? 14 : 16,
                                                        fontWeight: FontWeight.w600,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),

                                          SizedBox(height: isSmallScreen ? 12 : 16),

                                          // Cancel Button
                                          Container(
                                            width: double.infinity,
                                            height: isSmallScreen ? 48 : 56,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                              border: Border.all(
                                                color: AppColors.primaryRed,
                                                width: 2,
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black.withOpacity(0.05),
                                                  blurRadius: isSmallScreen ? 8 : 10,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: Material(
                                              color: Colors.transparent,
                                              child: InkWell(
                                                onTap: () => DialogHelper.showConfirmationDialog(
                                                  context: context,
                                                  title: localization.translate('cancel_appointment'),
                                                  message: localization.translate('confirm_cancel_appointment'),
                                                  confirmButtonText: localization.translate('yes'),
                                                  cancelButtonText: localization.translate('no'),
                                                  onConfirm: () => _handleAppointmentAction(isCancel: true),
                                                  icon: Icons.warning_rounded,
                                                  iconColor: AppColors.primaryRed,
                                                ),
                                                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.cancel_rounded,
                                                      color: AppColors.primaryRed,
                                                      size: isSmallScreen ? 18 : 24,
                                                    ),
                                                    SizedBox(width: isSmallScreen ? 8 : 12),
                                                    Text(
                                                      localization.translate('cancel_appointment'),
                                                      style: TextStyle(
                                                        fontSize: isSmallScreen ? 14 : 16,
                                                        fontWeight: FontWeight.w600,
                                                        color: AppColors.primaryRed,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Loading Overlay
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Container(
                  padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        color: AppColors.primaryRed,
                        strokeWidth: 3,
                      ),
                      SizedBox(height: isSmallScreen ? 12 : 16),
                      Text(
                        localization.translate('processing'),
                        style: TextStyle(
                          fontSize: isSmallScreen ? 14 : 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _openGoogleMaps(String location) async {
    final encoded = Uri.encodeComponent(location);
    final uri = Uri.parse('https://www.google.com/maps/search/?api=1&query=$encoded');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openCalendar(String date) async {
    final parsed = DateTime.parse(date);
    final start = '${parsed.toIso8601String().substring(0, 10).replaceAll('-', '')}T${parsed.toIso8601String().substring(11, 19).replaceAll(':', '')}Z';
    final end = '${parsed.add(const Duration(hours: 1)).toIso8601String().substring(0, 10).replaceAll('-', '')}T${parsed.toIso8601String().substring(11, 19).replaceAll(':', '')}Z';
    final url = Uri.parse('https://www.google.com/calendar/render?action=TEMPLATE&dates=$start/$end&text=${Uri.encodeComponent(widget.appointment.eventName)}&location=${Uri.encodeComponent(widget.appointment.location)}');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }
}

class EmergencyNotice extends StatelessWidget {
  final bool isSmallScreen;
  const EmergencyNotice({Key? key, required this.isSmallScreen}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 10 : 16, vertical: isSmallScreen ? 8 : 12),
      decoration: BoxDecoration(
        color: AppColors.primaryRed.withOpacity(0.08),
        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 14),
        border: Border.all(color: AppColors.primaryRed.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.warning_amber_rounded, color: AppColors.primaryRed, size: isSmallScreen ? 18 : 22),
          SizedBox(width: isSmallScreen ? 8 : 12),
          Expanded(
            child: Text(
              localizations.locale.languageCode == 'vi'
                  ? 'Lưu ý: Đây là lịch hiến máu khẩn cấp. Vui lòng chú ý liên hệ với đơn vị tổ chức nếu cần thiết!'
                  : 'Note: This is an emergency blood donation appointment. Please make sure to contact the organizer if needed!',
              style: TextStyle(
                color: AppColors.primaryRed,
                fontWeight: FontWeight.w600,
                fontSize: isSmallScreen ? 11 : 13,
              ),
            ),
          ),
        ],
      ),
    );
  }
}