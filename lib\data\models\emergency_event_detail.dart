
import 'package:bloodplusmobile/data/models/emergency_event_participant.dart';

class EmergencyEventDetail {
  final String id;
  final String title;
  final String description;
  final String location;
  final DateTime eventDate;
  final DateTime endTime;
  final int requiredDonors;
  final int currentDonors;
  final String? image;
  final bool isEmergency;
  final String requiredBloodType;
  final String organizationName;
  final List<EmergencyEventParticipant> participants;

  EmergencyEventDetail({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.eventDate,
    required this.endTime,
    required this.requiredDonors,
    required this.currentDonors,
    this.image,
    required this.isEmergency,
    required this.requiredBloodType,
    required this.organizationName,
    required this.participants,
  });

  factory EmergencyEventDetail.fromJson(Map<String, dynamic> json) {
    return EmergencyEventDetail(
      id: json['Id'],
      title: json['Title'],
      description: json['Description'] ?? '',
      location: json['Location'],
      eventDate: DateTime.parse(json['EventDate']),
      endTime: DateTime.parse(json['EndTime']),
      requiredDonors: json['RequiredDonors'],
      currentDonors: json['CurrentDonors'],
      image: json['Image'],
      isEmergency: json['IsEmergency'],
      requiredBloodType: json['RequiredBloodType'],
      organizationName: json['OrganizationName'],
      participants: (json['Participants'] as List<dynamic>)
          .map((e) => EmergencyEventParticipant.fromJson(e))
          .toList(),
    );
  }
}
