import 'dart:convert';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/notification_model.dart';
import 'package:bloodplusmobile/data/repositories/notification_response.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final UserManager _userManager = UserManager();
  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;

  List<NotificationModel> get notifications => List.unmodifiable(_notifications);
  bool get isInitialized => _isInitialized;

  // Initialize notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Set up OneSignal notification handlers for push notifications
      _setupOneSignalHandlers();
      
      // Load notifications from API
      await _loadNotifications();
      
      _isInitialized = true;
    } catch (e) {
      print('Error initializing notification service: $e');
    }
  }

  // Set up OneSignal notification handlers for push notifications
  void _setupOneSignalHandlers() {
    // Handle notification received while app is in foreground
    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      // Don't add to local list, just let OneSignal display it
      // The notification will be fetched from API when user opens notification screen
    });

    // Handle notification opened
    OneSignal.Notifications.addClickListener((event) {
      // Navigate based on notification data
      _handleOneSignalNotificationTap(event.notification);
    });

    // Handle permission changes
    OneSignal.Notifications.addPermissionObserver((state) {
      // Handle permission changes silently
    });
  }

  // Handle OneSignal notification tap
  void _handleOneSignalNotificationTap(OSNotification notification) {
    final additionalData = notification.additionalData;
    
    // Navigate based on notification type
    if (additionalData != null) {
      final type = additionalData['type']?.toString() ?? '';
      final targetId = additionalData['targetId']?.toString();
      
      switch (type.toLowerCase()) {
        case 'appointment':
          // Navigate to appointment history
          break;
        case 'event':
          // Navigate to donation events
          break;
        case 'emergency':
          // Navigate to emergency donation
          break;
        default:
          // Navigate to notification screen
          break;
      }
    }
  }

  // Load notifications from API
  Future<void> _loadNotifications() async {
    try {
      final token = await _userManager.getUserToken();
      if (token == null) {
        _notifications = [];
        return;
      }

      final response = await ApiConfig.authenticatedGet(
        ApiConfig.notification,
        token: token,
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final notificationResponse = NotificationListResponse.fromJson(jsonResponse);
        _notifications = notificationResponse.items;
      } else {
        _notifications = [];
      }
    } catch (e) {
      print('Error loading notifications: $e');
      _notifications = [];
    }
  }

  // Refresh notifications from API
  Future<void> refreshNotifications() async {
    await _loadNotifications();
  }

  // Get notification detail and mark as read
  Future<NotificationModel?> getNotificationDetail(String notificationId) async {
    try {
      final token = await _userManager.getUserToken();
      if (token == null) return null;

      final response = await ApiConfig.authenticatedGet(
        '${ApiConfig.notification}/$notificationId',
        token: token,
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final detailResponse = NotificationDetailResponse.fromJson(jsonResponse);
        
        // Update the notification in the list with the read status
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notifications[index] = detailResponse.notification;
        }
        
        return detailResponse.notification;
      }
      return null;
    } catch (e) {
      print('Error getting notification detail: $e');
      return null;
    }
  }

  // Mark notification as read (by calling detail API)
  Future<void> markAsRead(String notificationId) async {
    try {
      await getNotificationDetail(notificationId);
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final token = await _userManager.getUserToken();
      if (token == null) return;

      // Call detail API for each unread notification
      final unreadNotifications = _notifications.where((n) => !n.isRead).toList();
      for (final notification in unreadNotifications) {
        await getNotificationDetail(notification.id);
      }
    } catch (e) {
      print('Error marking all notifications as read: $e');
    }
  }

  // Delete a specific notification (if API supports it)
  Future<void> deleteNotification(String notificationId) async {
    try {
      final token = await _userManager.getUserToken();
      if (token == null) return;

      final response = await ApiConfig.authenticatedDelete(
        '${ApiConfig.notification}/$notificationId',
        token: token,
      );

      if (response.statusCode == 200) {
        _notifications.removeWhere((n) => n.id == notificationId);
      }
    } catch (e) {
      print('Error deleting notification: $e');
    }
  }

  // Clear all notifications (if API supports it)
  Future<void> clearAllNotifications() async {
    try {
      final token = await _userManager.getUserToken();
      if (token == null) return;

      final response = await ApiConfig.authenticatedDelete(
        ApiConfig.notification,
        token: token,
      );

      if (response.statusCode == 200) {
        _notifications.clear();
      }
    } catch (e) {
      print('Error clearing all notifications: $e');
    }
  }

  // Get unread count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  // Get notifications by type
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get recent notifications (last 7 days)
  List<NotificationModel> getRecentNotifications() {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return _notifications
        .where((n) => n.sentDate?.isAfter(sevenDaysAgo) ?? false)
        .toList();
  }

  // Get paginated notifications
  Future<List<NotificationModel>> getNotificationsByPage(int page, {int pageSize = 5}) async {
    try {
      final token = await _userManager.getUserToken();
      if (token == null) return [];

      final response = await ApiConfig.authenticatedGet(
        ApiConfig.notification,
        token: token,
        queryParameters: {
          'page': page.toString(),
          'pageSize': pageSize.toString(),
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final notificationResponse = NotificationListResponse.fromJson(jsonResponse);
        return notificationResponse.items;
      }
      return [];
    } catch (e) {
      print('Error getting notifications by page: $e');
      return [];
    }
  }

  // OneSignal specific methods for push notifications
  Future<void> sendPushNotification({
    required String title,
    required String body,
    Map<String, dynamic>? additionalData,
  }) async {
    // This would be used if you want to send push notifications from the app
    // Usually this is handled by your backend
  }

  Future<void> setOneSignalExternalUserId(String userId) async {
    await OneSignal.login(userId);
  }

  Future<void> logoutOneSignal() async {
    await OneSignal.logout();
  }
} 