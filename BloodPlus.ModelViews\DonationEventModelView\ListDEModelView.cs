﻿namespace BloodPlus.ModelViews.DonationEventModelView
{
    public class ListDEModelView
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string OrganizationName { get; set; }
        public string Location { get; set; }
        public DateTime EventDate { get; set; }
        public DateTime EndTime { get; set; }
        public int RequiredDonors { get; set; }
        public int CurrentDonors { get; set; }
        public string? Image { get; set; }
    }

    public class EDModelView
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string OrganizationName { get; set; }
        public string RequiredBloodTypeName { get; set; }
        public string Location { get; set; }
        public string? ImageUrl { get; set; }
        public DateTime EventDate { get; set; }
        public int RequiredDonors { get; set; }
        public int CurrentDonors { get; set; }
    }
}
