import 'dart:io';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/data/models/emergency_donation_model.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bloodplusmobile/data/services/emergency_donation_service.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/data/services/blood_type_service.dart';
import 'package:bloodplusmobile/data/models/blood_type_model.dart';
import 'package:bloodplusmobile/data/services/organization_service.dart';
import 'package:bloodplusmobile/data/models/organization_model.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'map_location_picker_screen.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';

class CreateEmergencyDonationForm extends StatefulWidget {
  const CreateEmergencyDonationForm({super.key});

  @override
  State<CreateEmergencyDonationForm> createState() =>
      _CreateEmergencyDonationFormState();
}

class _CreateEmergencyDonationFormState
    extends State<CreateEmergencyDonationForm> {
  final _formKey = GlobalKey<FormState>();
  final EmergencyDonationService _service = EmergencyDonationService();
  final BloodTypeService _bloodTypeService = BloodTypeService();
  final OrganizationService _organizationService = OrganizationService();
  final UserManager _userManager = UserManager();

  String title = '';
  String description = '';
  String location = '';
  DateTime eventDate = DateTime.now();
  DateTime endTime = DateTime.now().add(const Duration(days: 30));
  int requiredDonors = 5;
  String bloodTypeId = '';
  String organizationId = '';
  File? selectedImage;

  bool isSubmitting = false;
  List<BloodTypeModel> bloodTypes = [];
  bool isLoadingBloodTypes = true;
  List<OrganizationModel> organizations = [];
  bool isLoadingOrganizations = true;
  final _requiredDonorsController = TextEditingController();
  String? userRole;

  @override
  void initState() {
    super.initState();
    _loadInitData();
    _requiredDonorsController.text = requiredDonors.toString();
  }

  Future<void> _loadInitData() async {
    await _loadUserRole();
    await _loadBloodTypes();
    await _loadOrganizations();
  }

  Future<void> _loadUserRole() async {
    final role = await _userManager.getUserRole();
    setState(() {
      userRole = role;
    });
  }

  @override
  void dispose() {
    _requiredDonorsController.dispose();
    super.dispose();
  }

  Future<void> _loadBloodTypes() async {
    setState(() {
      isLoadingBloodTypes = true;
    });
    try {
      final types = await _bloodTypeService.fetchBloodTypes();
      setState(() {
        bloodTypes = types;
        isLoadingBloodTypes = false;
      });
    } catch (e) {
      setState(() {
        isLoadingBloodTypes = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Lỗi tải nhóm máu: $e')));
    }
  }

  Future<void> _loadOrganizations() async {
    setState(() {
      isLoadingOrganizations = true;
    });
    try {
      final orgs = await _organizationService.getOrganizationsByUserId();
      setState(() {
        organizations = orgs;
        isLoadingOrganizations = false;
        if (organizations.isNotEmpty) {
          organizationId = organizations.first.id;
        }
      });
    } catch (e) {
      setState(() {
        isLoadingOrganizations = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Lỗi tải tổ chức: $e')));
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final result = await picker.pickImage(source: ImageSource.gallery);
    if (result != null) {
      setState(() {
        selectedImage = File(result.path);
      });
    }
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => isSubmitting = true);
    _formKey.currentState!.save();
    requiredDonors = int.tryParse(_requiredDonorsController.text) ?? 5;

    final request = EmergencyDonationRequest(
      isEmergency: true,
      eventDate: eventDate,
      endTime: endTime,
      requiredDonors: requiredDonors,
      organizationId: organizationId,
      requiredBloodType: bloodTypeId,
      location: location,
      imageFile: selectedImage,
      image: '',
      title: title,
      description: description,
    );

    try {
      final success = await _service.createEmergencyDonation(request);
      print("fdsfdsfdsf $success");
      if (success) {
        _formKey.currentState!.reset();
        setState(() => selectedImage = null);
        DialogHelper.showAnimatedSuccessDialog(
          context: context,
          title: 'Thành công',
          message: 'Tạo sự kiện khẩn cấp thành công!',
          buttonText: 'Đóng',
          onPressed: () {
            Navigator.of(context).pop(); // Đóng dialog
            Navigator.of(context).pop(true); // Đóng form, trả về true
            NavigationService.navigateTo(AppRoutes.emergencyDonation);
          },
        );
        return;
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Lỗi: $e')));
    } finally {
      setState(() => isSubmitting = false);
    }
  }

  void _updateEndTimeAfterEventDate(DateTime newEventDate) {
    setState(() {
      eventDate = newEventDate;
      endTime = newEventDate.add(const Duration(days: 30));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Tạo sự kiện khẩn cấp' , style: AppTheme.headingMedium),
        backgroundColor: AppTheme.primaryRed,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      backgroundColor: AppTheme.backgroundColor,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 430),
            child: Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 8,
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 28),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Tạo sự kiện khẩn cấp', style: AppTheme.headingMedium, selectionColor: Colors.white),
                      const SizedBox(height: 16),
                      if (isLoadingOrganizations)
                        const Center(child: CircularProgressIndicator())
                      else if (organizations.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Tổ chức: ${organizations.first.name}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        )
                      else
                        const SizedBox.shrink(),

                      const SizedBox(height: 16),

                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Tiêu đề',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.title),
                        ),
                        onSaved: (val) => title = val ?? '',
                        validator:
                            (val) =>
                                val == null || val.isEmpty
                                    ? 'Nhập tiêu đề'
                                    : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Mô tả',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.description),
                        ),
                        onSaved: (val) => description = val ?? '',
                        maxLines: 3,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Địa điểm',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.location_on),
                          suffixIcon: null, // sẽ thay bằng nút chọn bản đồ
                        ),
                        onSaved: (val) => location = val ?? '',
                        controller: TextEditingController(text: location),
                        readOnly: true,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => MapLocationPickerScreen(),
                                ),
                              );
                              if (result != null && result is String) {
                                setState(() {
                                  location = result;
                                });
                              }
                            },
                            icon: const Icon(Icons.map),
                            label: const Text('Chọn trên bản đồ'),
                            style: AppTheme.secondaryButtonStyle,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              location.isNotEmpty ? location : 'Chưa chọn địa điểm',
                              style: AppTheme.bodyLarge,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      isLoadingBloodTypes
                          ? const Center(child: CircularProgressIndicator())
                          : DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'Nhóm máu',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.bloodtype),
                            ),
                            value: bloodTypeId.isNotEmpty ? bloodTypeId : null,
                            items:
                                bloodTypes
                                    .map(
                                      (type) => DropdownMenuItem(
                                        value: type.bloodTypeId,
                                        child: Text(type.bloodTypeName),
                                      ),
                                    )
                                    .toList(),
                            onChanged: (val) {
                              setState(() {
                                bloodTypeId = val ?? '';
                              });
                            },
                            validator:
                                (val) =>
                                    val == null || val.isEmpty
                                        ? 'Vui lòng chọn nhóm máu'
                                        : null,
                          ),

                      const SizedBox(height: 16),
                      GestureDetector(
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: eventDate,
                            firstDate: DateTime.now(),
                            lastDate: DateTime(2100),
                          );
                          if (picked != null) {
                            final pickedTime = await showTimePicker(
                              context: context,
                              initialTime: TimeOfDay.fromDateTime(eventDate),
                            );
                            if (pickedTime != null) {
                              final newEventDate = DateTime(
                                picked.year,
                                picked.month,
                                picked.day,
                                pickedTime.hour,
                                pickedTime.minute,
                              );
                              _updateEndTimeAfterEventDate(newEventDate);
                            }
                          }
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            decoration: const InputDecoration(
                              labelText: 'Ngày',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.event),
                            ),
                            controller: TextEditingController(
                              text: DateFormat(
                                'yyyy-MM-dd HH:mm',
                              ).format(eventDate),
                            ),
                            readOnly: true,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _requiredDonorsController,
                        decoration: const InputDecoration(
                          labelText: 'Số lượng người hiến cần thiết',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.people),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (val) {
                          if (val == null || val.isEmpty)
                            return 'Nhập số lượng người hiến';
                          final n = int.tryParse(val);
                          if (n == null || n < 1)
                            return 'Số lượng phải lớn hơn 0';
                          return null;
                        },
                        onSaved:
                            (val) =>
                                requiredDonors = int.tryParse(val ?? '5') ?? 5,
                      ),
                      const SizedBox(height: 18),
                      Text('Ảnh minh họa', style: AppTheme.headingSmall),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: _pickImage,
                            icon: const Icon(Icons.image),
                            label: const Text('Chọn ảnh'),
                            style: AppTheme.secondaryButtonStyle,
                          ),
                          const SizedBox(width: 16),
                          if (selectedImage != null)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.file(
                                selectedImage!,
                                height: 60,
                                width: 60,
                                fit: BoxFit.cover,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 28),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: isSubmitting ? null : _submit,
                          style: AppTheme.primaryButtonStyle,
                          child:
                              isSubmitting
                                  ? const SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2.5,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                  : Text(
                                    'Gửi yêu cầu khẩn cấp',
                                    style: AppTheme.buttonText,
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
