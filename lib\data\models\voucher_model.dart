import 'package:bloodplusmobile/data/models/Enum/voucher_type.dart';

class Voucher {
  final String id;
  final String voucherName;
  final VoucherType type;
  final String sponsorName;
  final DateTime expiryDate;
  final String sponsorImage;
  final int point;
  final String description;
  final String? voucherImage;
  final String? userManual;
  final String? support;
  final String? panel;

  Voucher({
    required this.id,
    required this.voucherName,
    required this.type,
    required this.sponsorName,
    required this.expiryDate,
    required this.sponsorImage,
    required this.point,
    required this.description,
    this.voucherImage,
    this.userManual,
    this.support,
    this.panel,
  });

  factory Voucher.fromJson(Map<String, dynamic> json) {
    return Voucher(
      id: json['Id'] ?? '',
      voucherName: json['VoucherName'],
      type: VoucherType.values[json['Type']],
      sponsorName: json['SponsorName'],
      expiryDate: DateTime.parse(json['ExpiryDate']),
      sponsorImage: json['SponsorImage'],
      point: json['Point'],
      description: json['Description'],
      voucherImage: json['VoucherImage'],
      userManual: json['UserManual'],
      support: json['Support'] ?? json['support'],
      panel: json['Panel'],
    );
  }
} 