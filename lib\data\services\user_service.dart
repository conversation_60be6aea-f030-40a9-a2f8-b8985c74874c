import 'dart:convert';
import 'dart:io';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/models/user_model.dart';
import 'package:bloodplusmobile/data/models/user_ranking_model.dart';
import 'package:http/http.dart' as http;

class UserService {
  Future<UserModel> getUserInfo(
    String userId,
    String token, {
    bool forceRefresh = false,
  }) async {
    try {
      final headers = {
        if (forceRefresh) 'Cache-Control': 'no-cache',
        if (forceRefresh) 'Pragma': 'no-cache',
      };

      final response = await ApiConfig.authenticatedGet(
        ApiConfig.userDetailEndpoint(userId),
        token: token,
        headers: headers,
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return UserModel.fromJson(jsonData);
      } else {
        throw Exception(
          '<PERSON><PERSON>y thông tin người dùng thất bại: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Get user info error: $e');
      throw Exception('Lỗi kết nối khi lấy thông tin người dùng: $e');
    }
  }

  Future<Map<String, dynamic>> getDaysWaiting(String userId, String token) async {
    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.userDaysWaiting,
        token: token,
        queryParameters: {'id': userId},
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final messageData = jsonData['Message'] as Map<String, dynamic>?;
        if (messageData == null) {
          throw Exception('Dữ liệu Message không tồn tại trong phản hồi');
        }
        return {
          'daysRemaining': messageData['DaysRemaining'] as int? ?? 0,
          'bloodComponent': messageData['BloodComponent'] as String? ?? '',
        };
      } else {
        throw Exception('Lấy dữ liệu ngày chờ thất bại: ${response.statusCode}');
      }
    } catch (e) {
      print('Get days waiting error: $e');
      throw Exception('Lỗi kết nối khi lấy dữ liệu ngày chờ: $e');
    }
  }

  Future<List<UserRanking>> fetchUserRanking(String token) async {
    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.userRanking,
        token: token,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Request timed out');
        },
      );

      if (response.body.isEmpty) {
        throw Exception('Empty response from server');
      }
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final items = jsonData['Message']['Items'] as List;
        return items.map((e) => UserRanking.fromJson(e)).toList();
      } else {
        throw Exception(
          'Lấy dữ liệu xếp hạng thất bại: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Fetch user ranking error: $e');
      throw Exception('Lỗi kết nối khi lấy dữ liệu xếp hạng: $e');
    }
  }

  Future<UserRankingDetail> fetchUserRankingDetail(
    String id,
    String token,
  ) async {
    try {
      final response = await ApiConfig.authenticatedGet(
        ApiConfig.userRankingDetailEndpoint(id),
        token: token,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Request timed out after 30 seconds');
        },
      );

      if (response.body.isEmpty) {
        throw Exception('Empty response from server');
      }
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return UserRankingDetail.fromJson(jsonData['Message']);
      } else {
        throw Exception(
          'Lấy chi tiết xếp hạng thất bại: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Fetch user ranking detail error: $e');
      throw Exception('Lỗi kết nối khi lấy chi tiết xếp hạng: $e');
    }
  }

  Future<UserModel> updateUserProfileApi({
    required String token,
    String? bloodType,
    String? name,
    String? dateOfBirth,
    String? address,
    String? job,
    int? gender,
    String? passportNumber,
    String? userImagePath,
    String? phoneNumber,
    String? email,
    int? point,
    int? donationCount,
  }) async {
    final url = ApiConfig.getFullUrl(ApiConfig.userUpdateProfile);
    Map<String, String> fields = {};
    if (bloodType != null) fields['BloodType'] = bloodType;
    if (name != null) fields['Name'] = name;
    if (dateOfBirth != null) fields['DateOfBirth'] = dateOfBirth;
    if (address != null) fields['Address'] = address;
    if (job != null) fields['Job'] = job;
    if (gender != null) fields['Gender'] = gender.toString();
    if (passportNumber != null) fields['PassportNumber'] = passportNumber;
    if (phoneNumber != null) fields['PhoneNumber'] = phoneNumber;
    if (email != null) fields['Email'] = email;
    if (point != null) fields['Point'] = point.toString();
    if (donationCount != null)
      fields['DonationCount'] = donationCount.toString();

    try {
      var request = http.MultipartRequest('PUT', Uri.parse(url));
      request.headers['Authorization'] = 'Bearer $token';

      // Thêm tất cả các fields vào request
      fields.forEach((key, value) => request.fields[key] = value);

      // Nếu có ảnh, thêm file vào request
      if (userImagePath != null && userImagePath.isNotEmpty) {
        // Kiểm tra file tồn tại
        final file = File(userImagePath);
        if (!await file.exists()) {
          throw Exception('File ảnh không tồn tại: $userImagePath');
        }

        // Kiểm tra định dạng file ảnh
        final validExtensions = ['.jpg', '.jpeg', '.png'];
        final extension = userImagePath.toLowerCase();
        if (!validExtensions.any((ext) => extension.endsWith(ext))) {
          throw Exception(
            'Định dạng ảnh không hợp lệ. Chỉ hỗ trợ JPG, JPEG, PNG.',
          );
        }

        request.files.add(
          await http.MultipartFile.fromPath('UserImage', userImagePath),
        );
      }

      final streamedResponse = await ApiConfig.httpClient.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        print('Phản hồi từ server: $jsonData');
        return UserModel.fromJson(jsonData['Message'] ?? jsonData);
      } else {
        throw Exception(
          'Cập nhật thông tin thất bại: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Lỗi khi cập nhật: $e');
      throw Exception('Lỗi khi cập nhật: $e');
    }
  }

  Future<String?> changePassword({
    required String token,
    required String currentPassword,
    required String newPassword,
    required String verifyPassword,
  }) async {
    final body = {
      'CurrentPassword': currentPassword,
      'NewPassword': newPassword,
      'VerifyPassword': verifyPassword,
    };
    try {
      final response = await ApiConfig.authenticatedPatch(
        ApiConfig.getFullUrl(ApiConfig.userChangePassword),
        token: token,
        body: body,
      );
      if (response.statusCode == 200) {
        return null; // null = thành công
      } else {
        String? errorMsg;
        try {
          final json = jsonDecode(response.body);
          errorMsg = json['Message'] ?? json['message'] ?? response.body;
        } catch (_) {
          errorMsg = response.body;
        }
        print('Change password failed: \\${response.statusCode} - $errorMsg');
        return errorMsg;
      }
    } catch (e) {
      print('Change password error: \\${e.toString()}');
      return e.toString();
    }
  }

  Future<bool?> changeEmail({required String token}) async {
    try {
      final response = await ApiConfig.authenticatedPost(
        ApiConfig.userChangeEmail,
        token: token,
      );
      if (response.statusCode == 200) {
        return true; // null = thành công
      } else {
        String? errorMsg;
        try {
          final json = jsonDecode(response.body);
          errorMsg = json['Message'] ?? json['message'] ?? response.body;
        } catch (_) {
          errorMsg = response.body;
        }
        print('Change password failed: \\${response.statusCode} - $errorMsg');
        return false;
      }
    } catch (e) {
      print('Change password error: \\${e.toString()}');
      return false;
    }
  }

  Future<bool?> confirmOTP({
    required String token,
    required String email,
    required String otp
  }) async {
    final body = {
      'Email': email,
      'Otp': otp,
    };
    try {
      final response = await ApiConfig.authenticatedPatch(
        ApiConfig.userConfirmOTP,
        token: token,
        body: body
      );
      print("response.body ${response.body}");
      if (!response.body.contains("OTP không chính xác")) {
        return true; // null = thành công
      } else {
        String? errorMsg;
        try {
          final json = jsonDecode(response.body);
          errorMsg = json['Message'] ?? json['message'] ?? response.body;
        } catch (_) {
          errorMsg = response.body;
        }
        print('Change password failed: \\${response.statusCode} - $errorMsg');
        return false;
      }
    } catch (e) {
      print('Change password error: \\${e.toString()}');
      return false;
    }
  }

  Future<bool?> UpdateEmail({
    required String token,
    required String newEmail,
  }) async {
    final body = {
      'NewEmail': newEmail,
    };
    try {
      final response = await ApiConfig.authenticatedPatch(
          ApiConfig.userUpdateEmail,
          token: token,
          body: body
      );
      if (response.statusCode == 200) {
        return true; // null = thành công
      } else {
        String? errorMsg;
        try {
          final json = jsonDecode(response.body);
          errorMsg = json['Message'] ?? json['message'] ?? response.body;
        } catch (_) {
          errorMsg = response.body;
        }
        print('Change email failed: \\${response.statusCode} - $errorMsg');
        return false;
      }
    } catch (e) {
      print('Change email error: \\${e.toString()}');
      return false;
    }
  }
}
