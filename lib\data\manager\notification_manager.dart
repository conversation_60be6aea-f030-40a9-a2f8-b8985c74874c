import 'package:flutter/material.dart';
import 'package:bloodplusmobile/data/models/notification_model.dart';
import 'package:bloodplusmobile/data/services/notification_service.dart';
import 'dart:async';

class NotificationManager extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;
  bool _isLoading = false;

  List<NotificationModel> get notifications => List.unmodifiable(_notifications);
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  Timer? _autoSyncTimer;

  // Initialize notification manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _setLoading(true);
      await _notificationService.initialize();
      _loadNotifications();
      _isInitialized = true;
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setLoading(false);
      print('Error initializing notification manager: $e');
    }
  }

  // Load notifications from service
  void _loadNotifications() {
    _notifications = _notificationService.notifications;
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Refresh notifications from API
  Future<void> refreshNotifications() async {
    try {
      _setLoading(true);
      await _notificationService.refreshNotifications();
      _loadNotifications();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setLoading(false);
      print('Error refreshing notifications: $e');
    }
  }

  void startAutoSync({Duration interval = const Duration(seconds: 5)}) {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = Timer.periodic(interval, (_) {
      refreshNotifications();
    });
  }

  void stopAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = null;
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      _loadNotifications();
      notifyListeners();
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      _setLoading(true);
      await _notificationService.markAllAsRead();
      _loadNotifications();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setLoading(false);
      print('Error marking all notifications as read: $e');
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);
      _loadNotifications();
      notifyListeners();
    } catch (e) {
      print('Error deleting notification: $e');
    }
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      _setLoading(true);
      await _notificationService.clearAllNotifications();
      _loadNotifications();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setLoading(false);
      print('Error clearing all notifications: $e');
    }
  }

  // Get notification detail
  Future<NotificationModel?> getNotificationDetail(String notificationId) async {
    try {
      final notification = await _notificationService.getNotificationDetail(notificationId);
      if (notification != null) {
        _loadNotifications();
        notifyListeners();
      }
      return notification;
    } catch (e) {
      print('Error getting notification detail: $e');
      return null;
    }
  }

  // Get notifications by type
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get recent notifications (last 7 days)
  List<NotificationModel> getRecentNotifications() {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return _notifications
        .where((n) => n.sentDate?.isAfter(sevenDaysAgo) ?? false)
        .toList();
  }

  // Get paginated notifications
  Future<List<NotificationModel>> getNotificationsByPage(int page, {int pageSize = 5}) async {
    try {
      return await _notificationService.getNotificationsByPage(page, pageSize: pageSize);
    } catch (e) {
      print('Error getting notifications by page: $e');
      return [];
    }
  }

  // OneSignal specific methods
  Future<void> setOneSignalUserId(String userId) async {
    try {
      await _notificationService.setOneSignalExternalUserId(userId);
    } catch (e) {
      print('Error setting OneSignal user ID: $e');
    }
  }

  Future<void> logoutOneSignal() async {
    try {
      await _notificationService.logoutOneSignal();
    } catch (e) {
      print('Error logging out OneSignal: $e');
    }
  }
} 