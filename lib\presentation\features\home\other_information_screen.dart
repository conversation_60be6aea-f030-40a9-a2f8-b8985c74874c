import 'dart:io';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

class InformationScreen extends StatefulWidget {
  const InformationScreen({Key? key}) : super(key: key);

  @override
  State<InformationScreen> createState() => _InformationScreenState();
}

class _InformationScreenState extends State<InformationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Filtered lists for search
  List<Map<String, String>> _filteredEmergencyNumbers = [];
  List<Map<String, String>> _filteredDonateHospitals = [];

  final List<Map<String, String>> _emergencyNumbers = [
    {
      'name': 'Trung tâm Truyền máu <PERSON>ợ Rẫy',
      'number': '(+84) 28 3855 5730',
      'icon': 'hospital'
    },
    {
      'name': 'Viện Huyết học - Truyền máu Trung ương',
      'number': '(+84) 24 3868 6008',
      'icon': 'hospital'
    },
    {
      'name': 'Trung tâm Truyền máu Bệnh viện Bạch Mai',
      'number': '(+84) 24 3869 3721',
      'icon': 'hospital'
    },
    {
      'name': 'Hội Chữ thập đỏ TP.HCM',
      'number': '(+84) 28 3829 1151',
      'icon': 'emergency'
    },
    {
      'name': 'Trung tâm Cấp cứu 115 TP.HCM',
      'number': '115',
      'icon': 'emergency'
    },
    {
      'name': 'Trung tâm Cấp cứu 115 Hà Nội',
      'number': '115',
      'icon': 'emergency'
    },
  ];

  final List<Map<String, String>> _donateHospitals = [
    {
      'name': 'Bệnh viện Chợ Rẫy',
      'address': '201B Nguyễn Chí Thanh, Quận 5, TP.HCM',
      'phone': '(+84) 28 3855 4137',
      'hours': '8:00 - 17:00',
      'rating': '4.5'
    },
    {
      'name': 'Bệnh viện Bạch Mai',
      'address': '78 Giải Phóng, Đống Đa, Hà Nội',
      'phone': '(+84) 24 3869 3721',
      'hours': '8:00 - 17:00',
      'rating': '4.3'
    },
    {
      'name': 'Bệnh viện Việt Đức',
      'address': '40 Tràng Thi, Hoàn Kiếm, Hà Nội',
      'phone': '(+84) 24 3825 3531',
      'hours': '8:00 - 17:00',
      'rating': '4.4'
    },
    {
      'name': 'Bệnh viện FV',
      'address': '6 Nguyễn Lương Bằng, Quận 7, TP.HCM',
      'phone': '(+84) 28 5411 3333',
      'hours': '24/7',
      'rating': '4.7'
    },
    {
      'name': 'Bệnh viện Vinmec Central Park',
      'address': '208 Nguyễn Hữu Cảnh, Quận Bình Thạnh, TP.HCM',
      'phone': '(+84) 28 3622 1166',
      'hours': '24/7',
      'rating': '4.6'
    },
    {
      'name': 'Bệnh viện Từ Dũ',
      'address': '284 Cống Quỳnh, Quận 1, TP.HCM',
      'phone': '(+84) 28 5404 2829',
      'hours': '8:00 - 17:00',
      'rating': '4.2'
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );

    // Initialize filtered lists
    _filteredEmergencyNumbers = List.from(_emergencyNumbers);
    _filteredDonateHospitals = List.from(_donateHospitals);

    _fadeController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _filterLists(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredEmergencyNumbers = List.from(_emergencyNumbers);
        _filteredDonateHospitals = List.from(_donateHospitals);
      } else {
        final lowerQuery = query.toLowerCase();
        _filteredEmergencyNumbers = _emergencyNumbers
            .where((item) => item['name']!.toLowerCase().contains(lowerQuery))
            .toList();
        _filteredDonateHospitals = _donateHospitals
            .where((item) =>
        item['name']!.toLowerCase().contains(lowerQuery) ||
            item['address']!.toLowerCase().contains(lowerQuery))
            .toList();
      }
    });
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    try {
      // Kiểm tra và yêu cầu quyền trên Android
      if (Platform.isAndroid) {
        bool hasPermission = await _requestPhonePermission();
        if (!hasPermission) {
          _showErrorSnackBar('Quyền gọi điện bị từ chối');
          return;
        }
      }

      // Làm sạch số điện thoại
      String cleanedNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // Thêm haptic feedback
      await HapticFeedback.lightImpact();

      // Thực hiện cuộc gọi
      final Uri launchUri = Uri(scheme: 'tel', path: cleanedNumber);
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      } else {
        _showErrorSnackBar('Không thể thực hiện cuộc gọi');
      }
    } catch (e) {
      _showErrorSnackBar('Lỗi: $e');
    }
  }

  Future<bool> _requestPhonePermission() async {
    var status = await Permission.phone.status;
    if (!status.isGranted) {
      status = await Permission.phone.request();
    }
    return status.isGranted;
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: AppTheme.bodyMedium.copyWith(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: AppTheme.primaryRed,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Đóng',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          _buildSliverAppBar(localizations, innerBoxIsScrolled, isSmallScreen),
        ],
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              _buildTabBar(localizations, isSmallScreen),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  physics: const BouncingScrollPhysics(),
                  children: [
                    _buildEmergencyTab(localizations, isSmallScreen),
                    _buildDonateTab(localizations, isSmallScreen),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(AppLocalizations localizations, bool innerBoxIsScrolled, bool isSmallScreen) {
    return SliverAppBar(
      expandedHeight: isSmallScreen ? 100 : 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppTheme.primaryRed,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: FlexibleSpaceBar(
          title: Text(
            localizations.translate('information'),
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: isSmallScreen ? 18 : 22,
            ),
          ),
          centerTitle: true,
          titlePadding: EdgeInsets.only(bottom: isSmallScreen ? 8 : 16),
        ),
      ),
      leading: IconButton(
        icon: Container(
          padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
          ),
          child: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Container(
            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            ),
            child: const Icon(Icons.notifications_outlined, color: Colors.white, size: 20),
          ),
          onPressed: () {
            _showErrorSnackBar('Tính năng thông báo chưa được triển khai');
          },
        ),
        SizedBox(width: isSmallScreen ? 4 : 8),
      ],
    );
  }

  Widget _buildTabBar(AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.fromLTRB(isSmallScreen ? 12 : 20, 16, isSmallScreen ? 12 : 20, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: isSmallScreen ? 10 : 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelStyle: AppTheme.headingSmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: isSmallScreen ? 14 : 16,
        ),
        unselectedLabelStyle: AppTheme.bodyMedium.copyWith(
          color: AppTheme.textSecondary,
          fontWeight: FontWeight.w500,
          fontSize: isSmallScreen ? 12 : 14,
        ),
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryRed.withOpacity(0.3),
              blurRadius: isSmallScreen ? 4 : 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelPadding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
        padding: EdgeInsets.all(isSmallScreen ? 4 : 6),
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.emergency, size: isSmallScreen ? 16 : 18),
                SizedBox(width: isSmallScreen ? 4 : 8),
                Text(localizations.translate('emergency_number_hotline')),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.local_hospital, size: isSmallScreen ? 16 : 17),
                SizedBox(width: isSmallScreen ? 4 : 8),
                Text(localizations.translate('donate_blood')),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 20, vertical: isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: isSmallScreen ? 10 : 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: localizations.translate('search'),
          hintStyle: AppTheme.bodyMedium.copyWith(
            color: AppTheme.textSecondary.withOpacity(0.7),
            fontSize: isSmallScreen ? 14 : 16,
          ),
          prefixIcon: Container(
            padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
            child: Icon(
              Icons.search_rounded,
              color: AppTheme.primaryRed,
              size: isSmallScreen ? 18 : 22,
            ),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
            icon: Container(
              padding: EdgeInsets.all(isSmallScreen ? 2 : 4),
              decoration: BoxDecoration(
                color: AppTheme.textSecondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
              ),
              child: Icon(
                Icons.close,
                color: AppTheme.textSecondary,
                size: isSmallScreen ? 12 : 16,
              ),
            ),
            onPressed: () {
              _searchController.clear();
              _filterLists('');
            },
          )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            borderSide: BorderSide(color: AppTheme.primaryRed, width: isSmallScreen ? 1 : 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: EdgeInsets.symmetric(vertical: isSmallScreen ? 10 : 16, horizontal: isSmallScreen ? 12 : 20),
        ),
        style: AppTheme.bodyLarge.copyWith(color: AppTheme.textPrimary, fontSize: isSmallScreen ? 14 : 16),
        onChanged: _filterLists,
      ),
    );
  }

  Widget _buildEmergencyTab(AppLocalizations localizations, bool isSmallScreen) {
    return Column(
      children: [
        _buildSearchBar(localizations, isSmallScreen),
        Expanded(
          child: _filteredEmergencyNumbers.isEmpty
              ? _buildEmptyState(localizations, 'Không tìm thấy số cấp cứu', isSmallScreen)
              : ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 20),
            physics: const BouncingScrollPhysics(),
            itemCount: _filteredEmergencyNumbers.length,
            itemBuilder: (context, index) {
              final emergency = _filteredEmergencyNumbers[index];
              return _AnimatedCard(
                index: index,
                child: _buildEmergencyCard(emergency, localizations, isSmallScreen),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDonateTab(AppLocalizations localizations, bool isSmallScreen) {
    return Column(
      children: [
        _buildSearchBar(localizations, isSmallScreen),
        Expanded(
          child: _filteredDonateHospitals.isEmpty
              ? _buildEmptyState(localizations, 'Không tìm thấy bệnh viện', isSmallScreen)
              : ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 20),
            physics: const BouncingScrollPhysics(),
            itemCount: _filteredDonateHospitals.length,
            itemBuilder: (context, index) {
              final hospital = _filteredDonateHospitals[index];
              return _AnimatedCard(
                index: index,
                child: _buildHospitalCard(hospital, localizations, isSmallScreen),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations, String message, bool isSmallScreen) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: isSmallScreen ? 48 : 64,
            color: AppTheme.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          Text(
            message,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textSecondary,
              fontSize: isSmallScreen ? 16 : 18,
            ),
          ),
          SizedBox(height: isSmallScreen ? 6 : 8),
          Text(
            'Thử tìm kiếm với từ khóa khác',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary.withOpacity(0.7),
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyCard(Map<String, String> emergency, AppLocalizations localizations, bool isSmallScreen) {
    final isEmergency = emergency['icon'] == 'emergency';

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: isSmallScreen ? 10 : 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
          onTap: () => _makePhoneCall(emergency['number']!),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            child: Row(
              children: [
                Container(
                  width: isSmallScreen ? 40 : 56,
                  height: isSmallScreen ? 40 : 56,
                  decoration: BoxDecoration(
                    gradient: isEmergency
                        ? LinearGradient(
                      colors: [Colors.red.shade400, Colors.red.shade600],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                        : LinearGradient(
                      colors: [AppTheme.primaryRed, AppTheme.secondaryRed],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 16),
                    boxShadow: [
                      BoxShadow(
                        color: (isEmergency ? Colors.red : AppTheme.primaryRed).withOpacity(0.3),
                        blurRadius: isSmallScreen ? 6 : 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    isEmergency ? Icons.emergency : Icons.local_hospital,
                    color: Colors.white,
                    size: isSmallScreen ? 20 : 28,
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        emergency['name']!,
                        style: AppTheme.headingSmall.copyWith(
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.w600,
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 2 : 4),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: isSmallScreen ? 12 : 16,
                            color: AppTheme.textSecondary,
                          ),
                          SizedBox(width: isSmallScreen ? 4 : 6),
                          Text(
                            emergency['number']!,
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.w500,
                              fontSize: isSmallScreen ? 12 : 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  ),
                  child: IconButton(
                    icon: Icon(Icons.phone, color: Colors.green, size: isSmallScreen ? 16 : 20),
                    onPressed: () => _makePhoneCall(emergency['number']!),
                    tooltip: 'Gọi điện',
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHospitalCard(Map<String, String> hospital, AppLocalizations localizations, bool isSmallScreen) {
    final rating = double.tryParse(hospital['rating'] ?? '0') ?? 0.0;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: isSmallScreen ? 10 : 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 20),
          onTap: () => _makePhoneCall(hospital['phone']!),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: isSmallScreen ? 40 : 56,
                      height: isSmallScreen ? 40 : 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [AppTheme.secondaryRed, AppTheme.primaryRed],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 16),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.secondaryRed.withOpacity(0.3),
                            blurRadius: isSmallScreen ? 6 : 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.local_hospital,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            hospital['name']!,
                            style: AppTheme.headingSmall.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w600,
                              fontSize: isSmallScreen ? 14 : 16,
                            ),
                          ),
                          SizedBox(height: isSmallScreen ? 2 : 4),
                          Row(
                            children: [
                              ...List.generate(5, (index) => Icon(
                                index < rating.floor() ? Icons.star : Icons.star_border,
                                size: isSmallScreen ? 12 : 16,
                                color: Colors.amber,
                              )),
                              SizedBox(width: isSmallScreen ? 4 : 6),
                              Text(
                                rating.toString(),
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.textSecondary,
                                  fontWeight: FontWeight.w500,
                                  fontSize: isSmallScreen ? 10 : 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                _buildInfoRow(Icons.location_on, hospital['address']!, isSmallScreen),
                SizedBox(height: isSmallScreen ? 6 : 8),
                _buildInfoRow(Icons.phone, hospital['phone']!, isSmallScreen),
                SizedBox(height: isSmallScreen ? 6 : 8),
                _buildInfoRow(Icons.access_time, hospital['hours']!, isSmallScreen),
                SizedBox(height: isSmallScreen ? 12 : 16),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                        ),
                        child: TextButton.icon(
                          onPressed: () {
                            _showErrorSnackBar('Tính năng xem vị trí chưa được triển khai');
                          },
                          icon: Icon(Icons.map, color: Colors.blue, size: isSmallScreen ? 14 : 18),
                          label: Text(
                            'Xem vị trí',
                            style: TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.w600,
                              fontSize: isSmallScreen ? 12 : 14,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                      ),
                      child: IconButton(
                        onPressed: () => _makePhoneCall(hospital['phone']!),
                        icon: Icon(Icons.phone, color: Colors.green, size: isSmallScreen ? 16 : 20),
                        tooltip: 'Gọi điện',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text, bool isSmallScreen) {
    return Row(
      children: [
        Icon(
          icon,
          size: isSmallScreen ? 12 : 16,
          color: AppTheme.textSecondary,
        ),
        SizedBox(width: isSmallScreen ? 4 : 8),
        Expanded(
          child: Text(
            text,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontSize: isSmallScreen ? 12 : 14,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class _AnimatedCard extends StatefulWidget {
  final Widget child;
  final int index;

  const _AnimatedCard({
    required this.child,
    required this.index,
  });

  @override
  _AnimatedCardState createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<_AnimatedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 600 + (widget.index * 100)),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutBack),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}