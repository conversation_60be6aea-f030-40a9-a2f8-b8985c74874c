﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\BloodPlus.Repositories\BloodPlus.Repositories.csproj">
      <Private>True</Private>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.69.0" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="4.13.0" />
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
  </ItemGroup>

</Project>
