import 'package:bloodplusmobile/data/models/voucher_model.dart';
import 'package:bloodplusmobile/presentation/features/chat_ai_screen.dart';
import 'package:bloodplusmobile/presentation/features/auth/create_new_password_screen.dart';
import 'package:bloodplusmobile/presentation/features/auth/forgot_password_screen.dart';
import 'package:bloodplusmobile/presentation/features/auth/verify_otp_password_screen.dart';
import 'package:bloodplusmobile/presentation/features/emergency/emergency_donation_screen.dart';
import 'package:bloodplusmobile/presentation/features/home/<USER>';
import 'package:bloodplusmobile/presentation/features/user/account_infor_screen.dart';
import 'package:bloodplusmobile/presentation/features/user/change_email_screen.dart';
import 'package:bloodplusmobile/presentation/features/user/update_user_screen.dart';
import 'package:bloodplusmobile/presentation/features/voucher/my_vouchers_screen.dart';
import 'package:bloodplusmobile/presentation/features/voucher/available_voucher_detail_screen.dart';
import 'package:bloodplusmobile/presentation/features/voucher/user_voucher_detail_screen.dart';
import 'package:bloodplusmobile/presentation/features/notification/notification_screen.dart';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/presentation/features/auth/login_screen.dart';
import 'package:bloodplusmobile/presentation/features/blog/blog_screen.dart';
import 'package:bloodplusmobile/presentation/features/blog/blog_detail_screen.dart';
import 'package:bloodplusmobile/presentation/features/home/<USER>';
import 'package:bloodplusmobile/presentation/features/home/<USER>';
import 'package:bloodplusmobile/presentation/features/home/<USER>';
import 'package:bloodplusmobile/presentation/features/onboarding/started_screen.dart';
import 'package:bloodplusmobile/presentation/features/onboarding/splash_screen.dart';
import 'package:bloodplusmobile/presentation/features/schedule/donation_event_screen.dart';
import 'package:bloodplusmobile/presentation/features/schedule/donation_event_detail_screen.dart';
import 'package:bloodplusmobile/presentation/features/schedule/donation_form_screen.dart';
import 'package:bloodplusmobile/presentation/features/schedule/donation_history_screen.dart';
import 'package:bloodplusmobile/presentation/features/schedule/history_detail_screen.dart';
import 'package:bloodplusmobile/presentation/features/user/profile_screen.dart';
import 'package:bloodplusmobile/presentation/features/user/settings_screen.dart';
import 'package:bloodplusmobile/presentation/features/voucher/voucher_screen.dart';
import 'package:bloodplusmobile/presentation/features/home/<USER>';
import 'package:bloodplusmobile/data/models/donation_event_model.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case AppRoutes.splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
          settings: const RouteSettings(name: AppRoutes.splash),
        );

      case AppRoutes.started:
        return MaterialPageRoute(
          builder: (_) => const StartedScreen(),
          settings: const RouteSettings(name: AppRoutes.started),
        );


      case AppRoutes.login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());

      case AppRoutes.forgotPassword:
        return MaterialPageRoute(builder: (_) => const ForgotPasswordScreen());

      case AppRoutes.createNewPassword:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => CreateNewPasswordScreen(email: args['email']),
          );
        }
        return _errorRoute();

      case AppRoutes.home:
        return MaterialPageRoute(
          builder: (_) => const HomeScreen(),
          settings: const RouteSettings(name: AppRoutes.home),
        );

      case AppRoutes.changeEmail:
        return MaterialPageRoute(builder: (_) => const ChangeEmailScreen());

      case AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());

      case AppRoutes.settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());

      case AppRoutes.accountInfo:
        return MaterialPageRoute(builder: (_) => const AccountInfoScreen());

      case AppRoutes.emergencyDonation:
        return MaterialPageRoute(builder: (_) => const EmergencyDonationScreen());

      case AppRoutes.notification:
        return MaterialPageRoute(builder: (_) => const NotificationScreen());

      case AppRoutes.donationEvent:
        return MaterialPageRoute(builder: (_) => const DonationEventScreen());

      case AppRoutes.donationEventDetail:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => EventDetailScreen(eventId: args['eventId']),
          );
        }
        return _errorRoute();

      case AppRoutes.donationForm:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => DonationFormScreen(eventId: args['eventId']),
          );
        }
        return _errorRoute();

      case AppRoutes.donationHistory:
        return MaterialPageRoute(builder: (_) => const HistoryScreen());

      case AppRoutes.historyDetail:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder:
                (_) => HistoryDetailScreen(
                  appointment: args['appointment'],
                  onCancel: args['onCancel'],
                  onComplete: args['onComplete'],
                ),
          );
        }
        return _errorRoute();

      case AppRoutes.blog:
        return MaterialPageRoute(builder: (_) => const BlogScreen());

      case AppRoutes.blogDetail:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => BlogDetailScreen(blogId: args['blogId']),
          );
        }
        return _errorRoute();

      case AppRoutes.expertAdvice:
        return MaterialPageRoute(builder: (_) => const ExpertAdviceScreen());

      case AppRoutes.otherInformation:
        return MaterialPageRoute(builder: (_) => const InformationScreen());

      case AppRoutes.voucher:
        return MaterialPageRoute(builder: (_) => const VoucherScreen());

      case AppRoutes.availableVoucherDetail:
        if (args is Map<String, dynamic>) {
          final voucher = args['voucher'] as Voucher?;
          if (voucher != null) {
            return MaterialPageRoute(
              builder: (_) => AvailableVoucherDetailScreen(voucher: voucher),
            );
          }
        }
        return _errorRoute();

      case AppRoutes.userVoucherDetail:
        if (args is Map<String, dynamic>) {
          final voucher = args['voucher'] as Voucher?;
          if (voucher != null) {
            return MaterialPageRoute(
              builder: (_) => UserVoucherDetailScreen(voucher: voucher),
            );
          }
        }
        return _errorRoute();

      case AppRoutes.myVouchersScreen:
        return MaterialPageRoute(builder: (_) => const MyVouchersScreen());

      case AppRoutes.leaderboard:
        return MaterialPageRoute(builder: (_) => const UserRankingScreen());

      case AppRoutes.chatAI:
        return MaterialPageRoute(
          builder: (_) => const ChatAIScreen(),
          settings: const RouteSettings(name: AppRoutes.chatAI),
        );

      case AppRoutes.nearbyEvent:
        if (args is List<dynamic> &&
            args.isNotEmpty &&
            args.first is DonationEvent) {
          return MaterialPageRoute(
            builder:
                (_) => NearbyEventScreen(events: args.cast<DonationEvent>()),
          );
        } else if (args is List<DonationEvent>) {
          return MaterialPageRoute(
            builder: (_) => NearbyEventScreen(events: args),
          );
        }
        return _errorRoute();

      case AppRoutes.forgotPassword:
        return MaterialPageRoute(builder: (_) => const ForgotPasswordScreen());

      case AppRoutes.createNewPassword:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => CreateNewPasswordScreen(email: args['email']),
          );
        }
        return _errorRoute();

      case AppRoutes.verifyOtpPassword:
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => VerifyOtpPasswordScreen(email: args['email']),
          );
        }
        return _errorRoute();

      case AppRoutes.updateUser:
        return MaterialPageRoute(builder: (_) => const UpdateUserScreen());

      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(
      builder: (_) {
        return Scaffold(
          appBar: AppBar(title: const Text('Error')),
          body: const Center(child: Text('Route not found!')),
        );
      },
    );
  }
}
