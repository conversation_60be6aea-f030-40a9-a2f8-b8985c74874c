class ChatMessage {
  final int? id;
  final String role;
  final String message;
  final DateTime timestamp;
  final String conversationId;

  ChatMessage({
    this.id,
    required this.role,
    required this.message,
    required this.timestamp,
    required this.conversationId,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['Id'] is int ? json['Id'] as int : null,
      role: json['Role']?.toString() ?? '',
      message: json['Message']?.toString() ?? '',
      timestamp: DateTime.tryParse(json['Timestamp']?.toString() ?? '')?.toUtc()
          ?? DateTime.now().toUtc(),
      conversationId: json['ConversationId']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Id': id,
      'Role': role,
      'Message': message,
      'Timestamp': timestamp.toIso8601String(), // sẽ có hậu tố Z
      'ConversationId': conversationId,
    };
  }
}
