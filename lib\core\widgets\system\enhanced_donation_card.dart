import 'package:flutter/material.dart';

class EnhancedDonationCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final List<Color> gradientColors;
  final Color shadowColor;

  const EnhancedDonationCard({
    Key? key,
    required this.child,
    this.onTap,
    this.gradientColors = const [Color(0x00ffffff), Color(0x00ffffff)],
    this.shadowColor = Colors.transparent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        constraints: BoxConstraints(
          minHeight: screenWidth * 0.35, // 140px equivalent on 400px screen
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.25),
              Colors.white.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(screenWidth * 0.05), // 20px equivalent
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: shadowColor.withOpacity(0.2),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.03), // 12px equivalent
          child: child,
        ),
      ),
    );
  }
}