name: 🚀 Deploy .NET 8 Web API to IIS on VPS

on:
  push:
    branches: [ "main" ]  # <PERSON><PERSON><PERSON> khi có push vào nhánh main

jobs:
  build-and-deploy:
    runs-on: [self-hosted, windows]  # VPS có GitHub runner tự host

    env:
      PUBLISH_PATH: "C:\\inetpub\\wwwroot\\publish"  # Đường dẫn publish đến IIS

    steps:
      - name: 📥 Checkout source code
        uses: actions/checkout@v3

      - name: ⚙️ Setup .NET 8 SDK
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: 📦 Restore NuGet packages
        run: dotnet restore ./BloodPlus.API/BloodPlus.API.csproj

      - name: 🛑 Stop IIS AppPool before cleaning
        shell: powershell
        run: |
          Import-Module WebAdministration
          $appPoolPath = "IIS:\AppPools\BloodPlusAPI"
          if (Test-Path $appPoolPath) {
            $appPool = Get-Item $appPoolPath
            if ($appPool.State -ne "Stopped") {
              $appPool.Stop()
              Write-Host "AppPool 'BloodPlusAPI' has been stopped."
            } else {
              Write-Host "AppPool 'BloodPlusAPI' was already stopped."
            }
          } else {
            Write-Host "AppPool 'BloodPlusAPI' does not exist."
          }

      - name: 🧹 Clean old publish directory
        shell: powershell
        run: |
          if (Test-Path "$env:PUBLISH_PATH") {
            Remove-Item -Recurse -Force "$env:PUBLISH_PATH\*" -ErrorAction SilentlyContinue
            Write-Host "Old files deleted from publish folder."
          } else {
            New-Item -Path "$env:PUBLISH_PATH" -ItemType Directory | Out-Null
            Write-Host "Created new publish folder."
          }

      - name: 🛠️ Build and Publish to IIS path
        run: dotnet publish ./BloodPlus.API/BloodPlus.API.csproj -c Release -o "$env:PUBLISH_PATH"

      - name: 🔁 Restart IIS AppPool for BloodPlusAPI
        shell: powershell
        run: |
          Import-Module WebAdministration
          $appPoolPath = "IIS:\AppPools\BloodPlusAPI"
          if (Test-Path $appPoolPath) {
            $appPool = Get-Item $appPoolPath
            $appPool.Start()
            Write-Host "AppPool 'BloodPlusAPI' restarted."
          } else {
            Write-Host "AppPool 'BloodPlusAPI' does not exist."
          }

      - name: ✅ Verify publish result
        shell: powershell
        run: |
          Write-Host "Files published to: $env:PUBLISH_PATH"
          Get-ChildItem "$env:PUBLISH_PATH" -Recurse