import 'dart:io';
import 'package:bloodplusmobile/data/models/emergency_donation_model.dart';
import 'package:bloodplusmobile/data/models/emergency_event_detail.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:http_parser/http_parser.dart';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'dart:convert';


class EmergencyDonationService {
  final UserManager _userManager = UserManager();

  Future<bool> createEmergencyDonation(EmergencyDonationRequest request) async {
    final token = await _userManager.getUserToken();
    if (token == null || token.isEmpty) {
      throw Exception('Token không hợp lệ');
    }

    try {
      // Fields gửi kèm
      final fields = {
        'IsEmergency': request.isEmergency.toString(),
        'EventDate': request.eventDate.toIso8601String(),
        'EndTime': request.endTime.toIso8601String(),
        'RequiredDonors': request.requiredDonors.toString(),
        'OrganizationId': request.organizationId,
        'RequiredBloodType': request.requiredBloodType,
        'Location': request.location,
        'Title': request.title,
        'Description': request.description,
        'Image': request.image, // Trường hợp nếu không dùng imageFile
      };

      final files = <String, http.MultipartFile>{};

      // Nếu có ảnh từ file
      if (request.imageFile != null) {
        final file = request.imageFile!;
        final fileName = basename(file.path);
        final mimeType = _detectMimeType(file.path);

        final multipartImage = await http.MultipartFile.fromPath(
          'imageFile',
          file.path,
          filename: fileName,
          contentType: mimeType,
        );

        files['imageFile'] = multipartImage;
      }

      final response = await ApiConfig.multipartPost(
        ApiConfig.emergencyDonation,
        token: token,
        fields: fields,
        files: files.isNotEmpty ? files : null,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        throw Exception('Tạo sự kiện thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<PaginationModel<EmergencyDonationEventModel>> getEmergencyEvents({
    int page = 1,
    int pageSize = 5,
    bool isUser = true
  }) async {
    final token = await _userManager.getUserToken() ?? '';

    final response = await ApiConfig.authenticatedGet(
      ApiConfig.emergencyDonation,
      token: token,
      queryParameters: {
        'isUser': isUser,
        'pageNumber': page,
        'pageSize': pageSize,

      },
      headers: {'Accept': 'text/plain'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return PaginationModel.fromJson(
        data['Message'],
        (dynamic e) => EmergencyDonationEventModel.fromJson(e as Map<String, dynamic>),
      );
    } else {
      throw Exception(
          'Không thể lấy danh sách sự kiện: ${response.statusCode} - ${response.body}');
    }
  }

  Future<EmergencyEventDetail> getEmergencyEventDetail(String eventId) async {
    final token = await _userManager.getUserToken() ?? '';

    final response = await ApiConfig.authenticatedGet(
      '${ApiConfig.emergencyDonation}/$eventId',
      token: token,
      headers: {'Accept': 'text/plain'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return EmergencyEventDetail.fromJson(data['Message']);
    } else {
      throw Exception(
        'Không thể lấy chi tiết sự kiện: ${response.statusCode} - ${response.body}',
      );
    }
  }

  MediaType _detectMimeType(String path) {
    final ext = path.toLowerCase();
    if (ext.endsWith('.jpg') || ext.endsWith('.jpeg')) return MediaType('image', 'jpeg');
    if (ext.endsWith('.png')) return MediaType('image', 'png');
    if (ext.endsWith('.webp')) return MediaType('image', 'webp');
    return MediaType('application', 'octet-stream');
  }
}
