import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/data/models/user_ranking_model.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class RankingListWidget extends StatefulWidget {
  final List<UserRanking> users;
  final String? currentUserId;
  final int startRank;
  final int maxDonation;
  final Function(String) onUserTap;

  const RankingListWidget({
    Key? key,
    required this.users,
    this.currentUserId,
    required this.startRank,
    required this.maxDonation,
    required this.onUserTap,
  }) : super(key: key);

  @override
  State<RankingListWidget> createState() => _RankingListWidgetState();
}

class _RankingListWidgetState extends State<RankingListWidget>
    with TickerProviderStateMixin {
  late AnimationController _listAnimationController;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  @override
  void dispose() {
    _listAnimationController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _listAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideAnimations = List.generate(
      widget.users.length,
          (index) => Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _listAnimationController,
          curve: Interval(
            index * 0.1,
            0.6 + (index * 0.1),
            curve: Curves.easeOutCubic,
          ),
        ),
      ),
    );

    _fadeAnimations = List.generate(
      widget.users.length,
          (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _listAnimationController,
          curve: Interval(
            index * 0.1,
            0.6 + (index * 0.1),
            curve: Curves.easeInOut,
          ),
        ),
      ),
    );

    // Start animation
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) _listAnimationController.forward();
    });
  }

  Widget _buildRankingItem(UserRanking user, int index) {
    final rank = widget.startRank + index;
    final isCurrent = user.id == widget.currentUserId;
    final progressValue = widget.maxDonation > 0
        ? (user.count / widget.maxDonation).clamp(0.0, 1.0)
        : 0.0;
    final localizations = AppLocalizations.of(context);

    return SlideTransition(
      position: _slideAnimations[index],
      child: FadeTransition(
        opacity: _fadeAnimations[index],
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          decoration: isCurrent
              ? BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryRed.withOpacity(0.12),
                      Colors.white,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryRed.withOpacity(0.18),
                      blurRadius: 18,
                      spreadRadius: 2,
                      offset: const Offset(0, 6),
                    ),
                  ],
                  border: Border.all(color: AppColors.primaryRed, width: 2),
                )
              : null,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => widget.onUserTap(user.id),
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isCurrent
                      ? Colors.transparent
                      : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: isCurrent
                      ? null
                      : Border.all(color: Colors.grey[300]!, width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: isCurrent
                          ? AppColors.primaryRed.withOpacity(0.1)
                          : Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Rank number
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isCurrent
                            ? AppColors.primaryRed
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: isCurrent
                            ? null
                            : Border.all(color: Colors.grey[300]!, width: 1),
                      ),
                      child: Center(
                        child: Text(
                          '$rank',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: isCurrent ? Colors.white : AppColors.primaryRed,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Avatar
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isCurrent
                              ? AppColors.primaryRed.withOpacity(0.5)
                              : Colors.grey[300]!,
                          width: 2,
                        ),
                        boxShadow: isCurrent
                            ? [
                                BoxShadow(
                                  color: AppColors.primaryRed.withOpacity(0.18),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ]
                            : [],
                      ),
                      child: CircleAvatar(
                        radius: 28,
                        backgroundColor: Colors.grey[100],
                        backgroundImage: user.image != null && user.image!.isNotEmpty
                            ? NetworkImage(user.image!)
                            : null,
                        child: (user.image == null || user.image!.isEmpty)
                            ? Icon(
                          Icons.person,
                          size: 28,
                          color: AppColors.primaryRed,
                        )
                            : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // User info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Name and current user indicator
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  user.name,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: isCurrent ? AppColors.primaryRed : Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isCurrent)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryRed,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    localizations.translate('you') ?? 'You',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // Progress bar and donation count
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Image.asset(
                                              'assets/icons/logo_origin.png',
                                              width: 14,
                                              height: 14,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              localizations.translate('donations') ?? 'Donations',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                          '${user.count}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.primaryRed,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: LinearProgressIndicator(
                                        value: progressValue,
                                        backgroundColor: Colors.grey[200],
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          isCurrent
                                              ? AppColors.primaryRed
                                              : AppColors.primaryRed.withOpacity(0.7),
                                        ),
                                        minHeight: 6,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Trophy icon for top performers
                    if (rank <= 10)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getTrophyColor(rank).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.emoji_events,
                          color: _getTrophyColor(rank),
                          size: 20,
                        ),
                      ),
                    // Arrow icon
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getTrophyColor(int rank) {
    if (rank <= 3) return Colors.amber;
    if (rank <= 5) return Colors.orange;
    if (rank <= 10) return Colors.grey[600]!;
    return Colors.grey[400]!;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    if (widget.users.isEmpty) {
      return Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              Icons.people_outline,
              size: 60,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              localizations.translate('no_more_donors') ?? 'No more donors in the list',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.primaryRed,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  localizations.translate('all_donors') ?? 'All Donors',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryRed.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${widget.users.length} ${localizations.translate('donors') ?? 'donors'}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryRed,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // User list
          ...widget.users.asMap().entries.map(
                (entry) => _buildRankingItem(entry.value, entry.key),
          ),
        ],
      ),
    );
  }
}