{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\FPT\\SE8\\PRM392\\FP\\FEv3\\android\\app\\.cxx\\Debug\\1i75b6c4\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\FPT\\SE8\\PRM392\\FP\\FEv3\\android\\app\\.cxx\\Debug\\1i75b6c4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}