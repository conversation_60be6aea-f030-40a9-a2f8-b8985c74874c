﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace BloodPlus.ModelViews.OrganizationModelViews
{
    public class CreateOrganizationModelView
    {
        [Required]
        public string Name { get; set; }
        [Required]
        public string Address { get; set; }
        [Required]
        public string ContactPhone { get; set; }
        [Required]
        public string ContactEmail { get; set; }
        public IFormFile? OrganizationImage { get; set; }
    }
}
