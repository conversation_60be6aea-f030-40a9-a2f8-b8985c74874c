import 'dart:async';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bloodplusmobile/data/models/verify_otp_request_model.dart';
import 'package:bloodplusmobile/data/services/auth_service.dart';
import 'package:bloodplusmobile/data/models/register_request_model.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class VerifyOtpScreen extends StatefulWidget {
  final String email;
  const VerifyOtpScreen({Key? key, required this.email}) : super(key: key);

  @override
  State<VerifyOtpScreen> createState() => _VerifyOtpScreenState();
}

class _VerifyOtpScreenState extends State<VerifyOtpScreen> with TickerProviderStateMixin {
  final List<TextEditingController> _otpControllers = List.generate(6, (_) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (_) => FocusNode());
  final AuthService _authService = AuthService();

  bool _isLoading = false;
  bool _canResend = false;
  int _secondsLeft = 180; // 3 minutes
  Timer? _timer;
  RegisterRequestModel? _registerInfo;

  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _timerAnimation;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['registerInfo'] is RegisterRequestModel) {
      _registerInfo = args['registerInfo'] as RegisterRequestModel;
    }
  }

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startTimer();
    _animationController.forward();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _timerAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );

    _pulseController.repeat(reverse: true);
  }

  void _startTimer() {
    setState(() {
      _secondsLeft = 180;
      _canResend = false;
    });
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_secondsLeft > 0) {
        setState(() {
          _secondsLeft--;
        });
      } else {
        setState(() {
          _canResend = true;
        });
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    _pulseController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  String get _otpCode {
    return _otpControllers.map((controller) => controller.text).join();
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty) {
      if (index < 5) {
        _focusNodes[index + 1].requestFocus();
      } else {
        _focusNodes[index].unfocus();
      }
    }

    if (_otpCode.length == 6) {
      Future.delayed(const Duration(milliseconds: 180), () {
        _verifyOtp();
      });
    }
  }

  void _onOtpBackspace(int index) {
    if (index > 0 && _otpControllers[index].text.isEmpty) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  Future<void> _verifyOtp() async {
    if (_otpCode.length != 6) {
      _showErrorSnackBar(AppLocalizations.of(context).translate('please_enter_otp'));
      return;
    }

    setState(() { _isLoading = true; });
    HapticFeedback.lightImpact();

    try {
      final model = VerifyOtpRequestModel(email: widget.email, otp: _otpCode);
      final success = await _authService.verifyOtp(model);

      setState(() { _isLoading = false; });

      if (success) {
        HapticFeedback.heavyImpact();
        _showSuccessSnackBar(AppLocalizations.of(context).translate('otp_verified_success'));
        await Future.delayed(const Duration(milliseconds: 500));
        Navigator.pushReplacementNamed(context, '/login');
      } else {
        HapticFeedback.heavyImpact();
        // Hiển thị thông báo lỗi OTP không chính xác
        _showErrorSnackBar(AppLocalizations.of(context).translate('otp_incorrect'));
        _clearOtpFields();
      }
    } catch (e) {
      setState(() { _isLoading = false; });
      HapticFeedback.heavyImpact();
      _showErrorSnackBar(AppLocalizations.of(context).translate('error_resend_otp').replaceFirst('{error}', e.toString()));
    }
  }

  Future<void> _resendOtp() async {
    if (_registerInfo == null) {
      _showErrorSnackBar(AppLocalizations.of(context).translate('no_registration_info'));
      return;
    }

    setState(() { _isLoading = true; });
    HapticFeedback.lightImpact();

    try {
      final success = await _authService.registerAccount(_registerInfo!);
      setState(() { _isLoading = false; });

      if (success) {
        _startTimer();
        _clearOtpFields();
        HapticFeedback.heavyImpact();
        _showSuccessSnackBar(AppLocalizations.of(context).translate('otp_resend_success'));
      } else {
        HapticFeedback.heavyImpact();
        _showErrorSnackBar(AppLocalizations.of(context).translate('otp_resend_failed'));
      }
    } catch (e) {
      setState(() { _isLoading = false; });
      HapticFeedback.heavyImpact();
      _showErrorSnackBar(AppLocalizations.of(context).translate('error_resend_otp').replaceFirst('{error}', e.toString()));
    }
  }

  void _clearOtpFields() {
    for (var controller in _otpControllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  String _formatTime(int seconds) {
    final m = (seconds ~/ 60).toString().padLeft(2, '0');
    final s = (seconds % 60).toString().padLeft(2, '0');
    return '$m:$s';
  }

  Widget _buildOtpField(int index, double fieldSize) {
    return Container(
      width: fieldSize,
      height: fieldSize + 10,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _focusNodes[index].hasFocus
              ? AppTheme.primaryRed
              : _otpControllers[index].text.isNotEmpty
              ? AppTheme.primaryRed.withAlpha(128)
              : AppTheme.borderColor,
          width: _focusNodes[index].hasFocus ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(12),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _otpControllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: AppTheme.headingLarge.copyWith(
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryRed,
          fontSize: fieldSize * 0.4,
        ),
        decoration: const InputDecoration(
          counterText: '',
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        onChanged: (value) {
          _onOtpChanged(value, index);
          if (value.isEmpty && index > 0) {
            _onOtpBackspace(index);
          }
        },
        onTap: () {
          _otpControllers[index].selection = TextSelection.fromPosition(
            TextPosition(offset: _otpControllers[index].text.length),
          );
        },
      ),
    );
  }

  Widget _buildTimerWidget(double timerSize) {
    final progress = 1.0 - (_secondsLeft / 180.0);

    return Container(
      padding: EdgeInsets.all(timerSize * 0.2),
      decoration: AppTheme.cardDecoration,
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: timerSize,
                height: timerSize,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 6,
                  backgroundColor: AppTheme.borderColor,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _secondsLeft > 60 ? AppTheme.primaryRed : Colors.orange,
                  ),
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.timer_outlined,
                    color: _secondsLeft > 60 ? AppTheme.primaryRed : Colors.orange,
                    size: timerSize * 0.3,
                  ),
                  SizedBox(height: timerSize * 0.05),
                  Text(
                    _formatTime(_secondsLeft),
                    style: AppTheme.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _secondsLeft > 60 ? AppTheme.primaryRed : Colors.orange,
                      fontSize: timerSize * 0.2,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: timerSize * 0.15),
          Text(
            _canResend ? AppLocalizations.of(context).translate('can_resend_otp') : AppLocalizations.of(context).translate('time_left'),
            style: AppTheme.bodyMedium.copyWith(fontSize: timerSize * 0.15),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double screenWidth = constraints.maxWidth;
        final double screenHeight = constraints.maxHeight;
        final double padding = screenWidth * 0.05;
        final double otpFieldSize = screenWidth > 600 ? 60.0 : screenWidth * 0.12;
        final double timerSize = screenWidth > 600 ? 100.0 : screenWidth * 0.25;
        final double buttonHeight = screenHeight * 0.08;
        final double fontScale = screenWidth > 600 ? 1.2 : 1.0;

        return Scaffold(
          backgroundColor: AppTheme.backgroundColor,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios, color: AppTheme.textPrimary, size: 24 * fontScale),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(AppLocalizations.of(context).translate('verify_otp'), style: AppTheme.headingMedium.copyWith(fontSize: 20 * fontScale)),
            centerTitle: true,
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(padding),
                child: Column(
                  children: [
                    SizedBox(height: screenHeight * 0.02),

                    // Header Section
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(padding * 1.2),
                      decoration: AppTheme.elevatedCardDecoration.copyWith(
                        gradient: AppTheme.primaryGradient,
                      ),
                      child: Column(
                        children: [
                          ScaleTransition(
                            scale: _pulseAnimation,
                            child: Container(
                              padding: EdgeInsets.all(padding * 0.8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.mail_outline,
                                size: 40 * fontScale,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(height: padding),
                          Text(
                            AppLocalizations.of(context).translate('verify_account'),
                            style: AppTheme.headingLarge.copyWith(
                              color: Colors.white,
                              fontSize: 24 * fontScale,
                            ),
                          ),
                          SizedBox(height: padding * 0.5),
                          Text(
                            AppLocalizations.of(context).translate('enter_otp_sent_email'),
                            style: AppTheme.bodyMedium.copyWith(
                              color: Colors.white70,
                              fontSize: 16 * fontScale,
                            ),
                          ),
                          SizedBox(height: padding * 0.25),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: padding * 0.6, vertical: padding * 0.3),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              widget.email,
                              style: AppTheme.bodyMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 14 * fontScale,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.04),

                    // OTP Input Section
                    Container(
                      padding: EdgeInsets.all(padding * 1.2),
                      decoration: AppTheme.cardDecoration,
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('enter_otp_verification'),
                            style: AppTheme.headingSmall.copyWith(fontSize: 18 * fontScale),
                          ),
                          SizedBox(height: screenHeight * 0.025),
                          LayoutBuilder(
                            builder: (context, constraints) {
                              final availableWidth = constraints.maxWidth;
                              final totalMargin = 8 * 5;
                              final maxFieldSize = (availableWidth - totalMargin) / 6;
                              final otpFieldSize = screenWidth > 600 ? 60.0 : (maxFieldSize.clamp(40.0, 50.0));

                              return Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  6,
                                      (index) => Flexible(
                                    child: _buildOtpField(index, otpFieldSize),
                                  ),
                                ),
                              );
                            },
                          ),
                          SizedBox(height: screenHeight * 0.025),
                          Text(
                            AppLocalizations.of(context).translate('otp_consists_6_digits'),
                            style: AppTheme.captionText.copyWith(fontSize: 12 * fontScale),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.03),

                    // Timer Section
                    _buildTimerWidget(timerSize),

                    SizedBox(height: screenHeight * 0.04),

                    // Action Buttons
                    Column(
                      children: [
                        // Verify Button
                        Container(
                          width: double.infinity,
                          height: buttonHeight,
                          decoration: BoxDecoration(
                            gradient: _otpCode.length == 6
                                ? AppTheme.primaryGradient
                                : LinearGradient(colors: [Colors.grey.shade300, Colors.grey.shade400]),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: _otpCode.length == 6
                                ? [
                              BoxShadow(
                                color: AppTheme.primaryRed.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ]
                                : null,
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: _isLoading || _otpCode.length != 6 ? null : _verifyOtp,
                              child: Center(
                                child: _isLoading
                                    ? SizedBox(
                                  width: 24 * fontScale,
                                  height: 24 * fontScale,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                                    : Text(
                                  AppLocalizations.of(context).translate('verify'),
                                  style: AppTheme.buttonText.copyWith(
                                    color: _otpCode.length == 6 ? Colors.white : Colors.grey.shade600,
                                    fontSize: 16 * fontScale,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(height: screenHeight * 0.02),

                        // Resend Button
                        Container(
                          width: double.infinity,
                          height: buttonHeight,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _canResend && !_isLoading
                                  ? AppTheme.primaryRed
                                  : AppTheme.borderColor,
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: _canResend && !_isLoading ? _resendOtp : null,
                              child: Center(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.refresh,
                                      color: _canResend && !_isLoading
                                          ? AppTheme.primaryRed
                                          : Colors.grey,
                                      size: 20 * fontScale,
                                    ),
                                    SizedBox(width: padding * 0.5),
                                    Text(
                                      AppLocalizations.of(context).translate('resend_otp'),
                                      style: AppTheme.buttonText.copyWith(
                                        color: _canResend && !_isLoading
                                            ? AppTheme.primaryRed
                                            : Colors.grey,
                                        fontSize: 16 * fontScale,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(height: screenHeight * 0.04),

                        // Help Section
                        Container(
                          padding: EdgeInsets.all(padding),
                          decoration: AppTheme.cardDecoration,
                          child: Column(
                            children: [
                              Icon(
                                Icons.help_outline,
                                color: AppTheme.textSecondary,
                                size: 32 * fontScale,
                              ),
                              SizedBox(height: padding * 0.6),
                              Text(
                                AppLocalizations.of(context).translate('didnt_receive_otp'),
                                style: AppTheme.bodyLarge.copyWith(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16 * fontScale,
                                ),
                              ),
                              SizedBox(height: padding * 0.5),
                              Text(
                                AppLocalizations.of(context).translate('check_spam_junk_wait_extra_minutes'),
                                style: AppTheme.captionText.copyWith(fontSize: 12 * fontScale),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: screenHeight * 0.05),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}