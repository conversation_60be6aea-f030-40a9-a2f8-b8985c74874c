import 'dart:math';
import 'package:flutter/material.dart';

class BloodDropAnimation extends StatefulWidget {
  const BloodDropAnimation({Key? key}) : super(key: key);

  @override
  _BloodDropAnimationState createState() => _BloodDropAnimationState();
}

class _BloodDropAnimationState extends State<BloodDropAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _fallAnimations;
  late List<Animation<double>> _scaleAnimations;
  late List<BloodDrop> _bloodDrops;

  final int _dropCount = 4;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startDropAnimations();
  }

  void _initializeAnimations() {
    _controllers = [];
    _fallAnimations = [];
    _scaleAnimations = [];
    _bloodDrops = [];

    for (int i = 0; i < _dropCount; i++) {
      // Create controller for each drop
      final controller = AnimationController(
        duration: Duration(milliseconds: 500 + _random.nextInt(1000)),
        vsync: this,
      );
      _controllers.add(controller);

      // Fall animation (from top to bottom)
      final fallAnimation = Tween<double>(begin: -0.1, end: 1.1).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeIn,
        ),
      );
      _fallAnimations.add(fallAnimation);

      // Scale animation (getting smaller as it falls)
      final scaleAnimation = Tween<double>(begin: 1.0, end: 0.3).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeOut,
        ),
      );
      _scaleAnimations.add(scaleAnimation);

      // Create blood drop data
      _bloodDrops.add(BloodDrop(
        startX: _random.nextDouble(),
        rotation: _random.nextDouble() * 2 * pi,
        delay: i * 200 + _random.nextInt(300),
      ));
    }
  }

  void _startDropAnimations() async {
    for (int i = 0; i < _dropCount; i++) {
      await Future.delayed(Duration(milliseconds: _bloodDrops[i].delay));
      if (mounted) {
        _controllers[i].forward();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return SizedBox(
      width: screenSize.width,
      height: screenSize.height,
      child: Stack(
        children: List.generate(_dropCount, (index) {
          return AnimatedBuilder(
            animation: Listenable.merge([_fallAnimations[index], _scaleAnimations[index]]),
            builder: (context, child) {
              final drop = _bloodDrops[index];
              final fallProgress = _fallAnimations[index].value;
              final scale = _scaleAnimations[index].value;

              // Calculate position
              final x = drop.startX * screenSize.width - 25; // Center the drop
              final y = fallProgress * screenSize.height;

              // Hide drops that are off screen
              if (fallProgress < 0 || fallProgress > 1) {
                return const SizedBox.shrink();
              }

              return Positioned(
                left: x,
                top: y,
                child: Transform.scale(
                  scale: scale,
                  child: Transform.rotate(
                    angle: drop.rotation,
                    child: Image.asset(
                      'assets/icons/blood.png',
                      width: 70,
                      height: 70,
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

class BloodDrop {
  final double startX;
  final double rotation;
  final int delay;

  BloodDrop({
    required this.startX,
    required this.rotation,
    required this.delay,
  });
}