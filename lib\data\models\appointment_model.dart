import 'package:bloodplusmobile/data/models/Enum/appointment_status.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';

class Appointment {
  final String id;
  final String eventName;
  final int bloodComponent;
  final String organizationName;
  final String location;
  final String appointmentDate;
  final AppointmentStatus status;
  final String? certification;
  final bool? isEmergency;

  Appointment({
    required this.id,
    required this.eventName,
    required this.bloodComponent,
    required this.organizationName,
    required this.location,
    required this.appointmentDate,
    required this.status,
    this.certification,
    this.isEmergency
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    print('Appointment.fromJson - Raw data: $json');

    return Appointment(
      id: json['Id']?.toString() ?? json['id']?.toString() ?? '',
      eventName: json['EventName']?.toString() ?? json['eventName']?.toString() ?? '',
      bloodComponent: (json['BloodComponent'] as num?)?.toInt() ??
                      (json['bloodComponent'] as num?)?.toInt() ?? 0,
      organizationName: json['OrganizationName']?.toString() ?? json['organizationName']?.toString() ?? '',
      location: json['Location']?.toString() ?? json['location']?.toString() ?? '',
      appointmentDate: json['AppointmentDate']?.toString() ?? json['appointmentDate']?.toString() ?? '',
      status: AppointmentStatus.fromValue((json['Status'] as num?)?.toInt() ??
                                         (json['status'] as num?)?.toInt() ?? 0),
      certification: json['Certification']?.toString() ?? json['certification']?.toString(),
      isEmergency: json['IsEmergency'] ?? json['isEmergency']?.toString()
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'eventName': eventName,
      'bloodComponent': bloodComponent,
      'organizationName': organizationName,
      'location': location,
      'appointmentDate': appointmentDate,
      'status': status.value,
      'certification': certification,
    };
  }

  String getFormattedDate() {
    try {
      final date = DateTime.parse(appointmentDate);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return appointmentDate.split('T')[0]; // Fallback
    }
  }

  String getFormattedTime() {
    try {
      final date = DateTime.parse(appointmentDate);
      return DateFormat('HH:mm').format(date);
    } catch (e) {
      return appointmentDate.split('T')[1].split('.')[0]; // Fallback
    }
  }

  String getStatusText(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (status) {
      case AppointmentStatus.pending:
        return localizations.translate('Pending');
      case AppointmentStatus.completed:
        return localizations.translate('Completed');
      case AppointmentStatus.cancelled:
        return localizations.translate('Canceled');
      case AppointmentStatus.needConfirm:
        return localizations.translate('need_confirm');
      default:
        return localizations.translate('status_unknown');
    }
  }
}