import 'dart:math';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:flutter/material.dart';

class SuccessDialog extends StatefulWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String message;
  final String buttonText;
  final VoidCallback onButtonPressed;
  final String? secondaryButtonText;
  final VoidCallback? onSecondaryButtonPressed;
  final bool showSecondaryButton;

  const SuccessDialog({
    super.key,
    this.icon = Icons.check_circle_outline_rounded,
    this.iconColor = AppColors.primaryRed,
    required this.title,
    required this.message,
    this.buttonText = 'OK',
    required this.onButtonPressed,
    this.secondaryButtonText,
    this.onSecondaryButtonPressed,
    this.showSecondaryButton = false,
  });

  const SuccessDialog.dualButton({
    super.key,
    this.icon = Icons.check_circle_outline_rounded,
    this.iconColor = AppColors.primaryRed,
    required this.title,
    required this.message,
    required this.buttonText,
    required this.onButtonPressed,
    required this.secondaryButtonText,
    required this.onSecondaryButtonPressed,
  }) : showSecondaryButton = true;

  @override
  State<SuccessDialog> createState() => _SuccessDialogState();
}

class _SuccessDialogState extends State<SuccessDialog>
    with TickerProviderStateMixin {
  late AnimationController _iconController;
  late AnimationController _particleController;
  late Animation<double> _iconScale;
  late Animation<double> _iconRotation;
  late List<Animation<Offset>> _particleAnimations;

  @override
  void initState() {
    super.initState();

    _iconController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _iconScale = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _iconController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _iconRotation = Tween<double>(begin: -0.5, end: 0.0).animate(
      CurvedAnimation(
        parent: _iconController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _particleAnimations = List.generate(6, (index) {
      final angle = (index * 60) * (3.14159 / 180);
      return Tween<Offset>(
        begin: Offset.zero,
        end: Offset(cos(angle) * 80, sin(angle) * 80),
      ).animate(
        CurvedAnimation(
          parent: _particleController,
          curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
        ),
      );
    });

    _iconController.forward();
    _particleController.forward();
  }

  @override
  void dispose() {
    _iconController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // Dynamic sizing based on screen width
    final dialogWidth = min(screenSize.width * 0.85, 400.0);
    final padding = isSmallScreen ? 16.0 : (isLargeScreen ? 40.0 : 32.0);
    final iconSize = isSmallScreen ? 48.0 : (isLargeScreen ? 60.0 : 52.0);
    final titleFontSize = isSmallScreen ? 18.0 : (isLargeScreen ? 26.0 : 22.0);
    final messageFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final buttonHeight = isSmallScreen ? 48.0 : (isLargeScreen ? 56.0 : 52.0);
    final buttonFontSize = isSmallScreen ? 15.0 : (isLargeScreen ? 18.0 : 17.0);
    final spacing = isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 16.0 : 24.0,
        vertical: isSmallScreen ? 16.0 : 24.0,
      ),
      child: Container(
        width: dialogWidth,
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: isSmallScreen ? 20 : 30,
              offset: const Offset(0, 15),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated icon with particles
            SizedBox(
              height: isSmallScreen ? 100 : (isLargeScreen ? 140 : 120),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Particles
                  ...List.generate(6, (index) {
                    return AnimatedBuilder(
                      animation: _particleController,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: _particleAnimations[index].value,
                          child: Opacity(
                            opacity: 1 - _particleController.value,
                            child: Container(
                              width: isSmallScreen ? 4 : 6,
                              height: isSmallScreen ? 4 : 6,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: widget.iconColor.withOpacity(0.6),
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  }),
                  // Main icon
                  AnimatedBuilder(
                    animation: _iconController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _iconScale.value,
                        child: Transform.rotate(
                          angle: _iconRotation.value,
                          child: Container(
                            padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  widget.iconColor.withOpacity(0.15),
                                  widget.iconColor.withOpacity(0.05),
                                  Colors.transparent,
                                ],
                                stops: const [0.3, 0.7, 1.0],
                              ),
                              border: Border.all(
                                color: widget.iconColor.withOpacity(0.3),
                                width: isSmallScreen ? 1.5 : 2,
                              ),
                            ),
                            child: Icon(
                              widget.icon,
                              size: iconSize,
                              color: widget.iconColor,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            SizedBox(height: spacing),
            Text(
              widget.title,
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: spacing),
            Text.rich(
              TextSpan(
                text: widget.message,
              ),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: messageFontSize,
                color: Colors.grey.shade600,
                height: 1.5,
                letterSpacing: 0.2,
              ),
              maxLines: 8,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: isSmallScreen ? 20 : (isLargeScreen ? 32 : 28)),
            if (widget.showSecondaryButton) ...[
              // Dual button layout
              LayoutBuilder(
                builder: (context, constraints) {
                  final buttonWidth = constraints.maxWidth / 2 - (spacing / 2);
                  return Row(
                    children: [
                      Expanded(
                        child: CustomButton(
                          text: widget.secondaryButtonText ?? 'Cancel',
                          color: Colors.grey[400]!,
                          textColor: Colors.white,
                          onPressed: widget.onSecondaryButtonPressed ?? () {},
                          borderRadius: isSmallScreen ? 12 : 14,
                          height: buttonHeight,
                          textStyle: TextStyle(
                            fontSize: buttonFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                            shadows: const [
                              Shadow(
                                color: Colors.black26,
                                offset: Offset(0, 2),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          maxLines: 2,
                          minWidth: buttonWidth,
                        ),
                      ),
                      SizedBox(width: spacing),
                      Expanded(
                        child: CustomButton(
                          text: widget.buttonText,
                          color: widget.iconColor,
                          textColor: Colors.white,
                          onPressed: widget.onButtonPressed,
                          borderRadius: isSmallScreen ? 12 : 14,
                          height: buttonHeight,
                          textStyle: TextStyle(
                            fontSize: buttonFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                            shadows: const [
                              Shadow(
                                color: Colors.black26,
                                offset: Offset(0, 2),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          maxLines: 2,
                          minWidth: buttonWidth,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ] else ...[
              // Single button layout
              SizedBox(
                width: double.infinity,
                child: CustomButton(
                  text: widget.buttonText,
                  color: widget.iconColor,
                  onPressed: widget.onButtonPressed,
                  borderRadius: isSmallScreen ? 12 : 14,
                  height: buttonHeight,
                  textStyle: TextStyle(
                    fontSize: buttonFontSize,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}