﻿using BloodPlus.Core.Enum;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BloodPlus.Contract.Repositories.Entity
{
    public class ChatMessage
    {
        [Key]
        public long Id { get; set; } // Dùng kiểu long cho hiệu suất cao

        [Required]
        public Guid UserId { get; set; } // FK đến User (IdentityUser<Guid>)

        [ForeignKey(nameof(UserId))]
        public virtual User User { get; set; } = null!;

        [Required, MaxLength(16)]
        public RoleChat Role { get; set; } = RoleChat.user;

        [Required]
        public string Message { get; set; } = null!;

        [Required]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [MaxLength(64)]
        public string? ConversationId { get; set; }
        [MaxLength(4000)]
        public string? MetadataJson { get; set; }
    }
}
