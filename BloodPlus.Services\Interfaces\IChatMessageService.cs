﻿using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.ModelViews.ChatGPT;
using BloodPlus.ModelViews.ChatMessageModelView;

namespace BloodPlus.Services.Interfaces
{
    public interface IChatMessageService
    {
        Task<List<ChatMessageModelView>> GetMessagesByConversationAsync(string conversationId, Guid userId, int limit = 50);
        Task<List<ChatMessageModelView>> AddMessageAsync(CreateChatMessageModelView input);
        Task<Dictionary<string, string>> GetUserConversationFirstMessagesAsync(Guid userId);
        Task<bool> DeleteConversationAsync(string conversationId, Guid userId);
    }
}
