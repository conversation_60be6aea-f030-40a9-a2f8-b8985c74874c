import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/system/custom_button.dart';
import 'package:bloodplusmobile/data/models/blog_model.dart';
import 'package:bloodplusmobile/data/services/blog_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';

class BlogScreen extends StatefulWidget {
  const BlogScreen({super.key});

  @override
  _BlogScreenState createState() => _BlogScreenState();
}

class _BlogScreenState extends State<BlogScreen> with TickerProviderStateMixin {
  final BlogService _blogService = BlogService();
  List<BlogModel> blogs = [];
  List<BlogModel> filteredBlogs = [];
  bool isLoading = false;
  String? errorMessage;
  int currentPage = 1;
  int pageSize = 5;
  bool hasNextPage = false;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));
    fetchBlogs();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  Future<void> fetchBlogs({bool loadMore = false}) async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final response = await _blogService.getBlogs(
        pageNumber: loadMore ? currentPage + 1 : 1,
        pageSize: pageSize,
      );

      setState(() {
        if (loadMore) {
          blogs.addAll(response.items);
          currentPage++;
        } else {
          blogs = response.items;
          filteredBlogs = blogs;
          currentPage = 1;
        }
        hasNextPage = response.hasNextPage;
        isLoading = false;
      });

      _fadeController.forward();
      _slideController.forward();
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  void _filterBlogs(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredBlogs = blogs;
      } else {
        filteredBlogs = blogs
            .where((blog) =>
        blog.title.toLowerCase().contains(query.toLowerCase()) ||
            blog.description.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  Widget _buildSearchBar(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.05,
        vertical: screenHeight * 0.015,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.06),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: screenWidth * 0.05,
            offset: Offset(0, screenWidth * 0.01),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _filterBlogs,
        onTap: () => setState(() => _isSearching = true),
        style: AppTheme.bodyMedium.copyWith(fontSize: screenWidth * 0.04),
        decoration: InputDecoration(
          hintText: localizations.translate('search_blogs'),
          hintStyle: AppTheme.bodyMedium.copyWith(
            color: Colors.grey[500],
            fontSize: screenWidth * 0.04,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(screenWidth * 0.06),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          prefixIcon: Container(
            padding: EdgeInsets.all(screenWidth * 0.03),
            child: Icon(
              Icons.search_rounded,
              color: AppColors.primaryRed,
              size: screenWidth * 0.06,
            ),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
            icon: Icon(Icons.clear_rounded, color: Colors.grey[400], size: screenWidth * 0.06),
            onPressed: () {
              _searchController.clear();
              _filterBlogs('');
              setState(() => _isSearching = false);
            },
          )
              : null,
          contentPadding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.05,
            vertical: screenHeight * 0.02,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      padding: EdgeInsets.fromLTRB(
        screenWidth * 0.05,
        screenHeight * 0.08,
        screenWidth * 0.05,
        screenHeight * 0.03,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryRed,
            AppColors.primaryRed.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: Colors.white,
                      size: screenWidth * 0.06,
                    ),
                    onPressed: () => NavigationService.goBack(),
                  ),
                ),
                const Spacer(),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.tune_rounded,
                      color: Colors.white,
                      size: screenWidth * 0.06,
                    ),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(localizations.translate('filter_coming_soon')),
                          backgroundColor: AppColors.primaryRed,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(screenWidth * 0.025),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.04),
            Text(
              localizations.translate('explore'),
              style: AppTheme.headingLarge.copyWith(
                color: Colors.white,
                fontSize: screenWidth * 0.08,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: screenHeight * 0.01),
            Text(
              localizations.translate('blog_title_detail'),
              style: AppTheme.bodyLarge.copyWith(
                color: Colors.white.withOpacity(0.9),
                fontSize: screenWidth * 0.04,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              _buildHeader(context),
              _buildSearchBar(context),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () => fetchBlogs(),
                  color: AppColors.primaryRed,
                  child: isLoading && blogs.isEmpty
                      ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: EdgeInsets.all(screenWidth * 0.05),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.05),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: screenWidth * 0.05,
                                offset: Offset(0, screenWidth * 0.025),
                              ),
                            ],
                          ),
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryRed),
                            strokeWidth: 3,
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.03),
                        Text(
                          'Đang tải bài viết...',
                          style: AppTheme.bodyMedium.copyWith(
                            color: Colors.grey[600],
                            fontSize: screenWidth * 0.04,
                          ),
                        ),
                      ],
                    ),
                  )
                      : errorMessage != null
                      ? Center(
                    child: Container(
                      margin: EdgeInsets.all(screenWidth * 0.05),
                      padding: EdgeInsets.all(screenWidth * 0.075),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(screenWidth * 0.05),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: screenWidth * 0.05,
                            offset: Offset(0, screenWidth * 0.025),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(screenWidth * 0.125),
                            ),
                            child: Icon(
                              Icons.error_outline_rounded,
                              color: Colors.red,
                              size: screenWidth * 0.1,
                            ),
                          ),
                          SizedBox(height: screenHeight * 0.03),
                          Text(
                            localizations.translate('error_loading_blogs'),
                            style: AppTheme.headingSmall.copyWith(
                              color: Colors.red,
                              fontSize: screenWidth * 0.045,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: screenHeight * 0.015),
                          Text(
                            'Vui lòng thử lại sau',
                            style: AppTheme.bodyMedium.copyWith(
                              color: Colors.grey[600],
                              fontSize: screenWidth * 0.035,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: screenHeight * 0.03),
                          CustomButton(
                            text: localizations.translate('retry'),
                            color: AppColors.primaryRed,
                            textColor: Colors.white,
                            onPressed: () => fetchBlogs(),
                            padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.075,
                              vertical: screenHeight * 0.02,
                            ),
                            borderRadius: screenWidth * 0.06,
                          ),
                        ],
                      ),
                    ),
                  )
                      : filteredBlogs.isEmpty
                      ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: EdgeInsets.all(screenWidth * 0.05),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(screenWidth * 0.05),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: screenWidth * 0.05,
                                offset: Offset(0, screenWidth * 0.025),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.article_outlined,
                            size: screenWidth * 0.15,
                            color: Colors.grey[400],
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.03),
                        Text(
                          localizations.translate('no_blogs'),
                          style: AppTheme.headingSmall.copyWith(
                            color: Colors.grey[600],
                            fontSize: screenWidth * 0.045,
                          ),
                        ),
                      ],
                    ),
                  )
                      : FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: ListView.builder(
                        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.05),
                        itemCount: filteredBlogs.length + (hasNextPage ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == filteredBlogs.length && hasNextPage) {
                            return Container(
                              margin: EdgeInsets.symmetric(vertical: screenHeight * 0.03),
                              child: CustomButton(
                                text: localizations.translate('load_more'),
                                color: AppColors.primaryRed,
                                textColor: Colors.white,
                                onPressed: () => fetchBlogs(loadMore: true),
                                padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.075,
                                  vertical: screenHeight * 0.02,
                                ),
                                borderRadius: screenWidth * 0.06,
                              ),
                            );
                          }

                          final blog = filteredBlogs[index];
                          return _buildBlogCard(blog, index, screenWidth, screenHeight);
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBlogCard(BlogModel blog, int index, double screenWidth, double screenHeight) {
    final localizations = AppLocalizations.of(context);

    return TweenAnimationBuilder(
      tween: Tween<double>(begin: 0, end: 1),
      duration: Duration(milliseconds: 400 + (index * 100)),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, screenHeight * 0.04 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          NavigationService.navigateToBlogDetail(
            blogId: blog.id,
            blogTitle: blog.title,
          );
        },
        child: Container(
          margin: EdgeInsets.only(bottom: screenHeight * 0.03),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.05),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: screenWidth * 0.05,
                offset: Offset(0, screenWidth * 0.02),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (blog.image1 != null)
                Hero(
                  tag: 'blog_image_${blog.id}',
                  child: ClipRRect(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(screenWidth * 0.05)),
                    child: Stack(
                      children: [
                        Image.network(
                          blog.image1!,
                          height: screenHeight * 0.25,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              height: screenHeight * 0.25,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [Colors.grey[200]!, Colors.grey[100]!],
                                ),
                              ),
                              child: Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                      (loadingProgress.expectedTotalBytes ?? 1)
                                      : null,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryRed),
                                  strokeWidth: 2,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (_, __, ___) => Container(
                            height: screenHeight * 0.25,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.grey[200]!, Colors.grey[100]!],
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported_rounded,
                                  size: screenWidth * 0.1,
                                  color: Colors.grey[400],
                                ),
                                SizedBox(height: screenHeight * 0.01),
                                Text(
                                  AppLocalizations.of(context).translate('cannot_load_image') ?? 'Cannot load image',
                                  style: TextStyle(
                                    color: Colors.grey[500],
                                    fontSize: screenWidth * 0.035,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: screenHeight * 0.02,
                          right: screenWidth * 0.04,
                          child: Container(
                            padding: EdgeInsets.all(screenWidth * 0.02),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(screenWidth * 0.05),
                            ),
                            child: Icon(
                              Icons.bookmark_border_rounded,
                              color: Colors.white,
                              size: screenWidth * 0.05,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              Padding(
                padding: EdgeInsets.all(screenWidth * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AutoSizeText(
                      blog.title,
                      style: AppTheme.headingMedium.copyWith(
                        fontSize: screenWidth * 0.045,
                        fontWeight: FontWeight.bold,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.015),
                    AutoSizeText(
                      blog.description,
                      style: AppTheme.bodyMedium.copyWith(
                        color: Colors.grey[600],
                        height: 1.5,
                        fontSize: screenWidth * 0.035,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.03,
                            vertical: screenHeight * 0.01,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primaryRed.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(screenWidth * 0.05),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.calendar_today_rounded,
                                size: screenWidth * 0.035,
                                color: AppColors.primaryRed,
                              ),
                              SizedBox(width: screenWidth * 0.015),
                              Text(
                                blog.createdTime != null
                                    ? _formatDate(blog.createdTime!)
                                    : '',
                                style: TextStyle(
                                  color: AppColors.primaryRed,
                                  fontSize: screenWidth * 0.03,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Spacer(),
                        if (blog.author != null)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.03,
                              vertical: screenHeight * 0.01,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(screenWidth * 0.05),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.person_rounded,
                                  size: screenWidth * 0.035,
                                  color: Colors.grey[600],
                                ),
                                SizedBox(width: screenWidth * 0.015),
                                Text(
                                  blog.author!,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: screenWidth * 0.03,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: screenHeight * 0.012),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [AppColors.primaryRed, AppColors.primaryRed.withOpacity(0.8)],
                              ),
                              borderRadius: BorderRadius.circular(screenWidth * 0.03),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primaryRed.withOpacity(0.3),
                                  blurRadius: screenWidth * 0.02,
                                  offset: Offset(0, screenWidth * 0.01),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                localizations.translate('read_more'),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: screenWidth * 0.035,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.03),
                        Container(
                          padding: EdgeInsets.all(screenWidth * 0.03),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Icon(
                            Icons.share_rounded,
                            color: Colors.grey[600],
                            size: screenWidth * 0.045,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}