﻿
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Core.Base;
using BloodPlus.ModelViews.DonationEventModelView;
using BloodPlus.ModelViews.EmergencyEventModelView;

namespace BloodPlus.Services.Interfaces
{
    public interface IEmergencyDonationService
    {
        Task<DonationEvent> CreateEmergencyEventAsync(DonationEmergencyEventCreateModelView model, string userId);
        Task<BasePaginatedList<EDModelView>> GetAllEmergencyEventAsync(int pageNumber, int pageSize, string? userId, bool isUser);
        Task<EmergencyEventDetailModelView> GetEmergencyEventDetailAsync(string eventId);

    }
}
