import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/dialog/dialog_helper.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/services/user_service.dart';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';

class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static bool _isOtpSent = false;
  static NavigatorState? get navigator => navigatorKey.currentState;

  // Navigate to a named route
  static Future<dynamic> navigateTo(String routeName, {Object? arguments}) {
    return navigator!.pushNamed(routeName, arguments: arguments);
  }

  // Navigate to a named route and remove all previous routes
  static Future<dynamic> navigateToAndRemoveUntil(String routeName, {Object? arguments}) {
    return navigator!.pushNamedAndRemoveUntil(
      routeName,
      (Route<dynamic> route) => false,
      arguments: arguments,
    );
  }

  // Navigate to a named route and remove the current route
  static Future<dynamic> navigateToAndReplace(String routeName, {Object? arguments}) {
    return navigator!.pushReplacementNamed(routeName, arguments: arguments);
  }

  // Go back
  static void goBack() {
    if (navigator != null && navigator!.canPop()) {
      navigator!.pop();
    } else {
      navigateToHome(); // fallback nếu không pop được
    }
  }


  // Go back with result
  static void goBackWithResult(dynamic result) {
    return navigator!.pop(result);
  }

  // Check if can go back
  static bool canGoBack() {
    return navigator!.canPop();
  }

  // Convenience methods for specific routes
  static Future<dynamic> navigateToSplash() {
    return navigateToAndRemoveUntil(AppRoutes.splash);
  }

  static Future<dynamic> navigateToChangeEmail() async {
    final UserManager userManager = UserManager();
    final UserService userService = UserService();
    final String? token = await userManager.getUserToken();

    if (token == null) {
      DialogHelper.showAnimatedErrorDialog(
        context: navigator!.context,
        title: AppLocalizations.of(navigator!.context).translate('error'),
        message: AppLocalizations.of(navigator!.context).translate('not_logged_in'),
        buttonText: AppLocalizations.of(navigator!.context).translate('ok'),
        icon: Icons.error_outline_rounded,
        iconColor: AppColors.primaryRed,
      );
      return null;
    }

    // Call changeEmail to trigger OTP sending
    final result = await userService.changeEmail(token: token);

    if (result == true) {
      _isOtpSent = true; // Set flag to prevent further OTP requests
      // Navigate to ChangeEmailScreen
      return navigateTo(AppRoutes.changeEmail);
    } else {
      DialogHelper.showAnimatedErrorDialog(
        context: navigator!.context,
        title: AppLocalizations.of(navigator!.context).translate('error'),
        message:
        '${AppLocalizations.of(navigator!.context).translate('network_connection_error')}: $result',
        buttonText: AppLocalizations.of(navigator!.context).translate('ok'),
        icon: Icons.error_outline_rounded,
        iconColor: AppColors.primaryRed,
      );
      return null;
    }
  }

  static Future<dynamic> navigateToHome() {
    _isOtpSent = false;
    return navigateToAndRemoveUntil(AppRoutes.home);
  }

  static Future<dynamic> navigateToLogin() {
    _isOtpSent = false;
    return navigateToAndRemoveUntil(AppRoutes.login);
  }

  static Future<dynamic> navigateToOtpRegister() {
    return navigateTo(AppRoutes.otpRegister);
  }

  static Future<dynamic> navigateToChatAI() {
    return navigateToAndRemoveUntil(AppRoutes.chatAI);
  }

  static Future<dynamic> navigateToDonationEventDetail({
    required String eventId,
    required String eventName,
  }) {
    return navigateTo(
      AppRoutes.donationEventDetail,
      arguments: {
        'eventId': eventId,
        'eventName': eventName,
      },
    );
  }

  static Future<dynamic> navigateToBlogDetail({
    required String blogId,
    required String blogTitle,
  }) {
    return navigateTo(
      AppRoutes.blogDetail,
      arguments: {
        'blogId': blogId,
        'blogTitle': blogTitle,
      },
    );
  }

  static Future<dynamic> navigateToHistoryDetail({
    required String appointmentId,
  }) {
    return navigateTo(
      AppRoutes.historyDetail,
      arguments: {
        'appointmentId': appointmentId,
      },
    );
  }
} 