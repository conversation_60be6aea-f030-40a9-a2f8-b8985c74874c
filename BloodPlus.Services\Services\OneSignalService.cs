﻿using Newtonsoft.Json;
using System.Text;

namespace BloodPlus.Services.Services
{
    public class OneSignalService
    {
        private readonly HttpClient _httpClient;
        private readonly string _appId = "526276b5-e237-48db-97b1-44d3bfccaa04";
        private readonly string _apiKey = "os_v2_app_kjrhnnpcg5enxf5ritj37tfkar5f2ztgne6ukefxjkc7ltqgymd62kuuqw3uhmpqj5okprg4jkeqw6e4tkvtnkhegac3lskpih2za4q";

        public OneSignalService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<bool> SendNotificationAsync(string title, string message, string? userId = null, string? deviceId = null)
        {
            var requestUrl = "https://onesignal.com/api/v1/notifications";

            var payload = new
            {
                app_id = _appId,
                headings = new { en = title },
                contents = new { en = message },
                included_segments = string.IsNullOrEmpty(userId) && string.IsNullOrEmpty(deviceId)
                    ? new[] { "Subscribed Users" } // <-- sửa tại đây
                    : null,
                include_player_ids = !string.IsNullOrEmpty(deviceId) ? new[] { deviceId } : null,
                include_external_user_ids = !string.IsNullOrEmpty(userId) ? new[] { userId } : null
            };


            var json = JsonConvert.SerializeObject(payload);
            var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
            request.Headers.Add("Authorization", $"Basic {_apiKey}");
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var response = await _httpClient.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"OneSignal gửi thông báo thất bại: {(int)response.StatusCode} - {errorContent}");
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi gửi thông báo đến OneSignal: {ex.Message}", ex);
            }
        }

    }
}
