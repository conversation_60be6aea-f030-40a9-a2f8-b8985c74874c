import 'package:flutter/material.dart';
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/data/models/user_ranking_model.dart';

class TopThreeWidget extends StatelessWidget {
  final List<UserRanking> users;
  final Function(String) onUserTap;

  const TopThreeWidget({
    Key? key,
    required this.users,
    required this.onUserTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    if (users.isEmpty) return const SizedBox.shrink();

    final List<UserRanking> podiumUsers = List.generate(
      3,
      (i) => i < users.length ? users[i] : UserRanking(id: '', name: '', count: 0),
    );

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildHeader(localizations),
          const SizedBox(height: 32),
          _buildPodium(podiumUsers),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildHeader(AppLocalizations? localizations) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryRed.withOpacity(0.1),
            Colors.amber.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryRed.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.amber,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.amber.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: const Icon(
              Icons.emoji_events,
              color: Colors.white,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            localizations?.translate('top_donors') ?? 'Top Donors',
            style: TextStyle(
              fontSize: 26,
              fontWeight: FontWeight.w800,
              color: Colors.grey[800],
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPodium(List<UserRanking> podiumUsers) {
    final List<int> podiumOrder = [1, 0, 2]; // Second, First, Third
    final List<double> podiumHeights = [120, 160, 90];
    final List<double> avatarSizes = [60, 80, 60];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: List.generate(3, (i) {
        final userIndex = podiumOrder[i];
        final user = podiumUsers[userIndex];
        final rank = userIndex + 1;
        final podiumHeight = podiumHeights[i];
        final avatarSize = avatarSizes[i];

        return Expanded(
          child: _buildPodiumItem(
            user,
            rank,
            podiumHeight,
            avatarSize,
            i,
          ),
        );
      }),
    );
  }

  Widget _buildPodiumItem(
    UserRanking user,
    int rank,
    double podiumHeight,
    double avatarSize,
    int displayIndex,
  ) {
    final isWinner = rank == 1;
    final colors = _getRankColors(rank);

    return GestureDetector(
      onTap: () => user.id.isNotEmpty ? onUserTap(user.id) : null,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Crown or medal
            _buildRankIcon(rank, colors),
            const SizedBox(height: 12),

            // Avatar with enhanced styling
            _buildAvatar(user, avatarSize, colors, isWinner),
            const SizedBox(height: 12),

            // User name
            _buildUserName(user, isWinner),
            const SizedBox(height: 8),

            // Donation count
            _buildDonationCount(user, colors),
            const SizedBox(height: 16),

            // Podium base
            _buildPodiumBase(rank, podiumHeight, colors),
          ],
        ),
      ),
    );
  }

  Widget _buildRankIcon(int rank, Map<String, Color> colors) {
    IconData icon;
    switch (rank) {
      case 1:
        icon = Icons.emoji_events;
        break;
      case 2:
        icon = Icons.military_tech;
        break;
      case 3:
        icon = Icons.star;
        break;
      default:
        icon = Icons.account_circle_outlined;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: colors['primary'],
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: colors['primary']!.withOpacity(0.4),
            blurRadius: 12,
            spreadRadius: 3,
          ),
        ],
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: rank == 1 ? 28 : 24,
      ),
    );
  }

  Widget _buildAvatar(UserRanking user, double size, Map<String, Color> colors, bool isWinner) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            colors['primary']!,
            colors['secondary']!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: colors['primary']!.withOpacity(0.3),
            blurRadius: isWinner ? 20 : 15,
            spreadRadius: isWinner ? 4 : 2,
          ),
        ],
      ),
      padding: const EdgeInsets.all(4),
      child: Container(
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
        ),
        padding: const EdgeInsets.all(2),
        child: CircleAvatar(
          radius: size / 2,
          backgroundColor: Colors.grey[100],
          backgroundImage: user.image != null && user.image!.isNotEmpty
              ? NetworkImage(user.image!)
              : null,
          child: (user.image == null || user.image!.isEmpty)
              ? Icon(
                  Icons.person,
                  size: size * 0.6,
                  color: colors['primary'],
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildUserName(UserRanking user, bool isWinner) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 44),
      child: Text(
        user.name,
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontWeight: FontWeight.w800,
          fontSize: isWinner ? 20 : 18,
          color: isWinner ? AppColors.primaryRed : Colors.grey[800],
          letterSpacing: 0.3,
          height: 1.2,
        ),
      ),
    );
  }

  Widget _buildDonationCount(UserRanking user, Map<String, Color> colors) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colors['primary']!.withOpacity(0.1),
            colors['secondary']!.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: colors['primary']!.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.bloodtype_rounded,
            color: colors['primary'],
            size: 18,
          ),
          const SizedBox(width: 6),
          Text(
            '${user.count}',
            style: TextStyle(
              color: colors['primary'],
              fontWeight: FontWeight.w800,
              fontSize: 16,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPodiumBase(int rank, double height, Map<String, Color> colors) {
    return Container(
      width: double.infinity,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colors['primary']!,
            colors['secondary']!,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        boxShadow: [
          BoxShadow(
            color: colors['primary']!.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '#$rank',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.w900,
              letterSpacing: 1,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 30,
            height: 3,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, Color> _getRankColors(int rank) {
    switch (rank) {
      case 1:
        return {
          'primary': const Color(0xFFFFD700), // Gold
          'secondary': const Color(0xFFFFA500), // Orange
        };
      case 2:
        return {
          'primary': const Color(0xFFC0C0C0), // Silver
          'secondary': const Color(0xFF808080), // Gray
        };
      case 3:
        return {
          'primary': const Color(0xFFCD7F32), // Bronze
          'secondary': const Color(0xFF8B4513), // Saddle Brown
        };
      default:
        return {
          'primary': AppColors.primaryRed,
          'secondary': AppColors.primaryRed.withOpacity(0.7),
        };
    }
  }
}

// Custom painter for podium shape (optional enhancement)
class PodiumPainter extends CustomPainter {
  final Color color;
  final double height;

  PodiumPainter({required this.color, required this.height});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height);
    path.lineTo(0, 20);
    path.quadraticBezierTo(0, 0, 20, 0);
    path.lineTo(size.width - 20, 0);
    path.quadraticBezierTo(size.width, 0, size.width, 20);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}