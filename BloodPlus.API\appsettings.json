{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Jwt": {"Key": "HT4bb6d1dfbafb64a681139d1586b6f1160d18159afd57c8c79136d7490630407c", "Issuer": "https://bloodplus.duckdns.org", "Audience": "https://bloodplus.duckdns.org", "RefreshTokenValidityInDays": 7, "TokenValidityInMinutes": 420}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************", "Model": "gpt-4o"}, "Gemini": {"ApiKey": "AIzaSyDoK9UmKmYREJmgLvukWG0D2U7IwBN535E"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": 587, "SenderName": "BloodPlus", "SenderEmail": "<EMAIL>", "AppPassword": "bdmgdcfgqgblgiew"}, "ConnectionStrings": {"database": "Server=**************;TrustServerCertificate=True;User ID=sa;Password=********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Initial Catalog=BloodPlusDB;Persist Security Info=False;Connection Timeout=30;"}, "LocationIQ": {"ApiKey": "***********************************"}}