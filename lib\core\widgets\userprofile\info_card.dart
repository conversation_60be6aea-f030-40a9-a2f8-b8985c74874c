import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:flutter/material.dart';

class InfoItem {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;

  InfoItem({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
  });
}

class InfoCard extends StatelessWidget {
  final String title;
  final List<InfoItem> items;

  const InfoCard({
    Key? key,
    required this.title,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: AppTheme.cardDecoration,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTheme.headingSmall.copyWith(fontSize: 16),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Column(
              children: [
                _InfoRow(item: item),
                if (index < items.length - 1) ...[
                  const SizedBox(height: 12),
                  Divider(
                    color: AppTheme.borderColor.withOpacity(0.5),
                    height: 1,
                  ),
                  const SizedBox(height: 12),
                ],
              ],
            );
          }).toList(),
        ],
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final InfoItem item;

  const _InfoRow({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.primaryRed.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            item.icon,
            color: AppTheme.primaryRed.withOpacity(0.8),
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.label,
                style: AppTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                item.value,
                style: AppTheme.bodyLarge.copyWith(
                  color: item.valueColor ?? AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}