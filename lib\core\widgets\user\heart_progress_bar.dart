import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';

class HeartProgressBar extends StatefulWidget {
  const HeartProgressBar({Key? key}) : super(key: key);

  @override
  _HeartProgressBarState createState() => _HeartProgressBarState();
}

class _HeartProgressBarState extends State<HeartProgressBar>
    with TickerProviderStateMixin {
  late AnimationController _fillController;
  late AnimationController _heartBeatController;
  late Animation<double> _fillAnimation;
  late Animation<double> _heartBeatAnimation;

  @override
  void initState() {
    super.initState();
    _fillController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _heartBeatController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    )..repeat(reverse: true);

    _fillAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fillController, curve: Curves.easeInOutQuart),
    );

    _heartBeatAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _heartBeatController, curve: Curves.elasticOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fillController.forward();
    });
  }

  @override
  void dispose() {
    _fillController.dispose();
    _heartBeatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final size = screenWidth * 0.35; // Heart size is 35% of screen width

    return Consumer<AppStateNotifier>(
      builder: (context, appState, child) {
        if (appState.isLoading && appState.daysWaiting == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (appState.daysWaiting == null) {
          return const SizedBox.shrink();
        }

        final daysLeft = appState.daysWaiting!['wholeBloodDaysLeft'] ?? 0;
        const totalDays = 84;
        final canDonate = daysLeft <= 0;
        final progress = canDonate ? 1.0 : (totalDays - daysLeft) / totalDays;

        return GestureDetector(
          onTap: canDonate
              ? () => NavigationService.navigateTo(AppRoutes.donationEvent)
              : null,
          child: AnimatedBuilder(
            animation: Listenable.merge([_fillController, _heartBeatController]),
            builder: (context, child) {
              return Container(
                width: size,
                height: size,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    if (canDonate)
                      Transform.scale(
                        scale: _heartBeatAnimation.value,
                        child: Container(
                          width: size * 0.95,
                          height: size * 0.95,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withOpacity(0.5),
                                blurRadius: 25,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                        ),
                      ),
                    Container(
                      width: size * 0.95,
                      height: size * 0.95,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withOpacity(0.3),
                            blurRadius: 12,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: size * 0.95,
                      height: size * 0.95,
                      child: CustomPaint(
                        painter: HeartPainter(
                          fillProgress: progress * _fillAnimation.value,
                          isReady: canDonate,
                          colors: [Colors.red, Colors.redAccent],
                        ),
                      ),
                    ),
                    AnimatedOpacity(
                      opacity: _fillAnimation.value,
                      duration: const Duration(milliseconds: 500),
                      child: Text(
                        canDonate
                            ? '💖'
                            : '${(progress * 100).toStringAsFixed(0)}%',
                        style: GoogleFonts.poppins(
                          fontSize: canDonate ? size * 0.25 : size * 0.2,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          shadows: [
                            const Shadow(
                              color: Color.fromRGBO(0, 0, 0, 0.5),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}

class HeartPainter extends CustomPainter {
  final double fillProgress;
  final bool isReady;
  final List<Color> colors;

  HeartPainter({
    required this.fillProgress,
    required this.isReady,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    final path = _createHeartPath(size);

    paint.style = PaintingStyle.stroke;
    paint.color = colors[0].withOpacity(0.3);
    paint.strokeWidth = 2;
    canvas.drawPath(path, paint);

    if (fillProgress > 0) {
      final gradient = LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: colors,
        stops: const [0.0, 1.0],
      );

      final rect = Rect.fromLTWH(0, 0, size.width, size.height);
      paint.shader = gradient.createShader(rect);
      paint.style = PaintingStyle.fill;

      canvas.save();
      final fillHeight = size.height * (1 - fillProgress);
      final clipRect = Rect.fromLTWH(
        0,
        fillHeight,
        size.width,
        size.height - fillHeight,
      );

      canvas.clipRect(clipRect);
      canvas.drawPath(path, paint);
      canvas.restore();
    }
  }

  Path _createHeartPath(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    path.moveTo(width * 0.5, height * 0.25);
    path.cubicTo(
      width * 0.2,
      height * 0.1,
      width * 0.1,
      height * 0.4,
      width * 0.2,
      height * 0.6,
    );
    path.cubicTo(
      width * 0.35,
      height * 0.8,
      width * 0.5,
      height * 0.9,
      width * 0.5,
      height * 0.9,
    );
    path.cubicTo(
      width * 0.5,
      height * 0.9,
      width * 0.65,
      height * 0.8,
      width * 0.8,
      height * 0.6,
    );
    path.cubicTo(
      width * 0.9,
      height * 0.4,
      width * 0.8,
      height * 0.1,
      width * 0.5,
      height * 0.25,
    );
    path.close();
    return path;
  }

  @override
  bool shouldRepaint(covariant HeartPainter oldDelegate) {
    return oldDelegate.fillProgress != fillProgress ||
        oldDelegate.isReady != isReady;
  }
}