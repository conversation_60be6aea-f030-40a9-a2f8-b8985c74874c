﻿using BloodPlus.Core.Base;
using BloodPlus.Core.Enum;
using BloodPlus.ModelViews.VoucherModelViews;

namespace BloodPlus.Services.Interfaces
{
    public interface IVoucherService
    {
        Task<string> CreateVoucherAsync(CreateVoucherModelView model, string userId);
        Task<string> DeleteVoucherAsync(string id, string userId);
        Task<VoucherModelView> GetVoucherByIdAsync(string id);
        Task<BasePaginatedList<ListVoucherForAdmin>> GetAllVoucherForAdminAsync(int pageNumber, int pageSize, VoucherType? type);
        Task<BasePaginatedList<ListVoucherForUser>> GetAllVoucherOfUserAsync(int pageNumber, int pageSize, VoucherType? type, string userId);
        Task<string> AddVoucherForUserAsync(string id, string userId);
        Task<BasePaginatedList<ListVoucherForUser>> GetAllVoucherForUserAsync(int pageNumber, int pageSize, VoucherType? type);
        Task<string> UpdateVoucherAsync(string id, UpdateVoucherModelView model, string userId);
        Task<DetailVoucherModel> GetDetailVoucherByIdAsync(string id);
    }
}
