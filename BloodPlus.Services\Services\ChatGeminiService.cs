﻿using Microsoft.Extensions.Configuration;
using System.Text;
using System.Text.Json;
using BloodPlus.Services.Interfaces;
using BloodPlus.ModelViews.ChatMessageModelView;
using BloodPlus.ModelViews.Gemini;

namespace BloodPlus.Services.Services
{
    public class ChatGeminiService : IChatGeminiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;

        public ChatGeminiService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient();
            _apiKey = configuration["Gemini:ApiKey"]
                      ?? throw new Exception("Missing Gemini:ApiKey in configuration");
        }

        // Cuộc trò chuyện MỚI: Có system prompt + thông tin user
        public async Task<string> GetCompletionAsync(string userPrompt, string userLocation, string userName, string bloodType)
        {
            var systemPrompt =
                "You are an intelligent, multilingual healthcare assistant embedded in a blood donation app. " +
                "You assist users by answering questions about health, blood types, donation eligibility, and nearby donation centers with distance information. " +
                "Ensure all answers are medically accurate, localized, respectful, and appropriate for the user's region. " +
                "If you're unsure about something, clearly explain your limitations. Never guess or hallucinate.";

            var preferredLanguage = DetectLanguageFromPrompt(userPrompt);
            var languageInstruction = !string.IsNullOrWhiteSpace(preferredLanguage)
                ? $"Please answer in {preferredLanguage}.\n"
                : string.Empty;

            var userContext = new StringBuilder();
            userContext.AppendLine($"User location: {userLocation}");
            userContext.AppendLine($"User name: {userName}");
            userContext.AppendLine($"User blood type: {bloodType}");
            userContext.AppendLine(languageInstruction);
            userContext.AppendLine($"User question: {userPrompt}");

            var contents = new List<object>
            {
                new 
                { 
                    parts = new[] { new { text = systemPrompt } },
                    role = "model"
                },
                new
                {
                    parts = new[] { new { text = userContext.ToString().Trim() } },
                    role = "user"
                }
            };

            return await CallGeminiAsync(contents);
        }

        // Cuộc trò chuyện TIẾP THEO: Dựa trên history + thông tin người dùng ở prompt cuối
        public async Task<string> GetCompletionWithHistoryAsync(string userPrompt,
                                                                List<ChatAIMessage> messages,
                                                                string location,
                                                                string userName,
                                                                string bloodType)
        {
            var contents = new List<object>();

            // 1. Xử lý các message cũ, ngoại trừ prompt cuối cùng nếu là từ user
            for (int i = 0; i < messages.Count; i++)
            {
                var msg = messages[i];
                var role = msg.Role.ToLower() switch
                {
                    "user" => "user",
                    "ai" => "model",
                    _ => throw new Exception($"Invalid role: {msg.Role}. Only 'user' or 'ai' are allowed.")
                };

                // Nếu là tin nhắn cuối cùng và từ user, bỏ qua (vì sẽ thay bằng enrichedPrompt)
                if (i == messages.Count - 1 && role == "user")
                    continue;

                contents.Add(new
                {
                    parts = new[] { new { text = msg.Content.Trim() } },
                    role = role
                });
            }
            var preferredLanguage = DetectLanguageFromPrompt(userPrompt);
            var enriched = new StringBuilder()
                .AppendLine($"[User: Name={userName}, Blood={bloodType}, Location={location}]")
                .AppendLine($"[Q: {userPrompt.Trim()}]")
                .AppendLine("→ No need to greet or repeat user information.")
                .AppendLine($"Please answer in {preferredLanguage}");

            contents.Add(new
            {
                parts = new[] { new { text = enriched.ToString().Trim() } },
                role = "user"
            });

            // 3. Gửi đến Gemini API
            return await CallGeminiAsync(contents);
        }

        // Hàm dùng chung gọi Gemini API
        private async Task<string> CallGeminiAsync(List<object> contents)
        {
            var payload = new
            {
                contents = contents,
                generationConfig = new
                {
                    maxOutputTokens = 1024,
                    temperature = 0.7
                }
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                $"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={_apiKey}",
                content);

            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception($"Gemini API error: {error}");
            }

            var result = await response.Content.ReadAsStringAsync();
            using var doc = JsonDocument.Parse(result);

            var firstText = doc.RootElement
                .GetProperty("candidates")[0]
                .GetProperty("content")
                .GetProperty("parts")[0]
                .GetProperty("text")
                .GetString();

            return firstText ?? "(No response from Gemini)";
        }

        private string? DetectLanguageFromPrompt(string prompt)
        {
            if (System.Text.RegularExpressions.Regex.IsMatch(prompt, "[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễ" +
                                                                     "ìíịỉĩòóọỏõôồốộổỗơờớợởỡ" +
                                                                     "ùúụủũưừứựửữỳýỵỷỹđ]"))
            {
                return "Vietnamese";
            }
            return null;
        }
    }

}
