﻿using BloodPlus.Core.Base;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using BloodPlus.Core.Enum;

namespace BloodPlus.Contract.Repositories.Entity
{
    public class Appointment : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        public string DonationEventId { get; set; }

        [ForeignKey("DonationEventId")]
        public virtual DonationEvent DonationEvent { get; set; }

        public string? UserName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Location { get; set; }
        public string? Email { get; set; }
        public string? BloodType { get; set; }
        public bool? IsEmergency { get; set; }
        public BloodComponent BloodComponent { get; set; }
        public AppointmentStatus Status { get; set; }
        public string? Certification { get; set; }
        public virtual ICollection<RegistrationForm> RegistrationForms { get; set; }
    }
}
