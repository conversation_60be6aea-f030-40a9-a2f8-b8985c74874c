import 'package:bloodplusmobile/core/constants/app_theme.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:flutter/material.dart';
import 'package:bloodplusmobile/data/models/voucher_model.dart';
import 'package:bloodplusmobile/data/services/voucher_service.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/core/routes/navigation_service.dart';

class MyVouchersScreen extends StatefulWidget {
  const MyVouchersScreen({Key? key}) : super(key: key);

  @override
  _MyVouchersScreenState createState() => _MyVouchersScreenState();
}

class _MyVouchersScreenState extends State<MyVouchersScreen> {
  final VoucherService _voucherService = VoucherService();
  List<Voucher> _myVouchers = [];
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasNextPage = true;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadVouchers();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 && !_isLoading && _hasNextPage) {
      _loadVouchers();
    }
  }

  Future<void> _loadVouchers({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
      _myVouchers.clear();
      _hasNextPage = true;
    }
    if (!_hasNextPage || _isLoading) return;
    setState(() {
      _isLoading = true;
      if (isRefresh) _errorMessage = null;
    });
    try {
      final response = await _voucherService.getMyVouchers(pageNumber: _currentPage);
      setState(() {
        _myVouchers.addAll(response.items);
        _hasNextPage = response.hasNextPage;
        _currentPage++;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshVouchers() async {
    await _loadVouchers(isRefresh: true);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Modern App Bar with Gradient
          SliverAppBar(
            expandedHeight: isSmallScreen ? 100 : 120,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: Container(
              decoration: const BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: FlexibleSpaceBar(
                title: Text(
                  localizations.translate('my_vouchers_title') ?? 'My Vouchers',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 16 : 18,
                  ),
                ),
                centerTitle: true,
                titlePadding: EdgeInsets.only(bottom: isSmallScreen ? 12 : 16),
              ),
            ),
            leading: Container(
              margin: EdgeInsets.all(isSmallScreen ? 6 : 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 20),
                onPressed: () => NavigationService.navigateToHome(),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                isSmallScreen ? 12 : 16,
                isSmallScreen ? 16 : 24,
                isSmallScreen ? 12 : 16,
                isSmallScreen ? 12 : 16,
              ),
              child: RefreshIndicator(
                onRefresh: _refreshVouchers,
                color: AppTheme.primaryRed,
                child: SizedBox(
                  height: MediaQuery.of(context).size.height - (isSmallScreen ? 160 : 200),
                  child: _buildVoucherSliverList(localizations),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoucherSliverList(AppLocalizations localizations) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    if (_isLoading && _myVouchers.isEmpty) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
          strokeWidth: isSmallScreen ? 2.5 : 3,
        ),
      );
    }
    if (_errorMessage != null) {
      return _buildErrorState(localizations, _errorMessage!, isSmallScreen);
    }
    if (_myVouchers.isEmpty) {
      return _buildEmptyState(localizations, isSmallScreen);
    }
    return ListView.builder(
      controller: _scrollController,
      itemCount: _myVouchers.length + (_hasNextPage ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _myVouchers.length) {
          return Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16.0),
            child: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
              ),
            ),
          );
        }
        final voucher = _myVouchers[index];
        return MyVoucherCard(
          voucher: voucher,
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRoutes.userVoucherDetail,
              arguments: {'voucher': voucher},
            );
          },
          isSmallScreen: isSmallScreen,
        );
      },
    );
  }

  Widget _buildErrorState(AppLocalizations localizations, String error, bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 16 : 32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
            decoration: BoxDecoration(
              color: Colors.red[50],
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.error_outline,
              color: AppTheme.primaryRed,
              size: 48,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            localizations.translate('voucher_error_title') ?? 'Error',
            style: AppTheme.headingSmall.copyWith(fontSize: isSmallScreen ? 14 : 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            localizations.translate('voucher_error_message') ?? 'Cannot load voucher list',
            style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 12 : 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _loadVouchers(isRefresh: true),
            style: AppTheme.primaryButtonStyle.copyWith(
              padding: MaterialStateProperty.all(EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 16 : 24,
                vertical: isSmallScreen ? 8 : 12,
              )),
            ),
            icon: const Icon(Icons.refresh, size: 20),
            label: Text(
              localizations.translate('reload') ?? 'Reload',
              style: AppTheme.buttonText.copyWith(fontSize: isSmallScreen ? 14 : 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations, bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 16 : 32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 16 : 32),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryRed.withOpacity(0.1),
                  AppTheme.secondaryRed.withOpacity(0.05),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.card_giftcard,
              color: AppTheme.primaryRed,
              size: 64,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            localizations.translate('no_vouchers') ?? 'No vouchers',
            style: AppTheme.headingSmall.copyWith(fontSize: isSmallScreen ? 14 : 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            localizations.translate('no_vouchers_exchanged') ??
                'You have not exchanged any vouchers yet. Accumulate points and exchange for attractive vouchers!',
            style: AppTheme.bodyMedium.copyWith(fontSize: isSmallScreen ? 12 : 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class MyVoucherCard extends StatelessWidget {
  final Voucher voucher;
  final VoidCallback onTap;
  final bool isSmallScreen;

  const MyVoucherCard({
    Key? key,
    required this.voucher,
    required this.onTap,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isExpiringSoon = voucher.expiryDate.difference(DateTime.now()).inDays <= 7;
    final localizations = AppLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
          onTap: onTap,
          child: Container(
            decoration: AppTheme.elevatedCardDecoration.copyWith(
              borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image Section with Overlay
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(isSmallScreen ? 16 : 20),
                    topRight: Radius.circular(isSmallScreen ? 16 : 20),
                  ),
                  child: Stack(
                    children: [
                      if (voucher.panel != null && voucher.panel!.isNotEmpty)
                        Image.network(
                          voucher.panel!,
                          width: double.infinity,
                          height: isSmallScreen ? 120 : 160,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => _buildImageError(isSmallScreen),
                        )
                      else
                        _buildImageError(isSmallScreen),

                      // Gradient Overlay
                      Container(
                        height: isSmallScreen ? 120 : 160,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.3),
                            ],
                          ),
                        ),
                      ),

                      // Expiry Warning Badge
                      if (isExpiringSoon)
                        Positioned(
                          top: isSmallScreen ? 8 : 12,
                          right: isSmallScreen ? 8 : 12,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 6 : 8,
                              vertical: isSmallScreen ? 2 : 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange[600],
                              borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.access_time, color: Colors.white, size: 12),
                                const SizedBox(width: 4),
                                Text(
                                  localizations.translate('expiring_soon') ?? 'Sắp hết hạn',
                                  style: AppTheme.captionText.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: isSmallScreen ? 10 : 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Content Section
                Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Sponsor Info
                      Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: isSmallScreen ? 1.5 : 2,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: isSmallScreen ? 6 : 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: CircleAvatar(
                              backgroundImage: NetworkImage(voucher.sponsorImage),
                              radius: isSmallScreen ? 16 : 20,
                              backgroundColor: Colors.grey[200],
                            ),
                          ),
                          SizedBox(width: isSmallScreen ? 8 : 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  voucher.sponsorName,
                                  style: AppTheme.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimary,
                                    fontSize: isSmallScreen ? 12 : 14,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  localizations.translate('official_partner') ?? 'Đối tác chính thức',
                                  style: AppTheme.captionText.copyWith(
                                    color: AppTheme.primaryRed,
                                    fontSize: isSmallScreen ? 10 : 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryRed.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                            ),
                            child: Icon(
                              Icons.arrow_forward_ios,
                              color: AppTheme.primaryRed,
                              size: isSmallScreen ? 14 : 16,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Voucher Name
                      Text(
                        voucher.voucherName,
                        style: AppTheme.headingSmall.copyWith(
                          fontSize: isSmallScreen ? 14 : 16,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 10),

                      // Expiry Date
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 10 : 12,
                          vertical: isSmallScreen ? 6 : 8,
                        ),
                        decoration: BoxDecoration(
                          color: isExpiringSoon
                              ? Colors.orange[50]
                              : AppTheme.primaryRed.withOpacity(0.08),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                          border: Border.all(
                            color: isExpiringSoon
                                ? Colors.orange[200]!
                                : AppTheme.primaryRed.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.schedule,
                              size: isSmallScreen ? 14 : 16,
                              color: isExpiringSoon ? Colors.orange[600] : AppTheme.primaryRed,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'HSD: ${DateFormat('dd/MM/yyyy').format(voucher.expiryDate)}',
                              style: AppTheme.bodySmall.copyWith(
                                color: isExpiringSoon ? Colors.orange[700] : AppTheme.primaryRed,
                                fontWeight: FontWeight.w600,
                                fontSize: isSmallScreen ? 10 : 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageError(bool isSmallScreen) {
    return Container(
      height: isSmallScreen ? 120 : 160,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryRed.withOpacity(0.1),
            AppTheme.secondaryRed.withOpacity(0.05),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.card_giftcard,
              color: AppTheme.primaryRed,
              size: isSmallScreen ? 30 : 40,
            ),
            SizedBox(height: isSmallScreen ? 6 : 8),
            Text(
              'Voucher Image',
              style: TextStyle(
                color: AppTheme.textSecondary,
                fontSize: isSmallScreen ? 10 : 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}