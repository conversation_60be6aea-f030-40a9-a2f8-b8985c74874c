import 'dart:math' as math;
import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/language_helper/localization.dart';
import 'package:bloodplusmobile/core/widgets/system/splash_drop_animation.dart';
import 'package:bloodplusmobile/core/widgets/system/splash_painter.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/presentation/features/home/<USER>';
import 'package:bloodplusmobile/presentation/features/onboarding/started_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bloodplusmobile/presentation/features/auth/login_screen.dart';
import 'package:bloodplusmobile/core/routes/app_routes.dart';
import 'dart:convert';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late AnimationController _splashController;
  late AnimationController _logoController;
  late Animation<double> _splashAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;

  bool _showSplashEffect = false;

  @override
  void initState() {
    super.initState();
    // XÓA _checkSessionTimeoutAndStart và gọi _startAnimation trực tiếp trong initState
    _startAnimation();

    final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
    appStateNotifier.setLoggedIn(false);
    // Controller for splash effect
    _splashController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Controller for logo animation
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000), // Adjusted for slower, smoother rotation
      vsync: this,
    );

    // Splash effect animation
    _splashAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _splashController,
        curve: Curves.easeOut,
      ),
    );

    // Logo rotation animation - Full 360-degree rotation
    _logoRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi, // Full circle (360 degrees)
    ).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: Curves.easeInOutQuad, // Smoother curve
      ),
    );

    // Logo scale animation
    _logoScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: Curves.elasticOut,
      ),
    );

    // Logo opacity animation
    _logoOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn), // Fade in during first half
      ),
    );

    _startAnimation();
  }

  void _startAnimation() async {
    // Wait for blood drops to complete (3 seconds)
    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      setState(() {
        _showSplashEffect = true;
      });

      // Start splash effect
      await _splashController.forward();

      if (mounted) {
        // Start logo animation
        await _logoController.forward();

        // Wait a bit then navigate
        await Future.delayed(const Duration(milliseconds: 700));

        if (mounted) {
          _navigateToNextScreen();
        }
      }
    }
  }

  void _navigateToNextScreen() async {
    final prefs = await SharedPreferences.getInstance();
    final onboardingCompleted = prefs.getBool('onboarding_completed') ?? false;
    final userManager = UserManager();
    final token = await userManager.getUserToken();
    String? username = await userManager.getUserEmail();

    bool isTokenExpired(String? token) {
      if (token == null || token.isEmpty) return true;
      try {
        final parts = token.split('.');
        if (parts.length != 3) return true;
        final payload = parts[1];
        final normalized = base64Url.normalize(payload);
        final payloadMap = json.decode(utf8.decode(base64Url.decode(normalized)));
        if (payloadMap is Map<String, dynamic> && payloadMap.containsKey('exp')) {
          final exp = payloadMap['exp'];
          final expTime = exp is int ? exp : int.tryParse(exp.toString());
          if (expTime == null) return true;
          final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
          return now >= expTime;
        }
        return true;
      } catch (_) {
        return true;
      }
    }

    Widget nextScreen;
    if (!onboardingCompleted) {
      nextScreen = const StartedScreen();
    } else if (token == null || token.isEmpty || isTokenExpired(token)) {
      // Nếu token hết hạn, lưu lại username/email để tự động điền lại
      if (username != null && username.isNotEmpty) {
        await prefs.setString('last_login_username', username);
      }
      nextScreen = const LoginScreen();
    } else {
      nextScreen = const HomeScreen();
    }

    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => nextScreen,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 1000),
        ),
      );
    }
  }

  @override
  void dispose() {
    _splashController.dispose();
    _logoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.primaryRed,
      body: Stack(
        children: [
          // Blood drops animation
          const BloodDropAnimation(),

          // Splash effect
          if (_showSplashEffect)
            AnimatedBuilder(
              animation: _splashAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: SplashPainter(_splashAnimation.value),
                  size: screenSize,
                );
              },
            ),

          // Logo animation
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedBuilder(
                  animation: Listenable.merge([
                    _logoRotationAnimation,
                    _logoScaleAnimation,
                    _logoOpacityAnimation,
                  ]),
                  builder: (context, child) {
                    final scaleValue = _logoScaleAnimation.value;
                    // ignore: avoid_print
                    return Opacity(
                      opacity: _logoOpacityAnimation.value,
                      child: Transform.scale(
                        scale: scaleValue.clamp(0.0, 1.0),
                        child: Transform.rotate(
                          angle: _logoRotationAnimation.value,
                          child: Hero(
                            tag: 'logo',
                            child: Image.asset(
                              'assets/icons/logo_white.png',
                              width: screenSize.width < 600 ? screenSize.width * 0.6 : 200,
                              height: screenSize.width < 600 ? screenSize.width * 0.6 : 200,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}