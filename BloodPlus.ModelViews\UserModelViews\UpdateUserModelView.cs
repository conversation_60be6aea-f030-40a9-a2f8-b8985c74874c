﻿using BloodPlus.Core.Enum;
using Microsoft.AspNetCore.Http;

namespace BloodPlus.ModelViews.UserModelViews
{
    public class UpdateUserModelView
    {
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? BloodType { get; set; }
        public IFormFile? UserImage { get; set; }
        public string? Name { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public ushort? DonationCount { get; set; }
        public string? Address { get; set; }
        public int? Point { get; set; }
        public string? Job { get; set; }
        public Gender? Gender { get; set; }
        public string? PassportNumber { get; set; }
    }
}
