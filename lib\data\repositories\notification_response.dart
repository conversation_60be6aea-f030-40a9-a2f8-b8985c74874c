import 'package:bloodplusmobile/data/models/notification_model.dart';

class NotificationListResponse {
  final List<NotificationModel> items;
  final int totalItems;
  final int currentPage;
  final int totalPages;
  final int pageSize;
  final bool hasPreviousPage;
  final bool hasNextPage;

  NotificationListResponse({
    required this.items,
    required this.totalItems,
    required this.currentPage,
    required this.totalPages,
    required this.pageSize,
    required this.hasPreviousPage,
    required this.hasNextPage,
  });

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) {
    final message = json['Message'] as Map<String, dynamic>? ?? {};
    final items = message['Items'] as List<dynamic>? ?? [];
    
    return NotificationListResponse(
      items: items.map((item) => NotificationModel.fromJson(item)).toList(),
      totalItems: message['TotalItems'] as int? ?? 0,
      currentPage: message['CurrentPage'] as int? ?? 1,
      totalPages: message['TotalPages'] as int? ?? 1,
      pageSize: message['PageSize'] as int? ?? 5,
      hasPreviousPage: message['HasPreviousPage'] as bool? ?? false,
      hasNextPage: message['HasNextPage'] as bool? ?? false,
    );
  }
}

class NotificationDetailResponse {
  final NotificationModel notification;

  NotificationDetailResponse({
    required this.notification,
  });

  factory NotificationDetailResponse.fromJson(Map<String, dynamic> json) {
    final message = json['Message'] as Map<String, dynamic>? ?? {};
    
    return NotificationDetailResponse(
      notification: NotificationModel.fromJson(message),
    );
  }
} 