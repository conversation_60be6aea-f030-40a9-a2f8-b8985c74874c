﻿using BloodPlus.Core.Enum;
using Microsoft.AspNetCore.Http;

namespace BloodPlus.ModelViews.VoucherModelViews
{
    public class CreateVoucherModelView
    {
        public string VoucherName { get; set; }
        public VoucherType Type { get; set; }
        public int Point { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string SponsorName { get; set; }          
        public IFormFile? SponsorImage { get; set; }         
        public string? Description { get; set; }         
        public string? UserManual { get; set; }           
        public string? Support { get; set; }           
        public IFormFile? Panel { get; set; }
        public List<IFormFile> Vouchers { get; set; }
    }
}
