﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BloodPlus.ModelViews.BlogModelViews
{
    public class BlogModelView
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        [Column(TypeName = "nvarchar(max)")]
        public string Content { get; set; }
        public string Author { get; set; }
        public int? ViewNumber { get; set; }
        public string? Image1 { get; set; }
        public string? Image2 { get; set; }
        public string? Image3 { get; set; }
        public string? Image4 { get; set; }
        public DateTimeOffset CreatedTime { get; set; }
    }
}
