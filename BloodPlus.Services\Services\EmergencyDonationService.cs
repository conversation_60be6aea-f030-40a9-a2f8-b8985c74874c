﻿
using AutoMapper;
using BloodPlus.Contract.Repositories.Entity;
using BloodPlus.Contract.Repositories.IUOW;
using BloodPlus.Core.Base;
using BloodPlus.Core.Enum;
using BloodPlus.Core.Utils;
using BloodPlus.ModelViews.DonationEventModelView;
using BloodPlus.ModelViews.EmergencyEventModelView;
using BloodPlus.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace BloodPlus.Services.Services
{
    public class EmergencyDonationService : IEmergencyDonationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly OneSignalService _oneSignalService;
        private readonly IGenericRepository<DonationEvent> _donationEvent;
        private readonly IGenericRepository<Notification> _notification;
        private readonly IGeolocationService _geolocationService;
        private readonly FirebaseService _firebaseService;
        public EmergencyDonationService(IUnitOfWork unitOfWork,OneSignalService oneSignalService, IGeolocationService geolocationService, FirebaseService firebaseService)
        {
            _unitOfWork = unitOfWork;
            _donationEvent = _unitOfWork.GetRepository<DonationEvent>();
            _oneSignalService = oneSignalService;
            _notification = _unitOfWork.GetRepository<Notification>();
            _geolocationService = geolocationService;
            _firebaseService = firebaseService;
        }

        public async Task<DonationEvent> CreateEmergencyEventAsync(DonationEmergencyEventCreateModelView model, string userId)
        {
            try
            {
                var eventCoords = await _geolocationService.GetCoordinatesFromAddressAsync(model.Location);
                if (eventCoords == null)
                    throw new Exception("Không thể lấy tọa độ từ địa chỉ sự kiện.");

                string? imageUrl = model.Image;
                if (model.imageFile != null)
                {
                    imageUrl = await _firebaseService.UploadImageAsync(model.imageFile);
                }

                var bloodType = await _unitOfWork.GetRepository<BloodType>().GetByIdAsync(model.RequiredBloodType);
                if (bloodType == null)
                    throw new Exception("BloodType can not found");

                var donationEmergency = new DonationEvent
                {
                    Id = Guid.NewGuid().ToString(),              
                    OrganizationId = model.OrganizationId,
                    Title = model.Title,
                    Description = model.Description,
                    Location = model.Location,
                    EventDate = model.EventDate,
                    EndTime = model.EndTime,
                    RequiredDonors = model.RequiredDonors,
                    CurrentDonors = 0,                            
                    Status = EventStatus.Pending,                 
                    Image = imageUrl,
                    IsEmergency = true,                           
                    RequiredBloodType = model.RequiredBloodType,
                };
                donationEmergency.CurrentDonors = 0;
                donationEmergency.CreatedTime = DateTime.Now;
                donationEmergency.CreatedBy = userId;
                await _donationEvent.InsertAsync(donationEmergency);

                var allUsers = await _unitOfWork.GetRepository<User>()
                                                .Entities
                                                .Where(u => !u.DeletedTime.HasValue && u.DeviceToken != null)
                                                .Include(u => u.BloodType)
                                                .ToListAsync();

                var eligibleUsers = allUsers
                            .Where(u => u.BloodType != null &&
                                        CanDonateTo(u.BloodType.BloodName, bloodType.BloodName))
                            .ToList();

                var pushTasks = new List<Task>();
                var notificationTasks = new List<Task>();

                foreach (var user in eligibleUsers)
                {
                    // 4. Kiểm tra xem user có gần địa chỉ sự kiện không
                    bool isNear = await _geolocationService.IsUserNearEventAsync(user.Address, model.Location, 50);
                    if (!isNear) continue;

                    // 5. Push notify
                    pushTasks.Add(_oneSignalService.SendNotificationAsync(
                        "Khẩn cấp: cần máu phù hợp!",
                        $"Sự kiện khẩn cấp gần bạn cần máu nhóm {bloodType.BloodName} vào {model.EventDate:dd/MM/yyyy}.",
                        user.Id.ToString(),
                        user.DeviceToken!
                    ));

                    // 6. Lưu DB notification
                    var noti = new Notification
                    {
                        UserId = user.Id,
                        Title = "Khẩn cấp: cần máu phù hợp!",
                        Content = $"Sự kiện khẩn cấp gần bạn cần máu nhóm {bloodType.BloodName} vào ngày {model.EventDate:dd/MM/yyyy}.",
                        IsRead = false,
                        SentDate = DateTime.Now,
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now
                    };

                    notificationTasks.Add(_notification.InsertAsync(noti));
                }

                await Task.WhenAll(pushTasks.Concat(notificationTasks));
                await _unitOfWork.SaveAsync();

                return donationEmergency;
            }
                catch(Exception ex)
            {
                throw new Exception(ex.InnerException?.Message ?? ex.Message, ex);
            }
        }

        private readonly Dictionary<string, List<string>> CompatibleRecipients = new()
        {
            { "O-", new List<string> { "O-" } },
            { "O+", new List<string> { "O-", "O+" } },
            { "A-", new List<string> { "O-", "A-" } },
            { "A+", new List<string> { "O-", "O+", "A-", "A+" } },
            { "B-", new List<string> { "O-", "B-" } },
            { "B+", new List<string> { "O-", "O+", "B-", "B+" } },
            { "AB-", new List<string> { "O-", "A-", "B-", "AB-" } },
            { "AB+", new List<string> { "O-", "O+", "A-", "A+", "B-", "B+", "AB-", "AB+" } }
        };



        private bool CanDonateTo(string userBlood, string requestBlood)
        {
            return CompatibleRecipients.ContainsKey(requestBlood)
                   && CompatibleRecipients[requestBlood].Contains(userBlood);
        }

        public async Task<BasePaginatedList<EDModelView>> GetAllEmergencyEventAsync(int pageNumber, int pageSize, string? userId, bool isUser)
        {
            var donationEventRepo = _unitOfWork.GetRepository<DonationEvent>();
            IQueryable<DonationEvent> donationEventQuery = donationEventRepo.Entities
                                    .Where(p => !p.DeletedTime.HasValue && p.IsEmergency);

            if (!string.IsNullOrWhiteSpace(userId))
            {
                if (isUser)
                {
                    donationEventQuery = donationEventQuery
                        .Where(p => p.CreatedBy == userId);
                }
                else
                {
                    donationEventQuery = donationEventQuery
                        .Where(p => p.CreatedBy != userId);
                }
            }

            donationEventQuery = donationEventQuery
                                        .OrderByDescending(s => s.CreatedTime);

            int totalCount = await donationEventQuery.CountAsync();

            var donationEvents = await donationEventQuery
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Include(p => p.Organization) 
                .ToListAsync();

            var result = new List<EDModelView>();
            foreach (var p in donationEvents)
            {
                var bloodType = await _unitOfWork.GetRepository<BloodType>()
                    .Entities
                    .Where(b => b.Id == p.RequiredBloodType)
                    .FirstOrDefaultAsync();

                result.Add(new EDModelView
                {
                    Id = p.Id,
                    Title = p.Title,
                    OrganizationName = p.Organization?.Name ?? "N/A",
                    Location = p.Location,
                    EventDate = p.EventDate,
                    RequiredBloodTypeName = bloodType?.BloodName ?? "Không rõ",
                    RequiredDonors = p.RequiredDonors,
                    CurrentDonors = p.CurrentDonors,
                    ImageUrl = p.Image,
                });
            }

            return new BasePaginatedList<EDModelView>(result, totalCount, pageNumber, pageSize);
        }

        public async Task<EmergencyEventDetailModelView> GetEmergencyEventDetailAsync(string eventId)
        {
            var donationEvent = await _unitOfWork.GetRepository<DonationEvent>()
                .Entities
                .Include(e => e.Organization)
                .FirstOrDefaultAsync(e => e.Id == eventId && !e.DeletedTime.HasValue);

            if (donationEvent == null)
                throw new Exception("Không tìm thấy sự kiện hiến máu.");

            var appointments = await _unitOfWork.GetRepository<Appointment>()
                .Entities
                .Include(a => a.User)
                .Where(a => a.DonationEventId == eventId && !a.DeletedTime.HasValue && a.Status == AppointmentStatus.Pending)
                .ToListAsync();

            var appointmentsBloodTypeIds = appointments.Select(a => a.BloodType)
                                                        .Where(id => id != null)
                                                        .Append(donationEvent.RequiredBloodType) 
                                                        .Where(id => id != null)
                                                        .Distinct()
                                                        .ToList();

            var bloodTypeDict = await _unitOfWork.GetRepository<BloodType>()
                .Entities
                .Where(b => appointmentsBloodTypeIds.Contains(b.Id))
                .ToDictionaryAsync(b => b.Id, b => b.BloodName);

            var participantList = appointments.Select(a => new EmergencyEventParticipantModelView
            {
                UserName = a.User.UserName,
                PhoneNumber = a.User.PhoneNumber,
                Email = a.User.Email,
                Address = a.User.Address,
                BloodType = a.BloodType != null && bloodTypeDict.ContainsKey(a.BloodType)
                                                ? bloodTypeDict[a.BloodType]
                                                : "Không rõ",
                BloodComponent = a.BloodComponent,
                Status = a.Status
            }).ToList();

            return new EmergencyEventDetailModelView
            {
                Id = donationEvent.Id,
                Title = donationEvent.Title,
                Description = donationEvent.Description ?? string.Empty,
                Location = donationEvent.Location,
                EventDate = donationEvent.EventDate,
                EndTime = donationEvent.EndTime,
                RequiredDonors = donationEvent.RequiredDonors,
                CurrentDonors = donationEvent.CurrentDonors,
                Image = donationEvent.Image,
                IsEmergency = donationEvent.IsEmergency,
                RequiredBloodType = donationEvent.RequiredBloodType != null && bloodTypeDict.ContainsKey(donationEvent.RequiredBloodType)
                            ? bloodTypeDict[donationEvent.RequiredBloodType]
                            : "Không rõ",
                OrganizationName = donationEvent.Organization.Name,
                Participants = participantList
            };
        }
    }
}
